#!/usr/bin/env python3
"""
验证Binance API密钥
测试API连接和权限
"""
import sys
import os
import asyncio
import aiohttp
import hmac
import hashlib
import time
from urllib.parse import urlencode
from loguru import logger

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils import load_config


class BinanceAPIVerifier:
    """Binance API验证器"""
    
    def __init__(self, api_key: str, api_secret: str, testnet: bool = False):
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = "https://testnet.binance.vision" if testnet else "https://api.binance.com"
        
    def _generate_signature(self, query_string: str) -> str:
        """生成API签名"""
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
    async def test_connectivity(self) -> bool:
        """测试API连接"""
        try:
            logger.info("🔗 测试Binance API连接...")
            
            url = f"{self.base_url}/api/v3/ping"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        logger.info("✅ Binance API连接正常")
                        return True
                    else:
                        logger.error(f"❌ Binance API连接失败: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ API连接测试异常: {e}")
            return False
            
    async def test_server_time(self) -> bool:
        """测试服务器时间"""
        try:
            logger.info("⏰ 测试服务器时间同步...")
            
            url = f"{self.base_url}/api/v3/time"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        server_time = data['serverTime']
                        local_time = int(time.time() * 1000)
                        time_diff = abs(server_time - local_time)
                        
                        logger.info(f"✅ 服务器时间: {server_time}")
                        logger.info(f"✅ 本地时间: {local_time}")
                        logger.info(f"✅ 时间差: {time_diff}ms")
                        
                        if time_diff > 5000:  # 5秒
                            logger.warning("⚠️ 时间差过大，可能影响API调用")
                            
                        return True
                    else:
                        logger.error(f"❌ 获取服务器时间失败: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ 服务器时间测试异常: {e}")
            return False
            
    async def test_api_key_permissions(self) -> bool:
        """测试API密钥权限"""
        try:
            logger.info("🔑 测试API密钥权限...")
            
            # 构建请求参数
            timestamp = int(time.time() * 1000)
            params = {
                'timestamp': timestamp
            }
            
            query_string = urlencode(params)
            signature = self._generate_signature(query_string)
            
            url = f"{self.base_url}/api/v3/account"
            headers = {
                'X-MBX-APIKEY': self.api_key
            }
            
            full_url = f"{url}?{query_string}&signature={signature}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(full_url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        logger.info("✅ API密钥验证成功")
                        logger.info(f"✅ 账户类型: {data.get('accountType', 'Unknown')}")
                        logger.info(f"✅ 交易权限: {'是' if data.get('canTrade', False) else '否'}")
                        logger.info(f"✅ 提现权限: {'是' if data.get('canWithdraw', False) else '否'}")
                        logger.info(f"✅ 充值权限: {'是' if data.get('canDeposit', False) else '否'}")
                        
                        # 显示余额信息
                        balances = data.get('balances', [])
                        non_zero_balances = [b for b in balances if float(b['free']) > 0 or float(b['locked']) > 0]
                        
                        if non_zero_balances:
                            logger.info("💰 账户余额:")
                            for balance in non_zero_balances[:5]:  # 只显示前5个
                                asset = balance['asset']
                                free = float(balance['free'])
                                locked = float(balance['locked'])
                                if free > 0 or locked > 0:
                                    logger.info(f"  {asset}: 可用 {free}, 冻结 {locked}")
                        else:
                            logger.info("💰 账户余额: 无余额或全部为0")
                            
                        return True
                        
                    elif response.status == 401:
                        logger.error("❌ API密钥无效或签名错误")
                        return False
                    elif response.status == 403:
                        logger.error("❌ API密钥权限不足")
                        return False
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ API权限测试失败: {response.status} - {error_text}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ API权限测试异常: {e}")
            return False
            
    async def test_trading_permissions(self) -> bool:
        """测试交易权限（不实际下单）"""
        try:
            logger.info("📊 测试交易权限...")
            
            # 获取交易规则
            url = f"{self.base_url}/api/v3/exchangeInfo"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # 检查BTCUSDT交易对
                        symbols = data.get('symbols', [])
                        btc_symbol = None
                        
                        for symbol in symbols:
                            if symbol['symbol'] == 'BTCUSDT':
                                btc_symbol = symbol
                                break
                                
                        if btc_symbol:
                            logger.info("✅ 找到BTCUSDT交易对")
                            logger.info(f"✅ 交易状态: {btc_symbol['status']}")
                            logger.info(f"✅ 现货交易: {'支持' if 'SPOT' in btc_symbol.get('permissions', []) else '不支持'}")
                            logger.info(f"✅ 杠杆交易: {'支持' if 'MARGIN' in btc_symbol.get('permissions', []) else '不支持'}")
                            return True
                        else:
                            logger.warning("⚠️ 未找到BTCUSDT交易对")
                            return False
                            
                    else:
                        logger.error(f"❌ 获取交易规则失败: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ 交易权限测试异常: {e}")
            return False
            
    async def run_full_verification(self) -> bool:
        """运行完整验证"""
        logger.info("🚀 开始Binance API完整验证")
        logger.info("=" * 60)
        
        tests = [
            ("API连接测试", self.test_connectivity),
            ("服务器时间测试", self.test_server_time),
            ("API密钥权限测试", self.test_api_key_permissions),
            ("交易权限测试", self.test_trading_permissions)
        ]
        
        results = []
        
        for test_name, test_func in tests:
            logger.info(f"\n🧪 {test_name}...")
            try:
                result = await test_func()
                results.append(result)
                if result:
                    logger.info(f"✅ {test_name} 通过")
                else:
                    logger.error(f"❌ {test_name} 失败")
            except Exception as e:
                logger.error(f"❌ {test_name} 异常: {e}")
                results.append(False)
                
        # 总结
        passed = sum(results)
        total = len(results)
        
        logger.info(f"\n📊 验证结果总结:")
        logger.info(f"✅ 通过: {passed}/{total}")
        logger.info(f"❌ 失败: {total - passed}/{total}")
        
        if passed == total:
            logger.info("🎉 所有测试通过！Binance API配置正确！")
            return True
        else:
            logger.error("⚠️ 部分测试失败，请检查API配置")
            return False


async def main():
    """主函数"""
    try:
        # 加载配置
        config = load_config()
        
        api_key = config['trading']['binance']['api_key']
        api_secret = config['trading']['binance']['api_secret']
        testnet = config['trading']['binance']['testnet']
        
        if not api_key or not api_secret:
            logger.error("❌ 未找到Binance API配置")
            return False
            
        logger.info(f"🔧 配置信息:")
        logger.info(f"  API Key: {api_key[:10]}...{api_key[-10:]}")
        logger.info(f"  Secret: {api_secret[:10]}...{api_secret[-10:]}")
        logger.info(f"  测试网: {'是' if testnet else '否'}")
        
        # 创建验证器
        verifier = BinanceAPIVerifier(api_key, api_secret, testnet)
        
        # 运行验证
        success = await verifier.run_full_verification()
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 验证过程异常: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    if not success:
        sys.exit(1)

# 🚀 GoldBot快速部署指南 (5分钟上手)

## 📋 准备工作

### 1. 购买Ubuntu服务器
- **推荐配置**: 2核4G内存，40GB硬盘
- **系统版本**: Ubuntu 20.04 LTS 或 22.04 LTS
- **获取信息**: 服务器IP、用户名(root)、密码

### 2. 连接服务器
**Windows用户推荐使用PuTTY**:
1. 下载PuTTY: https://www.putty.org/
2. 输入服务器IP，端口22，点击Open
3. 输入用户名: `root`
4. 输入密码

---

## 🎯 一键部署 (推荐)

### 步骤1: 上传项目文件
使用WinSCP上传项目文件到服务器 `/opt/goldbot/` 目录:
1. 下载WinSCP: https://winscp.net/
2. 连接服务器，将本地goldbot文件夹内容拖拽到 `/opt/goldbot/`

### 步骤2: 运行一键部署脚本
```bash
# 进入项目目录
cd /opt/goldbot

# 给脚本执行权限
chmod +x deploy_ubuntu.sh

# 运行部署脚本
./deploy_ubuntu.sh
```

### 步骤3: 配置文件
```bash
# 编辑主配置文件
nano /opt/goldbot/config.yaml

# 重点检查:
# 1. Binance API密钥
# 2. Telegram手机号: +8613375386798
# 3. 飞书Webhook地址
```

### 步骤4: 启动服务
```bash
# 启动服务
systemctl start goldbot

# 查看状态
systemctl status goldbot

# 查看日志
journalctl -u goldbot -f
```

---

## 🔧 手动部署 (详细步骤)

### 1. 更新系统
```bash
apt update && apt upgrade -y
```

### 2. 安装基础工具
```bash
apt install -y curl wget git vim htop screen python3.9 python3.9-venv python3-pip
```

### 3. 创建项目目录
```bash
mkdir -p /opt/goldbot
cd /opt/goldbot
```

### 4. 上传项目文件
使用WinSCP或scp命令上传所有项目文件到 `/opt/goldbot/`

### 5. 创建虚拟环境
```bash
python3.9 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

### 6. 创建系统服务
```bash
cat > /etc/systemd/system/goldbot.service << 'EOF'
[Unit]
Description=GoldBot Trading System
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/goldbot
Environment=PATH=/opt/goldbot/venv/bin
ExecStart=/opt/goldbot/venv/bin/python /opt/goldbot/start_bot.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable goldbot
```

### 7. 配置防火墙
```bash
ufw allow 8080
ufw --force enable
```

---

## 📱 日常管理命令

### 服务管理
```bash
# 启动服务
systemctl start goldbot

# 停止服务
systemctl stop goldbot

# 重启服务
systemctl restart goldbot

# 查看状态
systemctl status goldbot

# 查看日志
journalctl -u goldbot -f
```

### 配置修改
```bash
# 修改配置文件
nano /opt/goldbot/config.yaml

# 修改环境变量
nano /opt/goldbot/.env

# 修改后重启服务
systemctl restart goldbot
```

### 系统监控
```bash
# 查看系统资源
htop

# 查看磁盘使用
df -h

# 查看内存使用
free -h

# 查看网络端口
netstat -tulpn | grep :8080
```

---

## 🌐 Web界面访问

访问地址: `http://你的服务器IP:8080`

如果无法访问:
1. 检查服务是否启动: `systemctl status goldbot`
2. 检查防火墙: `ufw status`
3. 检查云服务器安全组设置

---

## 📊 配置检查

### 当前配置状态
你的配置已经基本完成:
- ✅ **Binance API**: 已配置正式环境
- ✅ **Telegram监控**: 已配置频道和关键词
- ✅ **飞书通知**: 已配置Webhook
- ⚠️ **Telegram手机号**: 需要在config.yaml中确认

### 重要配置项
```yaml
# config.yaml 中需要确认的配置
trading:
  binance:
    testnet: false  # 确认是否使用正式环境
    
monitoring:
  telegram:
    phone: "+8613375386798"  # 确认手机号格式
```

---

## 🚨 紧急处理

### 如果出现问题
```bash
# 1. 立即停止服务
systemctl stop goldbot

# 2. 查看错误日志
journalctl -u goldbot -n 50

# 3. 检查配置文件
nano /opt/goldbot/config.yaml

# 4. 重新启动
systemctl start goldbot
```

### 备份重要数据
```bash
# 创建备份
mkdir -p /opt/backup_$(date +%Y%m%d)
cp /opt/goldbot/config.yaml /opt/backup_$(date +%Y%m%d)/
cp /opt/goldbot/.env /opt/backup_$(date +%Y%m%d)/
cp /opt/goldbot/*.json /opt/backup_$(date +%Y%m%d)/
```

---

## ✅ 部署检查清单

部署完成后检查以下项目:

- [ ] 服务器连接正常
- [ ] 项目文件上传完成
- [ ] Python环境安装成功
- [ ] 依赖包安装完成
- [ ] 配置文件设置正确
- [ ] 系统服务创建成功
- [ ] 防火墙配置完成
- [ ] 服务启动成功
- [ ] Web界面可以访问
- [ ] 日志输出正常

---

## 🎉 完成！

现在你的GoldBot交易系统已经在Ubuntu服务器上运行了！

**下一步**:
1. 通过Web界面监控系统状态
2. 查看日志确认监控功能正常
3. 根据需要调整交易参数
4. 定期备份重要配置和数据

**获取帮助**:
- 查看完整文档: `UBUNTU_DEPLOYMENT_GUIDE.md`
- 查看系统状态: `/opt/goldbot/status.sh`
- 查看实时日志: `/opt/goldbot/logs.sh`

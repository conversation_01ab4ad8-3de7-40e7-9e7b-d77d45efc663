#!/usr/bin/env python3
"""
运行时配置验证器
在系统运行时验证配置的实际可用性和有效性
"""
import asyncio
import aiohttp
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from loguru import logger
from binance.client import Client as BinanceClient
from binance.exceptions import BinanceAPIException


class RuntimeConfigValidator:
    """运行时配置验证器 - 验证配置的实际可用性"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.validation_results: Dict[str, Any] = {}
        self.last_validation_time: Optional[datetime] = None
        self.validation_interval = 300  # 5分钟验证一次
        
        logger.info("运行时配置验证器初始化完成")
    
    async def validate_all_runtime_configs(self) -> Dict[str, Any]:
        """验证所有运行时配置"""
        try:
            logger.info("🔍 开始运行时配置验证...")
            
            validation_results = {
                'timestamp': datetime.now().isoformat(),
                'overall_status': 'unknown',
                'validations': {}
            }
            
            # 1. 验证币安API配置
            binance_result = await self._validate_binance_api()
            validation_results['validations']['binance_api'] = binance_result
            
            # 2. 验证跟单交易配置
            copy_trading_result = await self._validate_copy_trading_config()
            validation_results['validations']['copy_trading'] = copy_trading_result
            
            # 3. 验证Telegram配置
            telegram_result = await self._validate_telegram_config()
            validation_results['validations']['telegram'] = telegram_result
            
            # 4. 验证飞书通知配置
            feishu_result = await self._validate_feishu_config()
            validation_results['validations']['feishu'] = feishu_result
            
            # 5. 验证网络连接
            network_result = await self._validate_network_connectivity()
            validation_results['validations']['network'] = network_result
            
            # 6. 验证端口可用性
            port_result = await self._validate_port_availability()
            validation_results['validations']['ports'] = port_result
            
            # 计算总体状态
            validation_results['overall_status'] = self._calculate_overall_status(validation_results['validations'])
            
            # 保存验证结果
            self.validation_results = validation_results
            self.last_validation_time = datetime.now()
            
            logger.info(f"✅ 运行时配置验证完成 - 总体状态: {validation_results['overall_status']}")
            return validation_results
            
        except Exception as e:
            logger.error(f"❌ 运行时配置验证异常: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'overall_status': 'error',
                'error': str(e),
                'validations': {}
            }
    
    async def _validate_binance_api(self) -> Dict[str, Any]:
        """验证币安API配置的实际可用性"""
        try:
            binance_config = self.config.get('trading', {}).get('binance', {})
            
            if not binance_config.get('enabled', False):
                return {
                    'status': 'disabled',
                    'message': '币安交易未启用',
                    'details': {}
                }
            
            api_key = binance_config.get('api_key')
            api_secret = binance_config.get('api_secret')
            testnet = binance_config.get('testnet', False)
            
            if not api_key or not api_secret:
                return {
                    'status': 'error',
                    'message': 'API密钥配置不完整',
                    'details': {
                        'api_key_configured': bool(api_key),
                        'api_secret_configured': bool(api_secret)
                    }
                }
            
            # 实际测试API连接
            try:
                client = BinanceClient(
                    api_key=api_key,
                    api_secret=api_secret,
                    testnet=testnet
                )
                
                # 测试连接
                start_time = time.time()
                ping_result = client.ping()
                ping_time = (time.time() - start_time) * 1000
                
                # 测试账户信息
                account_info = client.get_account()
                
                # 测试期货账户（如果可用）
                futures_account = None
                try:
                    futures_account = client.futures_account()
                except:
                    pass
                
                return {
                    'status': 'ok',
                    'message': 'API连接正常',
                    'details': {
                        'testnet': testnet,
                        'ping_time_ms': round(ping_time, 2),
                        'account_type': account_info.get('accountType', 'unknown'),
                        'can_trade': account_info.get('canTrade', False),
                        'can_withdraw': account_info.get('canWithdraw', False),
                        'can_deposit': account_info.get('canDeposit', False),
                        'futures_available': futures_account is not None,
                        'balances_count': len(account_info.get('balances', [])),
                        'permissions': account_info.get('permissions', [])
                    }
                }
                
            except BinanceAPIException as e:
                return {
                    'status': 'error',
                    'message': f'币安API错误: {e.message}',
                    'details': {
                        'error_code': e.code,
                        'error_message': e.message,
                        'testnet': testnet
                    }
                }
            except Exception as e:
                return {
                    'status': 'error',
                    'message': f'连接异常: {str(e)}',
                    'details': {
                        'testnet': testnet,
                        'error_type': type(e).__name__
                    }
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'message': f'验证异常: {str(e)}',
                'details': {}
            }
    
    async def _validate_copy_trading_config(self) -> Dict[str, Any]:
        """验证跟单交易配置"""
        try:
            copy_config = self.config.get('copy_trading', {})
            
            if not copy_config.get('enabled', False):
                return {
                    'status': 'disabled',
                    'message': '跟单交易未启用',
                    'details': {}
                }
            
            api_key = copy_config.get('api_key')
            api_secret = copy_config.get('api_secret')
            
            if not api_key or not api_secret:
                return {
                    'status': 'warning',
                    'message': '跟单API配置不完整',
                    'details': {
                        'api_key_configured': bool(api_key),
                        'api_secret_configured': bool(api_secret),
                        'suggestion': '请配置跟单专用API密钥'
                    }
                }
            
            # 验证跟单参数
            amount = copy_config.get('amount', 0)
            leverage = copy_config.get('leverage', 0)
            leader_config = copy_config.get('leader_config', {})
            
            issues = []
            if amount <= 0:
                issues.append('跟单金额必须大于0')
            if leverage < 1 or leverage > 20:
                issues.append('杠杆倍数应在1-20之间')
            
            min_follow = leader_config.get('min_follow_amount', 0)
            max_follow = leader_config.get('max_follow_amount', 0)
            if min_follow >= max_follow:
                issues.append('最小跟单金额应小于最大跟单金额')
            
            if issues:
                return {
                    'status': 'warning',
                    'message': '跟单参数配置有问题',
                    'details': {
                        'issues': issues,
                        'current_config': {
                            'amount': amount,
                            'leverage': leverage,
                            'min_follow_amount': min_follow,
                            'max_follow_amount': max_follow
                        }
                    }
                }
            
            return {
                'status': 'ok',
                'message': '跟单配置正常',
                'details': {
                    'amount': amount,
                    'leverage': leverage,
                    'leader_config': leader_config
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'验证异常: {str(e)}',
                'details': {}
            }
    
    async def _validate_telegram_config(self) -> Dict[str, Any]:
        """验证Telegram配置"""
        try:
            telegram_config = self.config.get('monitoring', {}).get('telegram', {})
            
            api_id = telegram_config.get('api_id')
            api_hash = telegram_config.get('api_hash')
            phone = telegram_config.get('phone')
            channels = telegram_config.get('channels', [])
            
            if not api_id or not api_hash:
                return {
                    'status': 'error',
                    'message': 'Telegram API配置不完整',
                    'details': {
                        'api_id_configured': bool(api_id),
                        'api_hash_configured': bool(api_hash),
                        'phone_configured': bool(phone)
                    }
                }
            
            # 检查会话文件
            import glob
            session_files = glob.glob("telegram_session*")
            
            return {
                'status': 'ok' if session_files else 'warning',
                'message': 'Telegram配置正常' if session_files else 'Telegram配置正常，但需要认证',
                'details': {
                    'api_configured': True,
                    'session_files': len(session_files),
                    'channels_count': len(channels),
                    'channels': channels,
                    'needs_auth': len(session_files) == 0
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'验证异常: {str(e)}',
                'details': {}
            }
    
    async def _validate_feishu_config(self) -> Dict[str, Any]:
        """验证飞书通知配置"""
        try:
            feishu_config = self.config.get('notifications', {}).get('feishu', {})
            
            if not feishu_config.get('enabled', False):
                return {
                    'status': 'disabled',
                    'message': '飞书通知未启用',
                    'details': {}
                }
            
            webhook_url = feishu_config.get('webhook_url')
            if not webhook_url:
                return {
                    'status': 'error',
                    'message': '飞书Webhook URL未配置',
                    'details': {}
                }
            
            # 测试Webhook连接
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                    test_payload = {
                        "msg_type": "text",
                        "content": {
                            "text": "配置验证测试消息"
                        }
                    }
                    
                    start_time = time.time()
                    async with session.post(webhook_url, json=test_payload) as response:
                        response_time = (time.time() - start_time) * 1000
                        
                        if response.status == 200:
                            result = await response.json()
                            return {
                                'status': 'ok',
                                'message': '飞书Webhook连接正常',
                                'details': {
                                    'response_time_ms': round(response_time, 2),
                                    'webhook_response': result
                                }
                            }
                        else:
                            return {
                                'status': 'error',
                                'message': f'飞书Webhook响应异常: {response.status}',
                                'details': {
                                    'status_code': response.status,
                                    'response_time_ms': round(response_time, 2)
                                }
                            }
            except Exception as e:
                return {
                    'status': 'error',
                    'message': f'飞书Webhook连接失败: {str(e)}',
                    'details': {
                        'error_type': type(e).__name__
                    }
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'message': f'验证异常: {str(e)}',
                'details': {}
            }

    async def _validate_network_connectivity(self) -> Dict[str, Any]:
        """验证网络连接"""
        try:
            test_urls = [
                ('binance_api', 'https://api.binance.com/api/v3/ping'),
                ('binance_futures', 'https://fapi.binance.com/fapi/v1/ping'),
                ('telegram_api', 'https://api.telegram.org'),
                ('google_dns', 'https://8.8.8.8')
            ]

            results = {}
            overall_status = 'ok'

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                for name, url in test_urls:
                    try:
                        start_time = time.time()
                        async with session.get(url) as response:
                            response_time = (time.time() - start_time) * 1000

                            results[name] = {
                                'status': 'ok' if response.status == 200 else 'warning',
                                'response_time_ms': round(response_time, 2),
                                'status_code': response.status
                            }

                            if response.status != 200:
                                overall_status = 'warning'

                    except Exception as e:
                        results[name] = {
                            'status': 'error',
                            'error': str(e),
                            'error_type': type(e).__name__
                        }
                        overall_status = 'error'

            return {
                'status': overall_status,
                'message': f'网络连接测试完成 - {len([r for r in results.values() if r["status"] == "ok"])}/{len(results)} 成功',
                'details': results
            }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'网络验证异常: {str(e)}',
                'details': {}
            }

    async def _validate_port_availability(self) -> Dict[str, Any]:
        """验证端口可用性"""
        try:
            import socket

            web_config = self.config.get('web', {})
            web_port = web_config.get('port', 8081)

            ports_to_check = [web_port]
            results = {}

            for port in ports_to_check:
                try:
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.settimeout(1)
                        result = s.connect_ex(('localhost', port))

                        if result == 0:
                            # 端口被占用
                            results[f'port_{port}'] = {
                                'status': 'warning',
                                'message': f'端口 {port} 被占用',
                                'port': port,
                                'in_use': True
                            }
                        else:
                            # 端口可用
                            results[f'port_{port}'] = {
                                'status': 'ok',
                                'message': f'端口 {port} 可用',
                                'port': port,
                                'in_use': False
                            }

                except Exception as e:
                    results[f'port_{port}'] = {
                        'status': 'error',
                        'message': f'端口 {port} 检查异常: {str(e)}',
                        'port': port,
                        'error': str(e)
                    }

            overall_status = 'ok'
            if any(r['status'] == 'error' for r in results.values()):
                overall_status = 'error'
            elif any(r['status'] == 'warning' for r in results.values()):
                overall_status = 'warning'

            return {
                'status': overall_status,
                'message': '端口可用性检查完成',
                'details': results
            }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'端口验证异常: {str(e)}',
                'details': {}
            }

    def _calculate_overall_status(self, validations: Dict[str, Any]) -> str:
        """计算总体状态"""
        try:
            statuses = [v.get('status', 'unknown') for v in validations.values()]

            if 'error' in statuses:
                return 'error'
            elif 'warning' in statuses:
                return 'warning'
            elif all(s in ['ok', 'disabled'] for s in statuses):
                return 'ok'
            else:
                return 'unknown'

        except Exception:
            return 'unknown'

    async def validate_specific_config(self, config_type: str) -> Dict[str, Any]:
        """验证特定类型的配置"""
        try:
            if config_type == 'binance':
                return await self._validate_binance_api()
            elif config_type == 'copy_trading':
                return await self._validate_copy_trading_config()
            elif config_type == 'telegram':
                return await self._validate_telegram_config()
            elif config_type == 'feishu':
                return await self._validate_feishu_config()
            elif config_type == 'network':
                return await self._validate_network_connectivity()
            elif config_type == 'ports':
                return await self._validate_port_availability()
            else:
                return {
                    'status': 'error',
                    'message': f'未知的配置类型: {config_type}',
                    'details': {}
                }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'验证异常: {str(e)}',
                'details': {}
            }

    def should_validate(self) -> bool:
        """判断是否需要进行验证"""
        if not self.last_validation_time:
            return True

        time_since_last = datetime.now() - self.last_validation_time
        return time_since_last.total_seconds() >= self.validation_interval

    def get_last_validation_results(self) -> Optional[Dict[str, Any]]:
        """获取最后一次验证结果"""
        return self.validation_results

    async def continuous_validation(self):
        """持续验证配置（后台任务）"""
        logger.info("🔄 启动持续配置验证...")

        while True:
            try:
                if self.should_validate():
                    await self.validate_all_runtime_configs()

                await asyncio.sleep(60)  # 每分钟检查一次

            except Exception as e:
                logger.error(f"持续验证异常: {e}")
                await asyncio.sleep(60)

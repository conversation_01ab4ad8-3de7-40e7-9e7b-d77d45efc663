"""
Web界面安全认证模块
"""
import hashlib
import hmac
import time
import secrets
from typing import Optional, Dict, Any
from fastapi import HTT<PERSON><PERSON>xception, Request, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from loguru import logger


class WebSecurityManager:
    """Web安全管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.security_config = config.get('web_security', {})
        
        # 安全配置
        self.enabled = self.security_config.get('enabled', True)
        self.api_key = self.security_config.get('api_key', '')
        self.api_secret = self.security_config.get('api_secret', '')
        self.token_expire_seconds = self.security_config.get('token_expire_seconds', 3600)
        
        # IP白名单
        self.ip_whitelist = set(self.security_config.get('ip_whitelist', ['127.0.0.1', 'localhost']))
        
        # 生成默认密钥（如果未配置）
        if not self.api_key or not self.api_secret:
            self._generate_default_keys()
        
        # 活跃令牌存储
        self.active_tokens: Dict[str, Dict[str, Any]] = {}
        
        logger.info(f"Web安全管理器初始化完成 - 启用: {self.enabled}")
        if self.enabled:
            logger.info(f"API Key: {self.api_key[:8]}...{self.api_key[-4:]}")
            logger.info(f"IP白名单: {list(self.ip_whitelist)}")
    
    def _generate_default_keys(self):
        """生成默认密钥"""
        if not self.api_key:
            self.api_key = secrets.token_urlsafe(32)
        if not self.api_secret:
            self.api_secret = secrets.token_urlsafe(64)
        
        logger.warning("⚠️ 使用自动生成的API密钥，建议在配置文件中设置固定密钥")
        logger.warning(f"临时API Key: {self.api_key}")
        logger.warning(f"临时API Secret: {self.api_secret[:16]}...")
    
    def check_ip_whitelist(self, client_ip: str) -> bool:
        """检查IP白名单"""
        if not self.enabled:
            return True
        
        # 检查是否在白名单中
        if client_ip in self.ip_whitelist:
            return True
        
        # 检查localhost的各种形式
        localhost_ips = {'127.0.0.1', '::1', 'localhost'}
        if client_ip in localhost_ips and 'localhost' in self.ip_whitelist:
            return True
        
        return False
    
    def generate_access_token(self, client_ip: str) -> str:
        """生成访问令牌"""
        timestamp = int(time.time())
        token_data = f"{self.api_key}:{client_ip}:{timestamp}"
        
        # 使用HMAC生成签名
        signature = hmac.new(
            self.api_secret.encode(),
            token_data.encode(),
            hashlib.sha256
        ).hexdigest()
        
        token = f"{self.api_key}.{timestamp}.{signature}"
        
        # 存储令牌信息
        self.active_tokens[token] = {
            'client_ip': client_ip,
            'created_at': timestamp,
            'expires_at': timestamp + self.token_expire_seconds
        }
        
        return token
    
    def verify_token(self, token: str, client_ip: str) -> bool:
        """验证访问令牌"""
        if not self.enabled:
            return True
        
        try:
            # 解析令牌
            parts = token.split('.')
            if len(parts) != 3:
                return False
            
            api_key, timestamp_str, signature = parts
            timestamp = int(timestamp_str)
            
            # 检查API Key
            if api_key != self.api_key:
                return False
            
            # 检查令牌是否过期
            current_time = int(time.time())
            if current_time > timestamp + self.token_expire_seconds:
                # 清理过期令牌
                if token in self.active_tokens:
                    del self.active_tokens[token]
                return False
            
            # 验证签名
            token_data = f"{api_key}:{client_ip}:{timestamp}"
            expected_signature = hmac.new(
                self.api_secret.encode(),
                token_data.encode(),
                hashlib.sha256
            ).hexdigest()
            
            if not hmac.compare_digest(signature, expected_signature):
                return False
            
            # 检查令牌是否在活跃列表中
            if token not in self.active_tokens:
                return False
            
            # 检查IP是否匹配
            token_info = self.active_tokens[token]
            if token_info['client_ip'] != client_ip:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"令牌验证异常: {e}")
            return False
    
    def revoke_token(self, token: str):
        """撤销令牌"""
        if token in self.active_tokens:
            del self.active_tokens[token]
    
    def cleanup_expired_tokens(self):
        """清理过期令牌"""
        current_time = int(time.time())
        expired_tokens = [
            token for token, info in self.active_tokens.items()
            if current_time > info['expires_at']
        ]
        
        for token in expired_tokens:
            del self.active_tokens[token]
        
        if expired_tokens:
            logger.debug(f"清理了 {len(expired_tokens)} 个过期令牌")
    
    def get_security_info(self) -> Dict[str, Any]:
        """获取安全信息"""
        self.cleanup_expired_tokens()
        
        return {
            'enabled': self.enabled,
            'ip_whitelist': list(self.ip_whitelist),
            'active_tokens': len(self.active_tokens),
            'token_expire_seconds': self.token_expire_seconds
        }


# FastAPI安全依赖
security = HTTPBearer(auto_error=False)

def get_security_manager() -> Optional[WebSecurityManager]:
    """获取安全管理器实例"""
    # 这里需要从全局配置中获取
    return None

async def verify_api_access(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> bool:
    """验证API访问权限"""
    # 获取安全管理器
    security_manager = get_security_manager()
    if not security_manager or not security_manager.enabled:
        return True
    
    # 获取客户端IP
    client_ip = request.client.host
    
    # 检查IP白名单
    if not security_manager.check_ip_whitelist(client_ip):
        logger.warning(f"IP {client_ip} 不在白名单中")
        raise HTTPException(status_code=403, detail="IP地址不在白名单中")
    
    # 检查令牌
    if not credentials or not credentials.credentials:
        raise HTTPException(status_code=401, detail="缺少访问令牌")
    
    token = credentials.credentials
    if not security_manager.verify_token(token, client_ip):
        logger.warning(f"无效的访问令牌: {token[:16]}... from {client_ip}")
        raise HTTPException(status_code=401, detail="无效的访问令牌")
    
    return True

async def verify_dangerous_operation(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> bool:
    """验证危险操作权限（更严格的验证）"""
    # 先进行基本验证
    await verify_api_access(request, credentials)
    
    # 危险操作的额外检查
    security_manager = get_security_manager()
    if security_manager and security_manager.enabled:
        client_ip = request.client.host
        
        # 记录危险操作
        logger.warning(f"危险操作请求: {request.method} {request.url.path} from {client_ip}")
        
        # 可以在这里添加额外的验证逻辑
        # 例如：二次确认、操作频率限制等
    
    return True

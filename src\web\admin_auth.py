"""
管理员认证模块
"""
import hashlib
import hmac
import time
import secrets
from typing import Optional, Dict, Any
from fastapi import HTTPException, Request, Depends
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from loguru import logger


class AdminAuthManager:
    """管理员认证管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.security_config = config.get('web_security', {})
        
        # 安全配置
        self.enabled = self.security_config.get('enabled', True)
        self.admin_password = self.security_config.get('admin_password', '')
        self.token_expire_hours = self.security_config.get('token_expire_hours', 24)
        self.ip_whitelist = set(self.security_config.get('admin_ip_whitelist', []))
        
        # 生成密钥用于令牌签名
        self.secret_key = secrets.token_urlsafe(32)
        
        # 活跃令牌存储
        self.active_tokens: Dict[str, Dict[str, Any]] = {}
        
        # 如果没有设置密码，生成一个临时密码
        if not self.admin_password:
            self.admin_password = secrets.token_urlsafe(16)
            logger.warning(f"⚠️ 未设置管理员密码，使用临时密码: {self.admin_password}")
        
        logger.info(f"管理员认证管理器初始化完成 - 启用: {self.enabled}")
        if self.ip_whitelist:
            logger.info(f"IP白名单: {list(self.ip_whitelist)}")
    
    def verify_password(self, password: str) -> bool:
        """验证管理员密码"""
        if not self.enabled:
            return True
        return password == self.admin_password
    
    def check_ip_whitelist(self, client_ip: str) -> bool:
        """检查IP白名单"""
        if not self.enabled or not self.ip_whitelist:
            return True
        
        # 检查是否在白名单中
        if client_ip in self.ip_whitelist:
            return True
        
        # 检查localhost的各种形式
        localhost_ips = {'127.0.0.1', '::1', 'localhost'}
        if client_ip in localhost_ips and ('127.0.0.1' in self.ip_whitelist or 'localhost' in self.ip_whitelist):
            return True
        
        return False
    
    def generate_token(self, client_ip: str) -> str:
        """生成访问令牌"""
        timestamp = int(time.time())
        expire_time = timestamp + (self.token_expire_hours * 3600)
        
        # 创建令牌数据
        token_data = f"admin:{client_ip}:{timestamp}:{expire_time}"
        
        # 使用HMAC生成签名
        signature = hmac.new(
            self.secret_key.encode(),
            token_data.encode(),
            hashlib.sha256
        ).hexdigest()
        
        token = f"{timestamp}.{expire_time}.{signature}"
        
        # 存储令牌信息
        self.active_tokens[token] = {
            'client_ip': client_ip,
            'created_at': timestamp,
            'expires_at': expire_time
        }
        
        # 清理过期令牌
        self._cleanup_expired_tokens()
        
        return token
    
    def verify_token(self, token: str, client_ip: str) -> bool:
        """验证访问令牌"""
        if not self.enabled:
            return True
        
        try:
            # 解析令牌
            parts = token.split('.')
            if len(parts) != 3:
                return False
            
            timestamp_str, expire_time_str, signature = parts
            timestamp = int(timestamp_str)
            expire_time = int(expire_time_str)
            
            # 检查令牌是否过期
            current_time = int(time.time())
            if current_time > expire_time:
                # 清理过期令牌
                if token in self.active_tokens:
                    del self.active_tokens[token]
                return False
            
            # 验证签名
            token_data = f"admin:{client_ip}:{timestamp}:{expire_time}"
            expected_signature = hmac.new(
                self.secret_key.encode(),
                token_data.encode(),
                hashlib.sha256
            ).hexdigest()
            
            if not hmac.compare_digest(signature, expected_signature):
                return False
            
            # 检查令牌是否在活跃列表中
            if token not in self.active_tokens:
                return False
            
            # 检查IP是否匹配
            token_info = self.active_tokens[token]
            if token_info['client_ip'] != client_ip:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"令牌验证异常: {e}")
            return False
    
    def revoke_token(self, token: str):
        """撤销令牌"""
        if token in self.active_tokens:
            del self.active_tokens[token]
            logger.info(f"令牌已撤销: {token[:16]}...")
    
    def _cleanup_expired_tokens(self):
        """清理过期令牌"""
        current_time = int(time.time())
        expired_tokens = [
            token for token, info in self.active_tokens.items()
            if current_time > info['expires_at']
        ]
        
        for token in expired_tokens:
            del self.active_tokens[token]
        
        if expired_tokens:
            logger.debug(f"清理了 {len(expired_tokens)} 个过期令牌")
    
    def get_auth_info(self) -> Dict[str, Any]:
        """获取认证信息"""
        self._cleanup_expired_tokens()
        
        return {
            'enabled': self.enabled,
            'ip_whitelist': list(self.ip_whitelist),
            'active_tokens': len(self.active_tokens),
            'token_expire_hours': self.token_expire_hours
        }


# 全局认证管理器实例
_auth_manager: Optional[AdminAuthManager] = None

def initialize_admin_auth(config: Dict[str, Any]):
    """初始化管理员认证"""
    global _auth_manager
    _auth_manager = AdminAuthManager(config)
    return _auth_manager

def get_auth_manager() -> Optional[AdminAuthManager]:
    """获取认证管理器实例"""
    return _auth_manager

# FastAPI安全依赖
security = HTTPBearer(auto_error=False)

async def verify_admin_token(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> str:
    """验证管理员令牌"""
    auth_manager = get_auth_manager()
    if not auth_manager or not auth_manager.enabled:
        return "admin"  # 如果未启用认证，直接通过
    
    # 获取客户端IP
    client_ip = request.client.host
    
    # 检查IP白名单
    if not auth_manager.check_ip_whitelist(client_ip):
        logger.warning(f"管理员访问被拒绝: IP {client_ip} 不在白名单中")
        raise HTTPException(status_code=403, detail="IP地址不在白名单中")
    
    # 检查令牌
    if not credentials or not credentials.credentials:
        raise HTTPException(status_code=401, detail="缺少访问令牌")
    
    token = credentials.credentials
    if not auth_manager.verify_token(token, client_ip):
        logger.warning(f"无效的管理员令牌: {token[:16]}... from {client_ip}")
        raise HTTPException(status_code=401, detail="无效的访问令牌")
    
    return token

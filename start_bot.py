#!/usr/bin/env python3
"""
智能交易机器人启动脚本
包含预检查和自动修复功能
"""
import os
import sys
import glob
import socket
import subprocess
import asyncio
from loguru import logger

def check_port(port):
    """检查端口是否被占用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex(('localhost', port))
            return result == 0
    except:
        return False

def fix_telegram_session():
    """修复Telegram会话文件"""
    try:
        session_files = glob.glob("telegram_session*")
        if session_files:
            logger.info(f"🔧 发现 {len(session_files)} 个会话文件，正在清理...")
            for session_file in session_files:
                os.remove(session_file)
                logger.info(f"✅ 已删除: {session_file}")
            return True
        return True
    except Exception as e:
        logger.error(f"❌ 清理会话文件失败: {e}")
        return False

def pre_check():
    """启动前检查"""
    logger.info("🔍 执行启动前检查...")

    # 1. 检查配置文件中的端口
    config_port = 8081  # 默认端口
    try:
        import yaml
        with open("config.yaml", 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            config_port = config.get('web', {}).get('port', 8081)
    except Exception as e:
        logger.warning(f"读取配置文件端口失败，使用默认端口8081: {e}")

    # 检查配置的端口是否被占用
    if check_port(config_port):
        logger.error(f"❌ 配置的端口 {config_port} 被占用！")
        logger.error(f"解决方案：")
        logger.error(f"1. 运行 'python check_ports.py' 查看并终止占用进程")
        logger.error(f"2. 或修改 config.yaml 中的 web.port 为其他端口")
        return False
    else:
        logger.info(f"✅ 端口 {config_port} 可用")

    # 2. 检查Telegram会话文件状态
    session_files = glob.glob("telegram_session*")
    if session_files:
        logger.info(f"✅ 发现Telegram会话文件: {session_files}")
        logger.info("📱 Telegram已认证，无需重新认证")
    else:
        logger.warning("⚠️ 未发现Telegram会话文件")
        logger.info("📝 首次运行时需要Telegram认证")

    # 3. 检查配置文件
    if not os.path.exists("config.yaml"):
        logger.error("❌ 配置文件 config.yaml 不存在")
        return False

    # 4. 检查日志目录
    os.makedirs("logs", exist_ok=True)

    logger.info("✅ 启动前检查完成")
    return True

def main():
    """主函数"""
    print("🚀 智能交易机器人启动器")
    print("=" * 50)
    
    # 执行预检查
    if not pre_check():
        print("❌ 预检查失败，请修复问题后重试")
        sys.exit(1)
    
    # 启动主程序
    try:
        logger.info("🚀 启动交易机器人主程序...")
        subprocess.run([sys.executable, "main.py"], check=True)
    except KeyboardInterrupt:
        logger.info("👋 用户中断程序")
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ 程序异常退出，返回码: {e.returncode}")
        sys.exit(e.returncode)
    except Exception as e:
        logger.error(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

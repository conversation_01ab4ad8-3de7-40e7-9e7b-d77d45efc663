#!/usr/bin/env python3
"""
运行时状态检查脚本
检查正在运行的系统的实时状态
"""
import asyncio
import aiohttp
import json
import time
from datetime import datetime
from loguru import logger

class RuntimeStatusChecker:
    """运行时状态检查器"""
    
    def __init__(self, web_port=8081):
        self.web_port = web_port
        self.base_url = f"http://localhost:{web_port}"
        
    async def check_system_status(self):
        """检查系统状态"""
        print("🔍 检查运行时系统状态")
        print("=" * 60)
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                # 1. 检查Web服务状态
                await self._check_web_service(session)
                
                # 2. 检查交易统计
                await self._check_trading_stats(session)
                
                # 3. 检查交易信号
                await self._check_trading_signals(session)
                
                # 4. 检查交易历史
                await self._check_trading_history(session)
                
                # 5. 检查系统健康状态
                await self._check_system_health(session)
                
        except Exception as e:
            print(f"❌ 系统状态检查失败: {e}")
            return False
        
        return True
    
    async def _check_web_service(self, session):
        """检查Web服务"""
        print("\n🌐 Web服务状态:")
        try:
            async with session.get(f"{self.base_url}/") as response:
                if response.status == 200:
                    print(f"  ✅ Web界面: 正常运行 (端口 {self.web_port})")
                else:
                    print(f"  ❌ Web界面: 状态码 {response.status}")
        except Exception as e:
            print(f"  ❌ Web界面: 连接失败 - {e}")
    
    async def _check_trading_stats(self, session):
        """检查交易统计"""
        print("\n📊 交易统计:")
        try:
            async with session.get(f"{self.base_url}/api/trading-stats") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"  ✅ 统计API: 正常")
                    print(f"  📈 总交易数: {data.get('total_trades', 0)}")
                    print(f"  💰 总盈亏: ${data.get('total_profit', 0):.2f}")
                    print(f"  📊 胜率: {data.get('win_rate', 0):.1f}%")
                    print(f"  🕐 最后更新: {data.get('last_updated', 'N/A')}")
                else:
                    print(f"  ❌ 统计API: 状态码 {response.status}")
        except Exception as e:
            print(f"  ❌ 统计API: 请求失败 - {e}")
    
    async def _check_trading_signals(self, session):
        """检查交易信号"""
        print("\n🔔 交易信号:")
        try:
            async with session.get(f"{self.base_url}/api/trading-signals?limit=5") as response:
                if response.status == 200:
                    data = await response.json()
                    signals = data.get('signals', [])
                    print(f"  ✅ 信号API: 正常")
                    print(f"  📡 信号总数: {data.get('total', 0)}")
                    
                    if signals:
                        print(f"  📋 最近5个信号:")
                        for i, signal in enumerate(signals[:5], 1):
                            timestamp = signal.get('timestamp', '')
                            symbol = signal.get('symbol', 'N/A')
                            source = signal.get('source', 'N/A')
                            print(f"    {i}. {symbol} ({source}) - {timestamp}")
                    else:
                        print(f"  📭 暂无信号记录")
                else:
                    print(f"  ❌ 信号API: 状态码 {response.status}")
        except Exception as e:
            print(f"  ❌ 信号API: 请求失败 - {e}")
    
    async def _check_trading_history(self, session):
        """检查交易历史"""
        print("\n📜 交易历史:")
        try:
            async with session.get(f"{self.base_url}/api/trading-history?limit=5") as response:
                if response.status == 200:
                    data = await response.json()
                    history = data.get('history', [])
                    print(f"  ✅ 历史API: 正常")
                    print(f"  📊 历史记录数: {data.get('total', 0)}")
                    
                    if history:
                        print(f"  📋 最近5笔交易:")
                        for i, trade in enumerate(history[:5], 1):
                            symbol = trade.get('symbol', 'N/A')
                            profit = trade.get('profit', 0)
                            timestamp = trade.get('timestamp', '')
                            status_icon = "📈" if profit > 0 else "📉" if profit < 0 else "➖"
                            print(f"    {i}. {status_icon} {symbol}: ${profit:+.2f} - {timestamp}")
                    else:
                        print(f"  📭 暂无交易历史")
                else:
                    print(f"  ❌ 历史API: 状态码 {response.status}")
        except Exception as e:
            print(f"  ❌ 历史API: 请求失败 - {e}")
    
    async def _check_system_health(self, session):
        """检查系统健康状态"""
        print("\n🏥 系统健康:")
        try:
            async with session.get(f"{self.base_url}/api/system-status") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"  ✅ 健康API: 正常")
                    
                    # 系统状态
                    system_status = data.get('status', 'unknown')
                    status_icon = "✅" if system_status == 'running' else "⚠️"
                    print(f"  {status_icon} 系统状态: {system_status}")
                    
                    # 组件状态
                    components = data.get('components', {})
                    if components:
                        print(f"  🔧 组件状态:")
                        for name, status in components.items():
                            icon = "✅" if status else "❌"
                            print(f"    {icon} {name}: {'正常' if status else '异常'}")
                    
                    # 错误率
                    error_rate = data.get('error_rate', 0)
                    error_icon = "✅" if error_rate < 10 else "⚠️" if error_rate < 50 else "❌"
                    print(f"  {error_icon} 错误率: {error_rate:.1f}%")
                    
                    # 运行时间
                    uptime = data.get('uptime_seconds', 0)
                    uptime_hours = uptime / 3600
                    print(f"  ⏱️ 运行时间: {uptime_hours:.1f} 小时")
                    
                else:
                    print(f"  ❌ 健康API: 状态码 {response.status}")
        except Exception as e:
            print(f"  ❌ 健康API: 请求失败 - {e}")

async def check_telegram_connection():
    """检查Telegram连接状态"""
    print("\n📱 Telegram连接状态:")
    
    # 检查会话文件
    import glob
    session_files = glob.glob("telegram_session*")
    if session_files:
        import os
        session_file = session_files[0]
        size = os.path.getsize(session_file)
        modified = datetime.fromtimestamp(os.path.getmtime(session_file))
        age_hours = (datetime.now() - modified).total_seconds() / 3600
        
        print(f"  ✅ 会话文件: {session_file}")
        print(f"  📁 文件大小: {size} bytes")
        print(f"  🕐 最后修改: {age_hours:.1f} 小时前")
        
        if age_hours > 24:
            print(f"  ⚠️ 会话文件较旧，可能需要重新认证")
    else:
        print(f"  ❌ 未找到会话文件，需要Telegram认证")

async def check_binance_connection():
    """检查Binance连接状态"""
    print("\n💰 Binance连接状态:")
    
    try:
        # 简单的ping测试
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
            start_time = time.time()
            async with session.get('https://fapi.binance.com/fapi/v1/ping') as response:
                response_time = (time.time() - start_time) * 1000
                
                if response.status == 200:
                    print(f"  ✅ Binance期货API: 连接正常")
                    print(f"  ⚡ 响应时间: {response_time:.1f}ms")
                else:
                    print(f"  ❌ Binance期货API: 状态码 {response.status}")
    except Exception as e:
        print(f"  ❌ Binance期货API: 连接失败 - {e}")

async def main():
    """主函数"""
    print("🚀 智能交易机器人 - 运行时状态检查")
    print(f"🕐 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查Web服务和API
    checker = RuntimeStatusChecker()
    web_ok = await checker.check_system_status()
    
    # 检查外部连接
    await check_telegram_connection()
    await check_binance_connection()
    
    # 总结
    print("\n" + "=" * 60)
    if web_ok:
        print("🎉 系统运行正常！")
        print(f"🌐 Web管理界面: http://localhost:8081")
    else:
        print("⚠️ 系统可能存在问题，请检查日志")
    
    print(f"📊 详细监控: http://localhost:8081/analysis")
    print(f"📈 交易历史: http://localhost:8081/trading-history")

if __name__ == "__main__":
    asyncio.run(main())

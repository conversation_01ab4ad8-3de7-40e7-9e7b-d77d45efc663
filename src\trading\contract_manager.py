"""
合约对管理系统
缓存和管理币安期货合约信息，避免频繁API调用
"""
import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass
from loguru import logger
import aiohttp

# 移除对BinanceFuturesTrader的依赖，直接使用Binance客户端


@dataclass
class ContractInfo:
    """合约信息"""
    symbol: str
    base_asset: str
    quote_asset: str
    status: str
    min_qty: float
    max_qty: float
    step_size: float
    tick_size: float
    min_notional: float
    leverage_brackets: List[Dict[str, Any]]
    last_updated: datetime


class ContractManager:
    """合约对管理器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.cache_file = "binance_contracts.json"
        self.cache_duration = 3600  # 1小时缓存

        # 合约缓存
        self.contracts: Dict[str, ContractInfo] = {}
        self.available_symbols: Set[str] = set()
        self.last_update: Optional[datetime] = None

        # 新币检查缓存 - 专门用于新币快速检查
        self.new_coin_cache: Dict[str, tuple] = {}  # symbol -> (is_available, check_time)
        self.new_coin_cache_duration = 60  # 新币缓存1分钟（新币状态变化快）

        # 负缓存 - 记录确认不存在的代币，避免重复检查
        self.negative_cache: Dict[str, datetime] = {}  # symbol -> last_check_time
        self.negative_cache_duration = 300  # 负缓存5分钟

        # 速度优化配置
        self.connection_pool = None  # HTTP连接池
        self.preconnect_enabled = True  # 预连接
        self.concurrent_checks = True   # 并发检查
        self.fast_fail_timeout = 3      # 快速失败超时(秒)

        # Binance客户端 (用于获取合约信息)
        self.binance_client = None

        logger.info("合约对管理器初始化完成 - 专注新币快速检查")

    async def initialize(self, binance_client=None):
        """初始化合约管理器"""
        try:
            self.binance_client = binance_client
            
            # 加载缓存的合约信息
            await self.load_contracts_cache()
            
            # 如果缓存过期或为空，更新合约信息
            if self.should_update_contracts():
                await self.update_contracts()
                
            logger.info(f"合约管理器初始化完成，共 {len(self.contracts)} 个合约")
            
        except Exception as e:
            logger.error(f"初始化合约管理器异常: {e}")
            
    def should_update_contracts(self) -> bool:
        """检查是否需要更新合约信息"""
        if not self.contracts or not self.last_update:
            return True
            
        # 检查缓存是否过期
        if (datetime.now() - self.last_update).total_seconds() > self.cache_duration:
            return True
            
        return False
        
    async def update_contracts(self):
        """更新合约信息"""
        try:
            logger.info("开始更新币安期货合约信息...")

            if not self.binance_client:
                logger.warning("Binance客户端未初始化，无法更新合约信息")
                return

            # 获取交易所信息
            exchange_info = self.binance_client.futures_exchange_info()
            if not exchange_info:
                logger.error("无法获取交易所信息")
                return
                
            # 解析合约信息
            new_contracts = {}
            new_symbols = set()
            
            for symbol_info in exchange_info.get('symbols', []):
                try:
                    symbol = symbol_info['symbol']

                    # 只处理USDT永续合约
                    if not symbol.endswith('USDT'):
                        continue

                    # 简化处理：只要是USDT交易对就接受
                    # 大部分USDT交易对都是永续合约

                    # 只处理活跃合约
                    if symbol_info['status'] != 'TRADING':
                        continue
                        
                    # 解析过滤器信息
                    filters = {f['filterType']: f for f in symbol_info.get('filters', [])}
                    
                    lot_size = filters.get('LOT_SIZE', {})
                    price_filter = filters.get('PRICE_FILTER', {})
                    min_notional = filters.get('MIN_NOTIONAL', {})
                    
                    # 获取杠杆信息
                    leverage_brackets = await self.get_leverage_brackets(symbol)
                    
                    contract = ContractInfo(
                        symbol=symbol,
                        base_asset=symbol_info.get('baseAsset', symbol.replace('USDT', '')),
                        quote_asset=symbol_info.get('quoteAsset', 'USDT'),
                        status=symbol_info.get('status', 'TRADING'),
                        min_qty=float(lot_size.get('minQty', 0.001)),
                        max_qty=float(lot_size.get('maxQty', 1000000)),
                        step_size=float(lot_size.get('stepSize', 0.001)),
                        tick_size=float(price_filter.get('tickSize', 0.01)),
                        min_notional=float(min_notional.get('notional', 5)),
                        leverage_brackets=leverage_brackets,
                        last_updated=datetime.now()
                    )
                    
                    new_contracts[symbol] = contract
                    new_symbols.add(symbol.replace('USDT', ''))  # 添加基础资产符号
                    
                except Exception as e:
                    symbol = symbol_info.get('symbol', 'unknown')
                    logger.debug(f"跳过合约 {symbol}: {e}")
                    # 只有在调试模式下才显示详细错误
                    if logger.level <= 10:  # DEBUG level
                        logger.debug(f"合约信息: {symbol_info}")
                    
            # 更新缓存
            self.contracts = new_contracts
            self.available_symbols = new_symbols
            self.last_update = datetime.now()
            
            # 保存到文件
            await self.save_contracts_cache()
            
            logger.info(f"合约信息更新完成，共 {len(new_contracts)} 个合约")
            
        except Exception as e:
            logger.error(f"更新合约信息异常: {e}")
            
    async def get_leverage_brackets(self, symbol: str) -> List[Dict[str, Any]]:
        """获取杠杆档位信息"""
        try:
            if not self.binance_client:
                return []
                
            # 这里应该调用币安API获取杠杆档位
            # 由于复杂性，返回默认档位
            return [
                {'bracket': 1, 'initialLeverage': 20, 'notionalCap': 50000, 'maintMarginRatio': 0.01},
                {'bracket': 2, 'initialLeverage': 10, 'notionalCap': 250000, 'maintMarginRatio': 0.025},
                {'bracket': 3, 'initialLeverage': 5, 'notionalCap': 1000000, 'maintMarginRatio': 0.05}
            ]
            
        except Exception as e:
            logger.error(f"获取杠杆档位异常: {symbol} - {e}")
            return []
            
    async def load_contracts_cache(self):
        """加载缓存的合约信息"""
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
                
            # 检查缓存时间
            last_update_str = cache_data.get('last_update')
            if last_update_str:
                self.last_update = datetime.fromisoformat(last_update_str)
                
                # 检查是否过期
                if (datetime.now() - self.last_update).total_seconds() > self.cache_duration:
                    logger.info("合约缓存已过期")
                    return
                    
            # 加载合约数据
            contracts_data = cache_data.get('contracts', {})
            symbols_data = cache_data.get('available_symbols', [])
            
            # 重建合约对象
            for symbol, contract_data in contracts_data.items():
                contract_data['last_updated'] = datetime.fromisoformat(contract_data['last_updated'])
                self.contracts[symbol] = ContractInfo(**contract_data)
                
            self.available_symbols = set(symbols_data)
            
            logger.info(f"加载缓存合约信息: {len(self.contracts)} 个合约")
            
        except FileNotFoundError:
            logger.info("未找到合约缓存文件")
        except Exception as e:
            logger.error(f"加载合约缓存异常: {e}")
            
    async def save_contracts_cache(self):
        """保存合约信息到缓存"""
        try:
            # 准备数据
            contracts_data = {}
            for symbol, contract in self.contracts.items():
                contract_dict = {
                    'symbol': contract.symbol,
                    'base_asset': contract.base_asset,
                    'quote_asset': contract.quote_asset,
                    'status': contract.status,
                    'min_qty': contract.min_qty,
                    'max_qty': contract.max_qty,
                    'step_size': contract.step_size,
                    'tick_size': contract.tick_size,
                    'min_notional': contract.min_notional,
                    'leverage_brackets': contract.leverage_brackets,
                    'last_updated': contract.last_updated.isoformat()
                }
                contracts_data[symbol] = contract_dict
                
            cache_data = {
                'contracts': contracts_data,
                'available_symbols': list(self.available_symbols),
                'last_update': self.last_update.isoformat() if self.last_update else None
            }
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
                
            logger.debug("合约缓存保存完成")
            
        except Exception as e:
            logger.error(f"保存合约缓存异常: {e}")
            
    def is_symbol_available(self, symbol: str) -> bool:
        """检查代币是否有期货合约 - 基础版本（仅查缓存）"""
        try:
            # 标准化符号
            base_symbol = symbol.upper().replace('USDT', '')
            contract_symbol = f"{base_symbol}USDT"

            # 检查是否在主缓存中
            return (base_symbol in self.available_symbols or
                   contract_symbol in self.contracts)

        except Exception as e:
            logger.error(f"检查符号可用性异常: {symbol} - {e}")
            return False

    async def check_new_coin_fast(self, symbol: str) -> bool:
        """专门用于新币的快速检查 - 核心方法"""
        try:
            base_symbol = symbol.upper().replace('USDT', '')
            contract_symbol = f"{base_symbol}USDT"
            current_time = datetime.now()

            # 1. 检查新币缓存
            if base_symbol in self.new_coin_cache:
                is_available, check_time = self.new_coin_cache[base_symbol]
                if (current_time - check_time).total_seconds() < self.new_coin_cache_duration:
                    logger.debug(f"🎯 新币缓存命中: {base_symbol} = {is_available}")
                    return is_available

            # 2. 检查负缓存（确认不存在的代币）
            if base_symbol in self.negative_cache:
                last_check = self.negative_cache[base_symbol]
                if (current_time - last_check).total_seconds() < self.negative_cache_duration:
                    logger.debug(f"❌ 负缓存命中: {base_symbol} 确认不存在")
                    return False

            # 3. 实时检查币安API
            logger.info(f"🔍 实时检查新币: {contract_symbol}")
            is_available = await self._real_time_check(contract_symbol)

            # 4. 更新缓存
            self.new_coin_cache[base_symbol] = (is_available, current_time)

            if not is_available:
                self.negative_cache[base_symbol] = current_time
                logger.info(f"❌ {contract_symbol} 合约不存在，已加入负缓存")
            else:
                # 如果存在，也更新主缓存
                self.available_symbols.add(base_symbol)
                logger.info(f"✅ {contract_symbol} 合约存在！已加入缓存")

            return is_available

        except Exception as e:
            logger.error(f"新币快速检查异常: {symbol} - {e}")
            return False

    async def _real_time_check(self, contract_symbol: str) -> bool:
        """实时检查币安API - 优化版本，提升速度"""
        import asyncio
        import aiohttp
        import time

        start_time = time.time()

        # 并发执行多种检查方法，取最快的结果
        if self.concurrent_checks:
            tasks = []

            # 任务1: Ticker检查（最快）
            if self.binance_client:
                tasks.append(asyncio.create_task(self._check_ticker_async(contract_symbol)))

            # 任务2: HTTP请求检查
            tasks.append(asyncio.create_task(self._check_http_async(contract_symbol)))

            if tasks:
                try:
                    # 等待第一个成功的结果，或者全部完成
                    done, pending = await asyncio.wait(
                        tasks,
                        timeout=self.fast_fail_timeout,
                        return_when=asyncio.FIRST_COMPLETED
                    )

                    # 取消剩余任务
                    for task in pending:
                        task.cancel()

                    # 检查结果
                    for task in done:
                        try:
                            result = await task
                            if result:
                                elapsed = (time.time() - start_time) * 1000
                                logger.info(f"✅ 快速检查成功: {contract_symbol} ({elapsed:.1f}ms)")
                                return True
                        except Exception as e:
                            logger.debug(f"并发检查任务异常: {e}")

                except asyncio.TimeoutError:
                    logger.debug(f"⏰ 快速检查超时: {contract_symbol}")

        # 如果并发检查失败，回退到传统方法
        return await self._fallback_check(contract_symbol)

    async def _check_ticker_async(self, contract_symbol: str) -> bool:
        """异步Ticker检查"""
        try:
            if self.binance_client:
                # 使用异步方式调用
                loop = asyncio.get_event_loop()
                ticker = await loop.run_in_executor(
                    None,
                    lambda: self.binance_client.futures_symbol_ticker(symbol=contract_symbol)
                )
                return ticker and 'price' in ticker
        except Exception as e:
            logger.debug(f"异步Ticker检查失败: {contract_symbol} - {e}")
        return False

    async def _check_http_async(self, contract_symbol: str) -> bool:
        """异步HTTP检查"""
        try:
            url = f"https://fapi.binance.com/fapi/v1/ticker/price?symbol={contract_symbol}"

            # 使用连接池提升性能
            connector = aiohttp.TCPConnector(
                limit=10,
                limit_per_host=5,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )

            timeout = aiohttp.ClientTimeout(total=self.fast_fail_timeout)

            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            ) as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        return 'price' in data

        except Exception as e:
            logger.debug(f"异步HTTP检查失败: {contract_symbol} - {e}")
        return False

    async def _fallback_check(self, contract_symbol: str) -> bool:
        """回退检查方法"""
        try:
            # 使用requests作为最后的备用方案
            import requests
            url = f"https://fapi.binance.com/fapi/v1/ticker/price?symbol={contract_symbol}"
            response = requests.get(url, timeout=self.fast_fail_timeout)
            if response.status_code == 200:
                data = response.json()
                if 'price' in data:
                    logger.info(f"✅ 回退检查成功: {contract_symbol}")
                    return True
        except Exception as e:
            logger.debug(f"回退检查失败: {contract_symbol} - {e}")

        logger.info(f"❌ 所有检查方法都失败: {contract_symbol}")
        return False

    async def quick_refresh_symbol(self, symbol: str):
        """快速刷新单个代币的合约信息"""
        try:
            if not self.binance_client:
                logger.warning("Binance客户端未初始化，无法快速刷新")
                return

            base_symbol = symbol.upper().replace('USDT', '')
            contract_symbol = f"{base_symbol}USDT"

            # 尝试获取该代币的ticker信息（快速检查是否存在）
            try:
                ticker = self.binance_client.futures_symbol_ticker(symbol=contract_symbol)
                if ticker and 'price' in ticker:
                    # 代币存在，更新缓存
                    self.symbol_lookup[base_symbol] = True
                    self.symbol_lookup[contract_symbol] = True
                    self.available_symbols.add(base_symbol)
                    self.last_quick_update = datetime.now()

                    logger.info(f"✅ 快速刷新成功: {contract_symbol} 合约存在")
                    return True

            except Exception:
                # 如果获取ticker失败，说明合约不存在
                self.symbol_lookup[base_symbol] = False
                self.symbol_lookup[contract_symbol] = False
                logger.info(f"❌ 快速刷新确认: {contract_symbol} 合约不存在")
                return False

        except Exception as e:
            logger.error(f"快速刷新异常: {symbol} - {e}")
            return False

    def get_contract_info(self, symbol: str) -> Optional[ContractInfo]:
        """获取合约信息"""
        try:
            # 标准化符号
            base_symbol = symbol.upper().replace('USDT', '')
            contract_symbol = f"{base_symbol}USDT"
            
            return self.contracts.get(contract_symbol)
            
        except Exception as e:
            logger.error(f"获取合约信息异常: {symbol} - {e}")
            return None
            
    def get_trading_symbol(self, symbol: str) -> Optional[str]:
        """获取交易符号"""
        try:
            # 标准化符号
            base_symbol = symbol.upper().replace('USDT', '')
            contract_symbol = f"{base_symbol}USDT"
            
            if contract_symbol in self.contracts:
                return contract_symbol
                
            return None
            
        except Exception as e:
            logger.error(f"获取交易符号异常: {symbol} - {e}")
            return None
            
    def calculate_position_size(self, symbol: str, amount_usdt: float) -> Dict[str, Any]:
        """计算合约仓位大小"""
        try:
            contract = self.get_contract_info(symbol)
            if not contract:
                return {'error': '合约不存在'}
                
            # 获取当前价格 (这里需要实时价格，简化处理)
            # 在实际使用中，应该从交易器获取实时价格
            estimated_price = 1.0  # 占位符
            
            # 计算数量
            quantity = amount_usdt / estimated_price
            
            # 调整到合约规格
            step_size = contract.step_size
            if step_size > 0:
                quantity = round(quantity / step_size) * step_size
                
            # 检查最小/最大数量
            if quantity < contract.min_qty:
                return {'error': f'数量太小，最小数量: {contract.min_qty}'}
                
            if quantity > contract.max_qty:
                return {'error': f'数量太大，最大数量: {contract.max_qty}'}
                
            # 检查最小名义价值
            notional_value = quantity * estimated_price
            if notional_value < contract.min_notional:
                return {'error': f'名义价值太小，最小值: {contract.min_notional}'}
                
            return {
                'symbol': contract.symbol,
                'quantity': quantity,
                'estimated_price': estimated_price,
                'notional_value': notional_value,
                'step_size': step_size,
                'tick_size': contract.tick_size
            }
            
        except Exception as e:
            logger.error(f"计算仓位大小异常: {symbol} - {e}")
            return {'error': str(e)}
            
    def get_max_leverage(self, symbol: str, notional_value: float) -> int:
        """获取最大杠杆倍数"""
        try:
            contract = self.get_contract_info(symbol)
            if not contract:
                return 1
                
            # 根据名义价值确定最大杠杆
            for bracket in contract.leverage_brackets:
                if notional_value <= bracket['notionalCap']:
                    return bracket['initialLeverage']
                    
            # 如果超过所有档位，返回最低杠杆
            return contract.leverage_brackets[-1]['initialLeverage'] if contract.leverage_brackets else 1
            
        except Exception as e:
            logger.error(f"获取最大杠杆异常: {symbol} - {e}")
            return 1
            
    def get_available_symbols(self) -> List[str]:
        """获取所有可用的交易符号"""
        return list(self.available_symbols)
        
    def get_contracts_summary(self) -> Dict[str, Any]:
        """获取合约摘要信息"""
        try:
            total_contracts = len(self.contracts)
            active_contracts = len([c for c in self.contracts.values() if c.status == 'TRADING'])
            
            # 按基础资产分类
            base_assets = {}
            for contract in self.contracts.values():
                base_asset = contract.base_asset
                if base_asset not in base_assets:
                    base_assets[base_asset] = 0
                base_assets[base_asset] += 1
                
            return {
                'total_contracts': total_contracts,
                'active_contracts': active_contracts,
                'base_assets_count': len(base_assets),
                'last_update': self.last_update.isoformat() if self.last_update else None,
                'cache_valid': not self.should_update_contracts(),
                'top_base_assets': dict(sorted(base_assets.items(), key=lambda x: x[1], reverse=True)[:10])
            }
            
        except Exception as e:
            logger.error(f"获取合约摘要异常: {e}")
            return {}
            
    async def refresh_contracts(self) -> bool:
        """手动刷新合约信息"""
        try:
            logger.info("手动刷新合约信息...")
            await self.update_contracts()
            return True
        except Exception as e:
            logger.error(f"手动刷新合约异常: {e}")
            return False
            
    async def check_symbol_before_trade(self, symbol: str) -> Dict[str, Any]:
        """交易前检查符号"""
        try:
            # 检查符号是否可用
            if not self.is_symbol_available(symbol):
                return {
                    'available': False,
                    'reason': '该代币没有期货合约',
                    'suggestion': '请检查代币符号或等待合约上线'
                }
                
            # 获取合约信息
            contract = self.get_contract_info(symbol)
            if not contract:
                return {
                    'available': False,
                    'reason': '无法获取合约信息',
                    'suggestion': '请刷新合约信息或联系技术支持'
                }
                
            # 检查合约状态
            if contract.status != 'TRADING':
                return {
                    'available': False,
                    'reason': f'合约状态异常: {contract.status}',
                    'suggestion': '该合约暂时无法交易'
                }
                
            return {
                'available': True,
                'contract_symbol': contract.symbol,
                'min_qty': contract.min_qty,
                'max_qty': contract.max_qty,
                'min_notional': contract.min_notional,
                'step_size': contract.step_size,
                'tick_size': contract.tick_size
            }
            
        except Exception as e:
            logger.error(f"交易前检查异常: {symbol} - {e}")
            return {
                'available': False,
                'reason': f'检查异常: {e}',
                'suggestion': '请重试或联系技术支持'
            }

#!/usr/bin/env python3
"""
检查C代币合约并创建模拟交易测试
"""
import sys
import os
import asyncio
sys.path.append(os.path.dirname(__file__))

from binance.client import Client
from src.utils import load_config

async def check_c_token_contract():
    """检查C代币合约"""
    print("🔍 检查C代币相关合约...")
    
    config = load_config('config.yaml')
    trading_config = config.get('trading', {}).get('binance', {})
    
    api_key = trading_config.get('api_key')
    api_secret = trading_config.get('api_secret')
    
    try:
        client = Client(api_key, api_secret)
        exchange_info = client.futures_exchange_info()
        symbols = [s['symbol'] for s in exchange_info['symbols']]
        
        # 检查所有以C开头的USDT合约
        c_symbols = [s for s in symbols if s.startswith('C') and s.endswith('USDT')]
        
        print(f"📋 找到 {len(c_symbols)} 个以C开头的USDT合约:")
        for symbol in c_symbols[:15]:  # 显示前15个
            try:
                ticker = client.futures_symbol_ticker(symbol=symbol)
                price = float(ticker['price'])
                print(f"   {symbol}: ${price:.6f}")
            except:
                print(f"   {symbol}: 价格获取失败")
        
        # 检查是否有简单的 'CUSDT'
        if 'CUSDT' in symbols:
            print("\n✅ 找到CUSDT合约")
            ticker = client.futures_symbol_ticker(symbol='CUSDT')
            price = float(ticker['price'])
            return 'CUSDT', price
        else:
            # 选择一个活跃的C开头代币作为测试
            active_c_symbols = []
            for symbol in c_symbols[:5]:
                try:
                    ticker = client.futures_symbol_ticker(symbol=symbol)
                    price = float(ticker['price'])
                    if price > 0:
                        active_c_symbols.append((symbol, price))
                except:
                    continue
            
            if active_c_symbols:
                test_symbol, test_price = active_c_symbols[0]  # 选择第一个
                print(f"\n✅ 选择 {test_symbol} 作为测试代币，价格: ${test_price:.6f}")
                return test_symbol, test_price
            else:
                print("\n❌ 没有找到可用的C开头代币")
                return None, None
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None, None

if __name__ == "__main__":
    asyncio.run(check_c_token_contract())
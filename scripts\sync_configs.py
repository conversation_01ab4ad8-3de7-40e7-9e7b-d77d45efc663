#!/usr/bin/env python3
"""
配置文件同步工具
确保配置文件备份和管理
"""
import os
import sys
import yaml
import shutil
from pathlib import Path
from typing import Dict, Any
from loguru import logger

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def load_yaml_config(file_path: str) -> Dict[str, Any]:
    """加载YAML配置文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    except Exception as e:
        logger.error(f"加载配置文件失败 {file_path}: {e}")
        return {}

def save_yaml_config(config: Dict[str, Any], file_path: str):
    """保存YAML配置文件"""
    try:
        # 创建目录
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        with open(file_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
        logger.info(f"✅ 配置文件已保存: {file_path}")
    except Exception as e:
        logger.error(f"保存配置文件失败 {file_path}: {e}")

def sync_configs():
    """同步配置文件并创建备份"""
    logger.info("🔄 开始同步配置文件...")

    # 配置文件路径
    main_config = "config.yaml"
    example_config = "configs/config_with_feishu.yaml"

    # 1. 加载主配置文件
    logger.info("📋 加载主配置文件...")
    config = load_yaml_config(main_config)
    if not config:
        logger.error("❌ 主配置文件为空或无法加载")
        return False

    # 2. 更新示例配置文件
    logger.info("📝 更新示例配置文件...")
    example_config_data = load_yaml_config(example_config)

    # 保留示例配置的结构，但更新关键配置
    if example_config_data:
        # 更新固定策略配置
        if 'fixed_strategy' in config:
            example_config_data['fixed_strategy'] = config['fixed_strategy']

        # 更新监控配置结构（但保留示例值）
        if 'monitoring' in config:
            if 'monitoring' not in example_config_data:
                example_config_data['monitoring'] = {}
            if 'telegram' not in example_config_data['monitoring']:
                example_config_data['monitoring']['telegram'] = {}

            # 保留关键词等配置
            if 'keywords' in config['monitoring'].get('telegram', {}):
                example_config_data['monitoring']['telegram']['keywords'] = config['monitoring']['telegram']['keywords']

    save_yaml_config(example_config_data, example_config)

    # 3. 创建备份
    logger.info("💾 创建配置备份...")
    backup_dir = "backup_configs"
    os.makedirs(backup_dir, exist_ok=True)

    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    if os.path.exists(main_config):
        shutil.copy2(main_config, f"{backup_dir}/config_{timestamp}.yaml")

    logger.info("✅ 配置文件同步完成！")
    logger.info("\n📋 同步摘要:")
    logger.info(f"  • 主配置: {main_config}")
    logger.info(f"  • 示例配置: {example_config}")
    logger.info(f"  • 备份目录: {backup_dir}")

    return True

def check_config_consistency():
    """检查配置文件完整性"""
    logger.info("🔍 检查配置文件完整性...")

    # 加载配置
    config = load_yaml_config("config.yaml")
    if not config:
        logger.error("❌ 主配置文件无法加载")
        return False

    issues = []

    # 检查关键配置是否存在
    if not config.get('trading', {}).get('binance', {}).get('api_key'):
        issues.append("缺少 Binance API Key")

    if not config.get('trading', {}).get('binance', {}).get('api_secret'):
        issues.append("缺少 Binance API Secret")

    if not config.get('monitoring', {}).get('telegram', {}).get('api_id'):
        issues.append("缺少 Telegram API ID")

    if not config.get('monitoring', {}).get('telegram', {}).get('api_hash'):
        issues.append("缺少 Telegram API Hash")

    if issues:
        logger.warning("⚠️ 发现配置问题:")
        for issue in issues:
            logger.warning(f"  • {issue}")
        return False
    else:
        logger.info("✅ 配置完整性检查通过")
        return True

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="配置文件管理工具")
    parser.add_argument('--check', action='store_true', help='检查配置文件完整性')
    parser.add_argument('--sync', action='store_true', help='同步配置文件并创建备份')

    args = parser.parse_args()

    if args.check:
        check_config_consistency()
    elif args.sync:
        sync_configs()
    else:
        # 默认先检查再同步
        if not check_config_consistency():
            logger.info("配置有问题，开始同步...")
            sync_configs()
        else:
            logger.info("配置检查通过，创建备份...")
            sync_configs()

#!/usr/bin/env python3
"""
模拟C代币上新交易完整流程测试
模拟参数：5U本金，10倍杠杆，50秒后全部平仓
"""
import sys
import os
import asyncio
import time
sys.path.append(os.path.dirname(__file__))

from datetime import datetime
from src.models import TradingSignal, SignalSource, TokenInfo
from src.core.fixed_signal_processor import FixedSignalProcessor
from src.utils import load_config

async def simulate_c_token_listing():
    """模拟C代币上新完整流程"""
    print("🎯 开始模拟C代币上新交易测试...")
    print("=" * 60)
    print("📋 测试参数:")
    print("   代币: C (CUSDT)")
    print("   本金: 5 USDT")
    print("   杠杆: 10倍")
    print("   平仓时间: 50秒后全部平仓")
    print("=" * 60)
    
    # 1. 创建模拟的上新信号
    print("\n🔔 步骤1: 创建模拟上新信号")
    
    token_info = TokenInfo(
        symbol="C",
        market_cap="127M"  # 假设市值
    )
    
    # 模拟Coinbase上新公告
    signal_content = """COINBASE LISTING: Assets added to the roadmap today: C (C)
COINBASE 上新: 今日路线图新增资产：C (C)
$C MarketCap: $127M
The trading-only phase will begin on approximately August 5, 2025, at 9:00 AM PT."""
    
    mock_signal = TradingSignal(
        source=SignalSource.TELEGRAM,
        symbol="C",
        timestamp=datetime.now(),
        content=signal_content,
        tokens=[token_info],
        exchange="coinbase",
        metadata={
            'market_cap': '127M',
            'message_id': 789012,
            'channel': '@BWEnews'
        }
    )
    
    print(f"✅ 创建信号完成: {mock_signal.symbol} ({mock_signal.exchange})")
    print(f"   信号内容: {mock_signal.content[:80]}...")
    
    # 2. 加载配置并初始化固定策略处理器
    print("\n🛠️ 步骤2: 初始化交易系统")
    config = load_config('config.yaml')
    
    # 临时修改固定策略参数
    original_amount = config['fixed_strategy']['amount']
    original_first_close_time = config['fixed_strategy']['first_close_time']
    original_first_close_percentage = config['fixed_strategy']['first_close_percentage']
    
    # 设置测试参数
    config['fixed_strategy']['amount'] = 5  # 5U本金
    config['fixed_strategy']['first_close_time'] = 50  # 50秒后平仓
    config['fixed_strategy']['first_close_percentage'] = 100  # 100%平仓（全部平仓）
    
    print(f"✅ 临时修改策略参数:")
    print(f"   本金: {original_amount}U → 5U")
    print(f"   平仓时间: {original_first_close_time}秒 → 50秒")
    print(f"   平仓比例: {original_first_close_percentage}% → 100%")
    
    # 初始化交易组件
    print("💰 初始化币安客户端和交易策略...")
    from binance.client import Client as BinanceClient
    from src.trading.contract_manager import ContractManager
    from src.trading.fixed_strategy import initialize_fixed_strategy
    
    # 获取币安配置
    binance_config = config.get('trading', {}).get('binance', {})
    if not binance_config.get('api_key') or not binance_config.get('api_secret'):
        print("❌ 币安API配置不完整")
        return
    
    # 初始化币安客户端
    binance_client = BinanceClient(
        api_key=binance_config['api_key'],
        api_secret=binance_config['api_secret'],
        testnet=binance_config.get('testnet', False)
    )
    
    # 测试连接
    binance_client.ping()
    print("✅ 币安客户端连接成功")
    
    # 初始化合约管理器
    contract_manager = ContractManager(binance_config)
    await contract_manager.initialize(binance_client)
    print("✅ 合约管理器初始化完成")
    
    # 初始化固定策略
    fixed_strategy = initialize_fixed_strategy(binance_client, contract_manager, config)
    print("✅ 固定交易策略初始化完成")
    
    # 初始化信号处理器
    signal_processor = FixedSignalProcessor(config)
    
    # 3. 处理模拟信号
    print("\n🚀 步骤3: 处理上新信号并执行交易")
    print("⚠️  注意：这将进行真实的交易操作！")
    
    # 立即开始交易测试
    print("   ⚡ 立即开始交易...")
    await asyncio.sleep(0.1)  # 极小延迟确保日志输出
    
    # 处理信号
    start_time = time.time()
    success = await signal_processor.process_listing_signal(mock_signal)
    
    if success:
        print("✅ 信号处理成功，交易已启动！")
        print("📊 交易状态监控中...")
        
        # 4. 监控交易过程（等待自动平仓）
        print(f"\n⏱️ 步骤4: 等待自动平仓（50秒）")
        
        # 等待交易完成（50秒 + 一些缓冲时间）
        total_wait_time = 60  # 60秒确保交易完成
        for remaining in range(total_wait_time, 0, -1):
            print(f"\r   等待交易完成: {remaining}秒 ", end="", flush=True)
            await asyncio.sleep(1)
        
        print("\n✅ 交易监控完成")
        
        # 5. 显示交易结果
        print(f"\n📈 步骤5: 交易结果汇总")
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"   总耗时: {total_time:.1f}秒")
        print("   通知状态: 已发送到飞书（如果配置正确）")
        print("   交易记录: 已保存到数据库")
        
        print(f"\n🎉 模拟测试完成！")
        print("💡 请检查:")
        print("   1. 飞书通知是否收到（上新通知 + 交易结果）")
        print("   2. 币安账户资金变化")
        print("   3. 系统日志中的交易记录")
        
    else:
        print("❌ 信号处理失败")
        print("可能原因:")
        print("   - C代币合约检查失败")
        print("   - 账户余额不足")
        print("   - API权限问题")
    
    # 恢复原始配置
    config['fixed_strategy']['amount'] = original_amount
    config['fixed_strategy']['first_close_time'] = original_first_close_time
    config['fixed_strategy']['first_close_percentage'] = original_first_close_percentage
    
    print(f"\n🔄 配置已恢复为原始值")

async def main():
    print("🧪 C代币上新模拟交易测试")
    print("⚠️  警告：这将使用真实的币安API进行交易！")
    print("💰 预计使用资金：约5 USDT")
    
    # 自动确认操作（用于测试）
    print("\n✅ 自动确认执行测试")
    confirm = 'YES'
    
    await simulate_c_token_listing()

if __name__ == "__main__":
    asyncio.run(main())
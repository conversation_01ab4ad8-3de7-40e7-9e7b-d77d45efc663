"""
交易统计数据库适配器
替换原来的JSON文件存储，使用统一数据库
"""
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from loguru import logger

from ..models import TradingOrder, OrderStatus, SignalSource
from ..database import unified_db, TradingOrderRecord, TradingHistoryRecord


class TradingStatisticsDB:
    """基于数据库的交易统计器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # 内存缓存（用于快速访问活跃持仓）
        self.active_positions: Dict[str, TradingHistoryRecord] = {}
        
        # 加载活跃持仓到内存
        self._load_active_positions()
        
        logger.info("数据库交易统计器初始化完成")
        
    def _load_active_positions(self):
        """加载活跃持仓到内存"""
        try:
            # 从数据库获取未平仓的交易
            conn = unified_db.db_path
            import sqlite3
            conn = sqlite3.connect(unified_db.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT order_id, symbol, side, entry_price, amount, leverage,
                       entry_time, exchange_source, signal_source
                FROM trading_history
                WHERE status = 'open'
            ''')
            
            rows = cursor.fetchall()
            conn.close()
            
            for row in rows:
                record = TradingHistoryRecord(
                    order_id=row[0],
                    symbol=row[1],
                    side=row[2],
                    entry_price=row[3],
                    exit_price=None,
                    amount=row[4],
                    leverage=row[5],
                    status='open',
                    entry_time=datetime.fromisoformat(row[6]) if row[6] else None,
                    exchange_source=row[7],
                    signal_source=row[8]
                )
                self.active_positions[row[1]] = record
                
            logger.info(f"加载活跃持仓: {len(self.active_positions)} 个")
            
        except Exception as e:
            logger.error(f"加载活跃持仓失败: {e}")
            
    def record_trade_entry(self, order: TradingOrder, signal_source: str = "unknown") -> Optional[TradingHistoryRecord]:
        """记录开仓交易"""
        try:
            # 创建交易历史记录
            record = TradingHistoryRecord(
                order_id=order.order_id or f"order_{datetime.now().timestamp()}",
                symbol=order.symbol,
                side=order.side.value,
                entry_price=order.filled_price or 0.0,
                exit_price=None,
                amount=order.amount,
                leverage=order.leverage,
                profit=0.0,
                profit_percent=0.0,
                status='open',
                entry_time=order.filled_at or datetime.now(),
                exit_time=None,
                duration_minutes=None,
                exchange_source=order.metadata.get('exchange', 'unknown') if hasattr(order, 'metadata') and order.metadata else 'unknown',
                signal_source=signal_source
            )
            
            # 保存到数据库
            history_id = unified_db.save_trading_history(record)
            if history_id:
                record.id = history_id
                
                # 添加到活跃持仓
                self.active_positions[order.symbol] = record
                
                # 同时保存订单记录
                order_record = TradingOrderRecord(
                    order_id=record.order_id,
                    symbol=order.symbol,
                    side=order.side.value,
                    amount=order.amount,
                    leverage=order.leverage,
                    stop_loss_percent=order.stop_loss_percent,
                    status=order.status.value,
                    filled_price=order.filled_price,
                    created_at=order.created_at,
                    filled_at=order.filled_at,
                    error_message=order.error_message
                )
                unified_db.save_trading_order(order_record)
                
                logger.info(f"记录开仓交易: {order.symbol} - ${order.amount}")
                return record
            else:
                logger.error(f"保存开仓交易失败: {order.symbol}")
                return None
            
        except Exception as e:
            logger.error(f"记录开仓交易异常: {e}")
            return None
            
    def record_trade_exit(self, symbol: str, exit_price: float, close_reason: str = "manual") -> Optional[TradingHistoryRecord]:
        """记录平仓交易"""
        try:
            if symbol not in self.active_positions:
                logger.warning(f"未找到活跃持仓: {symbol}")
                return None
                
            record = self.active_positions[symbol]
            
            # 更新平仓信息
            record.exit_price = exit_price
            record.exit_time = datetime.now()
            record.status = 'closed'
            record.close_reason = close_reason
            
            # 计算持仓时间
            if record.entry_time and record.exit_time:
                duration = record.exit_time - record.entry_time
                record.duration_minutes = int(duration.total_seconds() / 60)
            
            # 计算盈亏
            if record.entry_price and exit_price:
                if record.side.upper() == 'LONG':
                    profit_percent = ((exit_price - record.entry_price) / record.entry_price) * 100
                else:  # SHORT
                    profit_percent = ((record.entry_price - exit_price) / record.entry_price) * 100
                
                # 考虑杠杆
                profit_percent *= record.leverage
                record.profit_percent = profit_percent
                
                # 计算实际盈亏金额
                record.profit = (record.amount * profit_percent / 100)
            
            # 更新数据库
            unified_db.save_trading_history(record)
            
            # 从活跃持仓中移除
            del self.active_positions[symbol]
            
            logger.info(f"记录平仓交易: {symbol} - 盈利${record.profit:.2f} ({record.profit_percent:.2f}%)")
            return record
            
        except Exception as e:
            logger.error(f"记录平仓交易异常: {e}")
            return None
            
    def get_statistics(self) -> Dict[str, Any]:
        """获取交易统计信息"""
        try:
            return unified_db.get_trading_statistics()
        except Exception as e:
            logger.error(f"获取统计信息异常: {e}")
            return {}
            
    def get_recent_trades(self, days: int = 7, limit: int = 50) -> List[TradingHistoryRecord]:
        """获取最近交易记录"""
        try:
            return unified_db.get_trading_history(limit=limit)
        except Exception as e:
            logger.error(f"获取最近交易异常: {e}")
            return []
            
    def get_active_positions(self) -> Dict[str, TradingHistoryRecord]:
        """获取活跃持仓"""
        return self.active_positions.copy()
        
    def cleanup_old_records(self, days: int = 30):
        """清理旧记录（数据库版本不需要手动清理）"""
        logger.info(f"数据库版本无需手动清理旧记录")
        
    def add_signal_record(self, signal):
        """添加信号记录（已在信号处理器中处理）"""
        logger.debug(f"信号记录已在信号处理器中保存到数据库: {signal.symbol}")


# 创建全局实例（兼容性）
def create_trading_statistics(config: Dict[str, Any]) -> TradingStatisticsDB:
    """创建交易统计实例"""
    return TradingStatisticsDB(config)

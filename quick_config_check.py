#!/usr/bin/env python3
"""
快速配置检查工具
快速验证关键配置项，适合启动前的快速检查
"""
import sys
import yaml
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))


def check_config_completeness():
    """检查配置完整性"""
    print("🔍 快速配置检查工具")
    print("=" * 40)
    
    try:
        # 1. 检查配置文件是否存在
        config_file = Path("config.yaml")
        if not config_file.exists():
            print("❌ 配置文件 config.yaml 不存在")
            return False
        
        # 2. 加载配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("✅ 配置文件加载成功")
        
        # 3. 检查关键配置项
        issues = []
        warnings = []
        
        # 检查交易配置
        trading = config.get('trading', {})
        binance = trading.get('binance', {})
        
        if not binance.get('enabled', False):
            warnings.append("币安交易未启用")
        else:
            if not binance.get('api_key'):
                issues.append("币安API密钥未配置")
            if not binance.get('api_secret'):
                issues.append("币安API密钥未配置")
        
        # 检查跟单配置
        copy_trading = config.get('copy_trading', {})
        if copy_trading.get('enabled', False):
            if not copy_trading.get('api_key'):
                warnings.append("跟单API密钥未配置")
            if not copy_trading.get('api_secret'):
                warnings.append("跟单API密钥未配置")
        
        # 检查Telegram配置
        telegram = config.get('monitoring', {}).get('telegram', {})
        if not telegram.get('api_id'):
            issues.append("Telegram API ID未配置")
        if not telegram.get('api_hash'):
            issues.append("Telegram API Hash未配置")
        if not telegram.get('channels'):
            warnings.append("Telegram监控频道未配置")
        
        # 检查飞书配置
        feishu = config.get('notifications', {}).get('feishu', {})
        if feishu.get('enabled', False):
            if not feishu.get('webhook_url'):
                issues.append("飞书Webhook URL未配置")
        
        # 检查Web配置
        web = config.get('web', {})
        if not web.get('admin_password'):
            warnings.append("Web管理员密码未配置")
        
        # 4. 显示检查结果
        print("\n📋 配置检查结果:")
        print("-" * 25)
        
        if not issues and not warnings:
            print("🎉 所有关键配置项检查通过！")
            return True
        
        if issues:
            print("❌ 发现严重问题:")
            for issue in issues:
                print(f"  • {issue}")
        
        if warnings:
            print("⚠️ 发现警告:")
            for warning in warnings:
                print(f"  • {warning}")
        
        print(f"\n📊 总结: {len(issues)} 个错误, {len(warnings)} 个警告")
        
        if issues:
            print("\n🚨 建议修复错误后再启动系统")
            return False
        else:
            print("\n✅ 系统可以启动，但建议检查警告项")
            return True
            
    except Exception as e:
        print(f"❌ 配置检查异常: {e}")
        return False


def check_required_files():
    """检查必需文件"""
    print("\n📁 检查必需文件:")
    print("-" * 20)
    
    required_files = [
        "config.yaml",
        "main.py", 
        "src/core/system_controller.py",
        "src/core/config_manager.py",
        "src/core/runtime_config_validator.py",
        "requirements.txt"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ 缺少 {len(missing_files)} 个必需文件")
        return False
    else:
        print("\n✅ 所有必需文件存在")
        return True


def check_python_environment():
    """检查Python环境"""
    print("\n🐍 检查Python环境:")
    print("-" * 20)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ Python版本过低，需要3.8+")
        return False
    else:
        print("✅ Python版本符合要求")
    
    # 检查关键依赖
    required_packages = [
        'asyncio',
        'yaml', 
        'aiohttp',
        'fastapi',
        'loguru'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少 {len(missing_packages)} 个依赖包")
        print("建议运行: pip install -r requirements.txt")
        return False
    else:
        print("\n✅ 关键依赖包检查通过")
        return True


def main():
    """主函数"""
    print("🚀 系统启动前快速检查")
    print("=" * 50)
    
    checks = [
        ("配置完整性", check_config_completeness),
        ("必需文件", check_required_files), 
        ("Python环境", check_python_environment)
    ]
    
    results = []
    
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}检查异常: {e}")
            results.append((name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 检查总结:")
    print("-" * 20)
    
    passed = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
        if result:
            passed += 1
    
    print(f"\n通过率: {passed}/{len(results)} ({passed/len(results)*100:.1f}%)")
    
    if passed == len(results):
        print("\n🎉 所有检查通过，系统可以启动！")
        print("建议运行: python main.py")
        return 0
    else:
        print(f"\n⚠️ {len(results)-passed} 项检查失败，建议修复后再启动")
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断检查")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        sys.exit(1)

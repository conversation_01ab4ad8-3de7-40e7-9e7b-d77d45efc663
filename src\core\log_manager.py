"""
日志管理器 - 自动清理和轮转
"""
import os
import glob
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional
from loguru import logger
import schedule
import threading
import time


class LogManager:
    """日志管理器 - 负责日志清理和轮转"""
    
    def __init__(self, config: dict = None):
        self.config = config or {}
        
        # 日志配置
        self.log_dir = self.config.get('log_dir', 'logs')
        self.max_days = self.config.get('max_days', 10)  # 保留10天
        self.max_size_mb = self.config.get('max_size_mb', 100)  # 单文件最大100MB
        self.cleanup_time = self.config.get('cleanup_time', '02:00')  # 凌晨2点清理
        
        # 确保日志目录存在
        Path(self.log_dir).mkdir(parents=True, exist_ok=True)
        
        # 调度器线程
        self.scheduler_thread = None
        self.running = False
        
        logger.info(f"日志管理器初始化完成 - 保留{self.max_days}天，每日{self.cleanup_time}清理")
    
    def start(self):
        """启动日志管理器"""
        if self.running:
            logger.warning("日志管理器已在运行")
            return
            
        self.running = True
        
        # 设置定时任务
        schedule.clear()
        schedule.every().day.at(self.cleanup_time).do(self._cleanup_old_logs)
        
        # 启动调度器线程
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        # 立即执行一次清理
        self._cleanup_old_logs()
        
        logger.info(f"✅ 日志管理器已启动 - 每日{self.cleanup_time}自动清理")
    
    def stop(self):
        """停止日志管理器"""
        self.running = False
        schedule.clear()
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
            
        logger.info("日志管理器已停止")
    
    def _run_scheduler(self):
        """运行调度器"""
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"日志调度器异常: {e}")
                time.sleep(60)
    
    def _cleanup_old_logs(self):
        """清理旧日志文件"""
        try:
            logger.info("🧹 开始清理旧日志文件...")
            
            # 计算截止日期
            cutoff_date = datetime.now() - timedelta(days=self.max_days)
            
            # 查找所有日志文件
            log_patterns = [
                os.path.join(self.log_dir, "*.log"),
                os.path.join(self.log_dir, "*.log.*"),
                "*.log",  # 根目录的日志文件
                "*.log.*"
            ]
            
            deleted_count = 0
            total_size_freed = 0
            
            for pattern in log_patterns:
                for log_file in glob.glob(pattern):
                    try:
                        # 获取文件修改时间
                        file_mtime = datetime.fromtimestamp(os.path.getmtime(log_file))
                        
                        if file_mtime < cutoff_date:
                            # 获取文件大小
                            file_size = os.path.getsize(log_file)
                            
                            # 删除文件
                            os.remove(log_file)
                            
                            deleted_count += 1
                            total_size_freed += file_size
                            
                            logger.debug(f"删除旧日志: {log_file} ({file_size} bytes)")
                            
                    except Exception as e:
                        logger.error(f"删除日志文件失败 {log_file}: {e}")
            
            if deleted_count > 0:
                size_mb = total_size_freed / (1024 * 1024)
                logger.info(f"✅ 日志清理完成: 删除{deleted_count}个文件，释放{size_mb:.2f}MB空间")
            else:
                logger.info("✅ 日志清理完成: 无需删除文件")
                
            # 检查当前日志文件大小
            self._check_current_log_size()
            
        except Exception as e:
            logger.error(f"清理日志文件异常: {e}")
    
    def _check_current_log_size(self):
        """检查当前日志文件大小"""
        try:
            current_log_files = [
                "trading_bot.log",
                os.path.join(self.log_dir, "trading_bot.log")
            ]
            
            for log_file in current_log_files:
                if os.path.exists(log_file):
                    file_size_mb = os.path.getsize(log_file) / (1024 * 1024)
                    
                    if file_size_mb > self.max_size_mb:
                        # 轮转日志文件
                        self._rotate_log_file(log_file)
                        
        except Exception as e:
            logger.error(f"检查日志文件大小异常: {e}")
    
    def _rotate_log_file(self, log_file: str):
        """轮转日志文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            rotated_file = f"{log_file}.{timestamp}"
            
            # 重命名当前日志文件
            os.rename(log_file, rotated_file)
            
            logger.info(f"📋 日志文件已轮转: {log_file} -> {rotated_file}")
            
        except Exception as e:
            logger.error(f"轮转日志文件失败 {log_file}: {e}")
    
    def get_log_statistics(self) -> dict:
        """获取日志统计信息"""
        try:
            stats = {
                'log_dir': self.log_dir,
                'max_days': self.max_days,
                'total_files': 0,
                'total_size_mb': 0,
                'oldest_file': None,
                'newest_file': None
            }
            
            # 查找所有日志文件
            log_patterns = [
                os.path.join(self.log_dir, "*.log*"),
                "*.log*"
            ]
            
            oldest_time = None
            newest_time = None
            
            for pattern in log_patterns:
                for log_file in glob.glob(pattern):
                    if os.path.isfile(log_file):
                        stats['total_files'] += 1
                        stats['total_size_mb'] += os.path.getsize(log_file) / (1024 * 1024)
                        
                        file_mtime = datetime.fromtimestamp(os.path.getmtime(log_file))
                        
                        if oldest_time is None or file_mtime < oldest_time:
                            oldest_time = file_mtime
                            stats['oldest_file'] = log_file
                            
                        if newest_time is None or file_mtime > newest_time:
                            newest_time = file_mtime
                            stats['newest_file'] = log_file
            
            stats['total_size_mb'] = round(stats['total_size_mb'], 2)
            
            return stats
            
        except Exception as e:
            logger.error(f"获取日志统计异常: {e}")
            return {}
    
    def force_cleanup(self):
        """强制执行清理"""
        logger.info("🔧 手动触发日志清理...")
        self._cleanup_old_logs()
    
    def set_cleanup_schedule(self, time_str: str):
        """设置清理时间"""
        try:
            # 验证时间格式
            datetime.strptime(time_str, "%H:%M")
            
            self.cleanup_time = time_str
            
            # 重新设置定时任务
            schedule.clear()
            schedule.every().day.at(self.cleanup_time).do(self._cleanup_old_logs)
            
            logger.info(f"✅ 日志清理时间已更新为: {self.cleanup_time}")
            
        except ValueError:
            logger.error(f"无效的时间格式: {time_str}，应为 HH:MM 格式")


# 全局日志管理器实例
log_manager = None


def initialize_log_manager(config: dict = None) -> LogManager:
    """初始化全局日志管理器"""
    global log_manager
    
    if log_manager is None:
        log_manager = LogManager(config)
        log_manager.start()
    
    return log_manager


def get_log_manager() -> Optional[LogManager]:
    """获取全局日志管理器"""
    return log_manager


def shutdown_log_manager():
    """关闭全局日志管理器"""
    global log_manager
    
    if log_manager:
        log_manager.stop()
        log_manager = None

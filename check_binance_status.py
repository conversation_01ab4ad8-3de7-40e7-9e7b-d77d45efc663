#!/usr/bin/env python3
"""
检查币安合约和账户状态
"""
import sys
import os
import asyncio
sys.path.append(os.path.dirname(__file__))

from binance.client import Client
from src.utils import load_config

async def check_binance_status():
    """检查币安账户和合约状态"""
    print("🔍 检查币安合约和账户状态...")
    
    # 加载配置
    config = load_config('config.yaml')
    trading_config = config.get('trading', {}).get('binance', {})
    
    api_key = trading_config.get('api_key')
    api_secret = trading_config.get('api_secret')
    
    if not api_key or not api_secret:
        print("❌ 币安API配置不完整")
        return False
    
    try:
        # 创建币安客户端
        client = Client(api_key, api_secret)
        
        # 1. 检查账户权限
        print("\n📊 检查账户信息...")
        account_info = client.futures_account()
        total_balance = float(account_info['totalWalletBalance'])
        available_balance = float(account_info['availableBalance'])
        
        print(f"   总余额: {total_balance:.2f} USDT")
        print(f"   可用余额: {available_balance:.2f} USDT")
        
        if available_balance < 10:
            print("⚠️  可用余额不足10 USDT，建议增加资金")
            return False
        
        # 2. 检查C代币合约是否存在
        print("\n🔍 检查C代币合约...")
        try:
            # 获取CUSDT合约信息
            exchange_info = client.futures_exchange_info()
            symbols = [s['symbol'] for s in exchange_info['symbols']]
            
            if 'CUSDT' in symbols:
                print("✅ CUSDT合约存在")
                
                # 获取价格信息
                ticker = client.futures_symbol_ticker(symbol='CUSDT')
                price = float(ticker['price'])
                print(f"   当前价格: ${price:.6f}")
                
                # 计算5U能买多少代币
                amount_usd = 5
                leverage = 10
                position_size = amount_usd * leverage / price
                print(f"   5U本金10倍杠杆可买: {position_size:.2f} C")
                
                return True
            else:
                print("❌ CUSDT合约不存在")
                print("📋 可用的类似合约:")
                c_like_symbols = [s for s in symbols if s.startswith('C') and s.endswith('USDT')][:10]
                for symbol in c_like_symbols:
                    print(f"     {symbol}")
                return False
                
        except Exception as e:
            print(f"❌ 检查合约失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 连接币安API失败: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(check_binance_status())
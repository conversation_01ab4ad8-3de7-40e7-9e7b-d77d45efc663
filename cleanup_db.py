#!/usr/bin/env python3
"""
清理数据库中的错误符号条目
删除那些符号包含数字的记录，这些都是错误识别的市值数据
"""
import sys
import os
import re
import sqlite3
sys.path.append('src')

from database import exchange_db

def cleanup_invalid_symbols():
    """清理包含数字的无效符号"""
    
    print("开始清理数据库中的无效符号...")
    
    # 获取所有列表事件
    all_listings = exchange_db.get_listing_events()
    print(f"当前数据库中有 {len(all_listings)} 条记录")
    
    invalid_entries = []
    valid_entries = []
    
    # 检查每个条目
    for listing in all_listings:
        symbol = listing.symbol
        
        # 检查符号是否包含数字（无效）
        if re.search(r'\d', symbol):
            invalid_entries.append(listing)
            print(f"❌ 无效符号: {symbol} ({listing.exchange}) - 包含数字")
        else:
            valid_entries.append(listing)
    
    print(f"\n找到 {len(invalid_entries)} 个无效条目")
    print(f"保留 {len(valid_entries)} 个有效条目")
    
    if invalid_entries:
        # 显示要删除的条目
        print("\n准备删除的条目:")
        for entry in invalid_entries[:10]:  # 显示前10个
            print(f"  {entry.exchange} - {entry.symbol} (时间: {entry.announcement_time})")
        
        if len(invalid_entries) > 10:
            print(f"  ... 还有 {len(invalid_entries) - 10} 个条目")
        
        print(f"\n自动确认删除这 {len(invalid_entries)} 个无效条目...")
        
        # 直接使用sqlite连接删除无效条目
        deleted_count = 0
        db_path = exchange_db.db_path
        
        for entry in invalid_entries:
            try:
                # 直接执行SQL删除
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 先检查记录是否存在
                cursor.execute('''
                    SELECT COUNT(*) FROM listing_events 
                    WHERE exchange = ? AND symbol = ? AND announcement_time = ?
                ''', (entry.exchange, entry.symbol, entry.announcement_time))
                count_before = cursor.fetchone()[0]
                
                if count_before > 0:
                    cursor.execute('''
                        DELETE FROM listing_events 
                        WHERE exchange = ? AND symbol = ? AND announcement_time = ?
                    ''', (entry.exchange, entry.symbol, entry.announcement_time))
                    
                    # 验证删除
                    cursor.execute('''
                        SELECT COUNT(*) FROM listing_events 
                        WHERE exchange = ? AND symbol = ? AND announcement_time = ?
                    ''', (entry.exchange, entry.symbol, entry.announcement_time))
                    count_after = cursor.fetchone()[0]
                    
                    conn.commit()
                    
                    if count_after == 0:
                        deleted_count += 1
                        print(f"✅ 删除成功: {entry.exchange}-{entry.symbol}")
                    else:
                        print(f"❌ 删除失败: {entry.exchange}-{entry.symbol} - 记录仍存在")
                else:
                    print(f"⚠️ 记录不存在: {entry.exchange}-{entry.symbol}")
                    
                conn.close()
            except Exception as e:
                print(f"删除失败: {entry.exchange}-{entry.symbol} - {e}")
        
        print(f"✅ 成功删除 {deleted_count} 个无效条目")
        print(f"✅ 数据库清理完成，剩余 {len(valid_entries)} 个有效条目")
    else:
        print("✅ 没有发现无效条目，数据库是干净的")

if __name__ == "__main__":
    cleanup_invalid_symbols()
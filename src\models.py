"""
数据模型定义
"""
from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional, Dict, Any
from enum import Enum


class SignalSource(Enum):
    """信号来源"""
    UPBIT = "upbit"
    BINANCE = "binance"
    COINBASE = "coinbase"
    BITHUMB = "bithumb"
    TELEGRAM = "telegram"


class OrderSide(Enum):
    """订单方向"""
    LONG = "LONG"
    SHORT = "SHORT"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "PENDING"
    FILLED = "FILLED"
    CANCELLED = "CANCELLED"
    FAILED = "FAILED"


@dataclass
class TokenInfo:
    """代币信息"""
    symbol: str
    name: Optional[str] = None
    market_cap: Optional[float] = None
    price: Optional[float] = None
    
    
@dataclass
class TradingSignal:
    """交易信号"""
    source: SignalSource
    symbol: str
    timestamp: datetime
    content: str
    tokens: List[TokenInfo]
    confidence: float = 1.0  # 信号置信度
    priority: int = 1  # 固定策略统一优先级
    exchange: str = ""  # 来源交易所
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class UpbitNotice:
    """Upbit公告"""
    id: int
    title: str
    category: str
    listed_at: str
    first_listed_at: str
    need_new_badge: bool
    need_update_badge: bool


@dataclass
class TelegramMessage:
    """Telegram消息"""
    id: int
    text: str
    date: datetime
    channel: str
    sender: Optional[str] = None


@dataclass
class TradingOrder:
    """交易订单"""
    symbol: str
    side: OrderSide
    amount: float
    leverage: int
    stop_loss_percent: float
    status: OrderStatus
    order_id: Optional[str] = None
    filled_price: Optional[float] = None
    created_at: datetime = None
    filled_at: Optional[datetime] = None
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class TradingConfig:
    """交易配置"""
    amount: float
    leverage: int
    stop_loss_percent: float
    testnet: bool = True
    
    
@dataclass
class MarketData:
    """市场数据"""
    symbol: str
    price: float
    volume_24h: float
    change_24h: float
    timestamp: datetime
    exchange: str
    
    
@dataclass
class HistoricalAnalysis:
    """历史分析数据"""
    symbol: str
    exchange: str
    listing_time: datetime
    price_at_listing: float
    timeframe_data: Dict[str, Dict[str, float]]  # timeframe -> {high, low, change, etc}
    market_cap_at_listing: Optional[float] = None
    peak_price: Optional[float] = None
    peak_time: Optional[datetime] = None
    max_drawdown: Optional[float] = None

#!/usr/bin/env python3
"""
币安智能等待器
专门处理币安新币上线的等待和监控逻辑
"""
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
from loguru import logger

from ..models import TradingSignal


class BinanceSmartWaiter:
    """币安智能等待器 - 专门处理新币上线"""
    
    def __init__(self, contract_manager, fixed_strategy):
        self.contract_manager = contract_manager
        self.fixed_strategy = fixed_strategy
        self.waiting_symbols: Dict[str, dict] = {}  # 正在等待的代币
        
        # 等待配置
        self.max_wait_minutes = 30  # 最多等待30分钟
        self.check_interval = 10    # 每10秒检查一次
        self.existence_check_interval = 60  # 合约不存在时每60秒检查一次
        
        logger.info("🕐 币安智能等待器初始化完成")
    
    async def handle_binance_listing_signal(self, signal: TradingSignal, notifier=None) -> Tuple[bool, str]:
        """处理币安上币信号"""
        symbol = signal.symbol
        logger.info(f"🔍 币安智能等待器处理信号: {symbol}")
        
        # 1. 检查合约状态
        status_info = await self.check_contract_comprehensive(symbol)
        
        if status_info['can_trade']:
            # 可以直接交易
            logger.info(f"✅ {symbol} 合约可直接交易")
            return await self.execute_immediate_trade(signal, notifier)
        elif status_info['exists']:
            # 合约存在但不能交易，开始等待
            logger.info(f"⏳ {symbol} 合约存在但不可交易，状态: {status_info['status']}")
            return await self.start_smart_waiting(signal, status_info, notifier)
        else:
            # 合约不存在，记录并定期检查
            logger.info(f"❌ {symbol} 合约不存在，开始存在性等待")
            return await self.start_existence_waiting(signal, notifier)
    
    async def check_contract_comprehensive(self, symbol: str) -> Dict[str, Any]:
        """全面检查合约状态"""
        base_symbol = symbol.upper().replace('USDT', '')
        contract_symbol = f"{base_symbol}USDT"
        
        try:
            # 使用币安exchangeInfo接口检查合约状态
            url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
            
            timeout = aiohttp.ClientTimeout(total=10)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        for symbol_info in data.get('symbols', []):
                            if symbol_info['symbol'] == contract_symbol:
                                status = symbol_info['status']
                                exists = True
                                can_trade = (status == 'TRADING')
                                
                                return {
                                    'exists': exists,
                                    'can_trade': can_trade,
                                    'status': status,
                                    'symbol': contract_symbol
                                }
                        
                        return {
                            'exists': False,
                            'can_trade': False,
                            'status': 'NOT_FOUND',
                            'symbol': contract_symbol
                        }
                    else:
                        logger.error(f"获取exchangeInfo失败: {response.status}")
                        return {
                            'exists': False,
                            'can_trade': False,
                            'status': 'API_ERROR',
                            'symbol': contract_symbol
                        }
                        
        except Exception as e:
            logger.error(f"检查合约状态异常: {e}")
            return {
                'exists': False,
                'can_trade': False,
                'status': 'EXCEPTION',
                'symbol': contract_symbol
            }
    
    async def start_smart_waiting(self, signal: TradingSignal, status_info: dict, notifier=None) -> Tuple[bool, str]:
        """开始智能等待 - 合约存在但不可交易"""
        symbol = signal.symbol
        
        # 发送等待开始通知
        session_id = ""
        if notifier:
            session_id = notifier.send_signal_open_notification(signal, 0)
            if session_id:
                notifier.send_waiting_notification(session_id, f"合约存在但状态为 {status_info['status']}，开始等待交易开放")
        
        # 记录等待信息
        self.waiting_symbols[symbol] = {
            'signal': signal,
            'notifier': notifier,
            'session_id': session_id,
            'start_time': datetime.now(),
            'status': status_info['status'],
            'wait_type': 'status_change'
        }
        
        # 启动监控任务
        asyncio.create_task(self.monitor_contract_status(symbol))
        
        logger.info(f"🕐 开始等待 {symbol} 合约开放交易，当前状态: {status_info['status']}")
        return True, session_id
    
    async def start_existence_waiting(self, signal: TradingSignal, notifier=None) -> Tuple[bool, str]:
        """开始存在性等待 - 合约不存在"""
        symbol = signal.symbol
        
        # 发送等待开始通知
        session_id = ""
        if notifier:
            session_id = notifier.send_signal_open_notification(signal, 0)
            if session_id:
                notifier.send_waiting_notification(session_id, "合约不存在，开始等待合约上线")
        
        # 记录等待信息
        self.waiting_symbols[symbol] = {
            'signal': signal,
            'notifier': notifier,
            'session_id': session_id,
            'start_time': datetime.now(),
            'status': 'NOT_FOUND',
            'wait_type': 'existence'
        }
        
        # 启动监控任务
        asyncio.create_task(self.monitor_contract_existence(symbol))
        
        logger.info(f"🕐 开始等待 {symbol} 合约上线")
        return True, session_id
    
    async def monitor_contract_status(self, symbol: str):
        """监控合约状态变化 - 用于已存在但不可交易的合约"""
        wait_info = self.waiting_symbols.get(symbol)
        if not wait_info:
            return
            
        start_time = wait_info['start_time']
        max_wait = self.max_wait_minutes * 60
        
        while symbol in self.waiting_symbols:
            # 检查是否超时
            if (datetime.now() - start_time).total_seconds() > max_wait:
                logger.warning(f"⏰ {symbol} 状态等待超时，放弃交易")
                await self.cleanup_waiting(symbol, "等待超时")
                break
            
            # 检查合约状态
            status_info = await self.check_contract_comprehensive(symbol)
            
            if status_info['can_trade']:
                # 可以交易了！
                logger.info(f"🚀 {symbol} 合约已开放交易，立即执行开仓")
                await self.execute_waiting_trade(symbol)
                break
            else:
                # 更新状态信息
                if status_info['status'] != wait_info['status']:
                    logger.info(f"📊 {symbol} 合约状态变化: {wait_info['status']} → {status_info['status']}")
                    wait_info['status'] = status_info['status']
            
            # 等待下次检查
            await asyncio.sleep(self.check_interval)
    
    async def monitor_contract_existence(self, symbol: str):
        """监控合约存在性 - 用于不存在的合约"""
        wait_info = self.waiting_symbols.get(symbol)
        if not wait_info:
            return
            
        start_time = wait_info['start_time']
        max_wait = self.max_wait_minutes * 60
        
        while symbol in self.waiting_symbols:
            # 检查是否超时
            if (datetime.now() - start_time).total_seconds() > max_wait:
                logger.warning(f"⏰ {symbol} 存在性等待超时，放弃交易")
                await self.cleanup_waiting(symbol, "等待超时")
                break
            
            # 检查合约是否存在
            status_info = await self.check_contract_comprehensive(symbol)
            
            if status_info['exists']:
                if status_info['can_trade']:
                    # 合约存在且可交易
                    logger.info(f"🚀 {symbol} 合约已上线且可交易，立即执行开仓")
                    await self.execute_waiting_trade(symbol)
                    break
                else:
                    # 合约存在但不可交易，转为状态监控
                    logger.info(f"📊 {symbol} 合约已上线但不可交易，转为状态监控")
                    wait_info['wait_type'] = 'status_change'
                    wait_info['status'] = status_info['status']
                    
                    # 启动状态监控
                    asyncio.create_task(self.monitor_contract_status(symbol))
                    break
            
            # 等待下次检查（存在性检查间隔更长）
            await asyncio.sleep(self.existence_check_interval)
    
    async def execute_waiting_trade(self, symbol: str):
        """执行等待中的交易"""
        wait_info = self.waiting_symbols.get(symbol)
        if not wait_info:
            return
            
        signal = wait_info['signal']
        notifier = wait_info['notifier']
        session_id = wait_info['session_id']
        
        try:
            # 执行交易
            success, _ = await self.execute_immediate_trade(signal, notifier, session_id)
            
            if success:
                logger.info(f"✅ {symbol} 等待交易执行成功")
            else:
                logger.error(f"❌ {symbol} 等待交易执行失败")
                
        except Exception as e:
            logger.error(f"❌ {symbol} 等待交易执行异常: {e}")
            
        finally:
            # 清理等待记录
            await self.cleanup_waiting(symbol, "交易执行完成")
    
    async def execute_immediate_trade(self, signal: TradingSignal, notifier=None, session_id: str = "") -> Tuple[bool, str]:
        """执行立即交易"""
        try:
            # 调用固定策略的原有逻辑
            success, result_session_id = await self.fixed_strategy.process_listing_signal(
                signal=signal,
                exchange=signal.exchange,
                announcement_time=signal.timestamp,
                multiple_exchanges=False,
                notifier=notifier
            )
            
            # 如果有预设的session_id，使用预设的
            final_session_id = session_id if session_id else result_session_id
            
            return success, final_session_id
            
        except Exception as e:
            logger.error(f"执行立即交易异常: {e}")
            return False, session_id
    
    async def cleanup_waiting(self, symbol: str, reason: str):
        """清理等待记录"""
        wait_info = self.waiting_symbols.get(symbol)
        if not wait_info:
            return
            
        # 发送失败通知
        notifier = wait_info.get('notifier')
        session_id = wait_info.get('session_id')
        
        if notifier and session_id:
            notifier.send_trading_failed_notification(session_id, f"等待失败: {reason}")
        
        # 移除等待记录
        del self.waiting_symbols[symbol]
        logger.info(f"🗑️ 清理 {symbol} 等待记录: {reason}")
    
    def get_waiting_status(self) -> Dict[str, Any]:
        """获取当前等待状态"""
        return {
            'waiting_count': len(self.waiting_symbols),
            'waiting_symbols': {
                symbol: {
                    'start_time': info['start_time'].isoformat(),
                    'status': info['status'],
                    'wait_type': info['wait_type'],
                    'elapsed_seconds': (datetime.now() - info['start_time']).total_seconds()
                }
                for symbol, info in self.waiting_symbols.items()
            }
        }

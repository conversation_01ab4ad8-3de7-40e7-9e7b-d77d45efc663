# 固定交易策略系统

## 🎯 策略概述

本系统实施全新的固定交易策略，替代了之前的差异化策略。所有交易所的上币信号都使用相同的固定策略进行处理。

## 📋 策略规则

### 🔔 触发条件
- 电报频道监控到上币信息
- 立即检查币安合约交易对缓存
- 如果代币存在 → 立即开仓

### 💰 开仓参数
- **固定金额**: 100 USDT
- **合约倍数**: 10倍
- **仓位模式**: 逐仓 (ISOLATED)
- **方向**: 做多 (LONG)

### ⏰ 平仓规则

#### 第一次平仓 (56秒后)
- **时间**: 公告时间 + 56秒
- **比例**: 平仓80%仓位
- **剩余**: 保留20%仓位

#### 剩余20%仓位管理
1. **回撤平仓**: 如果价格在5分钟内从峰值回落超过20% → 立即平仓
2. **时间平仓**: 如果5分钟内回落≤20% → 公告时间5分钟后平仓
3. **手动管理**: 如果多个交易所上线同一代币 → 转为手动平仓

## 🏗️ 系统架构

### 核心组件

1. **FixedTradingStrategy** (`src/trading/fixed_strategy.py`)
   - 固定策略核心逻辑
   - 仓位管理和平仓控制
   - 多交易所检测

2. **FixedSignalProcessor** (`src/core/fixed_signal_processor.py`)
   - 专门处理上币信号
   - 信号过滤和冷却控制
   - 交易所信息提取

3. **ContractManager** (`src/trading/contract_manager.py`)
   - 币安合约交易对管理
   - 合约可用性检查

### 已删除的旧组件
- `src/trading/binance_futures.py`
- `src/core/exchange_priority.py`
- `src/core/exchange_strategies.py`
- `src/core/position_manager.py`
- `src/core/signal_processor.py`
- `src/core/take_profit_engine.py`
- `src/core/trading_statistics.py`

## ⚙️ 配置说明

### config.yaml 配置

```yaml
# 固定策略配置
fixed_strategy:
  enabled: true
  amount: 100  # 固定金额 100 USDT
  leverage: 10  # 10倍杠杆
  position_mode: isolated  # 逐仓模式
  first_close_time: 56  # 56秒后平仓80%
  first_close_percentage: 80  # 平仓80%
  monitor_duration: 300  # 5分钟监控期
  drawdown_threshold: 20  # 20%回撤阈值

# 简化的风险控制
risk_control:
  signal_cooldown: 300  # 信号冷却时间 5分钟
  max_errors: 10  # 最大错误次数
  error_window: 300  # 错误时间窗口 5分钟

# 币安交易配置
trading:
  binance:
    api_key: "your_api_key"
    api_secret: "your_api_secret"
    testnet: false  # 使用真实交易
    enabled: true
```

## 🚀 启动系统

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置API密钥
编辑 `.env` 文件：
```
BINANCE_API_KEY=your_api_key
BINANCE_API_SECRET=your_api_secret
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash
TELEGRAM_PHONE=your_phone
```

### 3. 启动机器人
```bash
python main.py
```

### 4. 访问Web界面
打开浏览器访问: http://localhost:8081

## 📊 监控和管理

### Web界面功能
- 实时系统状态监控
- 活跃仓位查看
- 信号历史记录
- 交易统计分析

### 手动操作
- 手动平仓特定代币
- 系统启停控制
- 配置参数调整

## ⚠️ 风险提示

1. **资金风险**: 使用10倍杠杆，请确保有足够的风险承受能力
2. **技术风险**: 系统可能出现故障，建议设置资金上限
3. **市场风险**: 加密货币市场波动剧烈，可能造成损失
4. **流动性风险**: 某些代币可能存在流动性不足的问题

## 🔧 故障排除

### 常见问题

1. **开仓失败**
   - 检查币安API密钥是否正确
   - 确认账户余额充足
   - 验证合约是否存在

2. **信号未处理**
   - 检查Telegram连接状态
   - 确认频道配置正确
   - 查看信号冷却时间

3. **平仓异常**
   - 检查网络连接
   - 确认仓位状态
   - 查看错误日志

### 日志查看
```bash
tail -f trading_bot.log
```

## 📈 性能优化

### 建议设置
- 确保网络连接稳定
- 定期清理历史数据
- 监控系统资源使用
- 设置合理的错误阈值

## 🔄 系统维护

### 定期任务
- 每日检查系统状态
- 每周备份配置文件
- 每月分析交易统计
- 及时更新系统版本

---

**注意**: 本系统仅供学习和研究使用，实际交易请谨慎操作并承担相应风险。

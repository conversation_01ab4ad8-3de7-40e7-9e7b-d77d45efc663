#!/usr/bin/env python3
"""
系统完整性检查脚本
检查所有关键功能模块的状态和连接
"""
import asyncio
import aiohttp
import sqlite3
import json
import yaml
import glob
from datetime import datetime, timedelta
from loguru import logger
from typing import Dict, List, Any

class SystemIntegrityChecker:
    """系统完整性检查器"""
    
    def __init__(self):
        self.config = self._load_config()
        self.results = {}
        
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open('config.yaml', 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            return {}
    
    async def check_all(self) -> Dict[str, Any]:
        """执行所有检查"""
        logger.info("🔍 开始系统完整性检查...")
        
        # 1. 基础文件检查
        await self._check_basic_files()
        
        # 2. 数据库连接检查
        await self._check_database_connections()
        
        # 3. API连接检查
        await self._check_api_connections()
        
        # 4. Telegram会话检查
        await self._check_telegram_session()
        
        # 5. 通知系统检查
        await self._check_notification_system()
        
        # 6. 交易系统检查
        await self._check_trading_system()
        
        # 7. 监控系统检查
        await self._check_monitoring_system()
        
        # 8. Web界面检查
        await self._check_web_interface()
        
        return self.results
    
    async def _check_basic_files(self):
        """检查基础文件"""
        logger.info("📁 检查基础文件...")
        
        required_files = [
            'config.yaml', 'main.py', 'requirements.txt',
            'src/core/fixed_signal_processor.py',
            'src/trading/fixed_strategy.py',
            'src/monitors/telegram_monitor.py',
            'src/notifications/feishu_notifier.py',
            'src/trading/binance_futures.py'
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        self.results['basic_files'] = {
            'status': 'ok' if not missing_files else 'error',
            'missing_files': missing_files,
            'total_required': len(required_files),
            'found': len(required_files) - len(missing_files)
        }
    
    async def _check_database_connections(self):
        """检查数据库连接"""
        logger.info("🗄️ 检查数据库连接...")
        
        db_files = ['exchange_data.db', 'trading_data.db']
        db_status = {}
        
        for db_file in db_files:
            try:
                if os.path.exists(db_file):
                    conn = sqlite3.connect(db_file)
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    tables = cursor.fetchall()
                    conn.close()
                    
                    db_status[db_file] = {
                        'status': 'ok',
                        'tables': len(tables),
                        'size_mb': round(os.path.getsize(db_file) / 1024 / 1024, 2)
                    }
                else:
                    db_status[db_file] = {'status': 'missing'}
                    
            except Exception as e:
                db_status[db_file] = {'status': 'error', 'error': str(e)}
        
        self.results['databases'] = db_status
    
    async def _check_api_connections(self):
        """检查API连接"""
        logger.info("🌐 检查API连接...")
        
        api_tests = [
            ('binance_spot', 'https://api.binance.com/api/v3/ping'),
            ('binance_futures', 'https://fapi.binance.com/fapi/v1/ping'),
            ('telegram', 'https://api.telegram.org'),
        ]
        
        api_status = {}
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
            for name, url in api_tests:
                try:
                    start_time = datetime.now()
                    async with session.get(url) as response:
                        response_time = (datetime.now() - start_time).total_seconds() * 1000
                        
                        api_status[name] = {
                            'status': 'ok' if response.status == 200 else 'error',
                            'response_time_ms': round(response_time, 2),
                            'status_code': response.status
                        }
                except Exception as e:
                    api_status[name] = {'status': 'error', 'error': str(e)}
        
        self.results['api_connections'] = api_status
    
    async def _check_telegram_session(self):
        """检查Telegram会话"""
        logger.info("📱 检查Telegram会话...")
        
        session_files = glob.glob("telegram_session*")
        
        if session_files:
            session_file = session_files[0]
            size = os.path.getsize(session_file)
            modified = datetime.fromtimestamp(os.path.getmtime(session_file))
            
            self.results['telegram_session'] = {
                'status': 'ok',
                'file': session_file,
                'size_bytes': size,
                'last_modified': modified.isoformat(),
                'age_hours': round((datetime.now() - modified).total_seconds() / 3600, 1)
            }
        else:
            self.results['telegram_session'] = {
                'status': 'missing',
                'message': '需要Telegram认证'
            }
    
    async def _check_notification_system(self):
        """检查通知系统"""
        logger.info("🔔 检查通知系统...")
        
        feishu_config = self.config.get('notifications', {}).get('feishu', {})
        
        self.results['notifications'] = {
            'feishu': {
                'enabled': feishu_config.get('enabled', False),
                'webhook_configured': bool(feishu_config.get('webhook_url')),
                'status': 'ok' if feishu_config.get('enabled') and feishu_config.get('webhook_url') else 'warning'
            }
        }
    
    async def _check_trading_system(self):
        """检查交易系统"""
        logger.info("💰 检查交易系统...")
        
        binance_config = self.config.get('trading', {}).get('binance', {})
        fixed_strategy_config = self.config.get('fixed_strategy', {})
        
        binance_status = 'ok' if (binance_config.get('api_key') and binance_config.get('api_secret') and binance_config.get('enabled')) else 'warning'
        strategy_status = 'ok' if fixed_strategy_config.get('enabled') else 'warning'

        self.results['trading_system'] = {
            'binance_api': {
                'status': binance_status,
                'configured': bool(binance_config.get('api_key') and binance_config.get('api_secret')),
                'testnet': binance_config.get('testnet', False),
                'enabled': binance_config.get('enabled', False)
            },
            'fixed_strategy': {
                'status': strategy_status,
                'enabled': fixed_strategy_config.get('enabled', False),
                'amount': fixed_strategy_config.get('amount', 0),
                'leverage': fixed_strategy_config.get('leverage', 0),
                'first_close_time': fixed_strategy_config.get('first_close_time', 0),
                'first_close_percentage': fixed_strategy_config.get('first_close_percentage', 0)
            }
        }
    
    async def _check_monitoring_system(self):
        """检查监控系统"""
        logger.info("👁️ 检查监控系统...")
        
        telegram_config = self.config.get('monitoring', {}).get('telegram', {})
        
        telegram_status = 'ok' if (telegram_config.get('api_id') and telegram_config.get('api_hash')) else 'warning'

        self.results['monitoring_system'] = {
            'telegram': {
                'status': telegram_status,
                'configured': bool(telegram_config.get('api_id') and telegram_config.get('api_hash')),
                'channels': len(telegram_config.get('channels', [])),
                'keywords': len(telegram_config.get('keywords', [])),
                'exclude_keywords': len(telegram_config.get('exclude_keywords', []))
            }
        }
    
    async def _check_web_interface(self):
        """检查Web界面"""
        logger.info("🌐 检查Web界面...")
        
        web_config = self.config.get('web', {})
        port = web_config.get('port', 8081)
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                async with session.get(f'http://localhost:{port}/api/status') as response:
                    self.results['web_interface'] = {
                        'status': 'ok' if response.status == 200 else 'error',
                        'port': port,
                        'response_code': response.status
                    }
        except Exception as e:
            self.results['web_interface'] = {
                'status': 'error',
                'port': port,
                'error': str(e)
            }

async def main():
    """主函数"""
    print("🔍 系统完整性检查")
    print("=" * 60)
    
    checker = SystemIntegrityChecker()
    results = await checker.check_all()
    
    # 输出结果
    print("\n📊 检查结果:")
    print("=" * 60)
    
    for category, data in results.items():
        print(f"\n🔸 {category.upper()}:")
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, dict):
                    status = value.get('status', 'unknown')
                    icon = "✅" if status == 'ok' else "⚠️" if status == 'warning' else "❌"
                    print(f"  {icon} {key}: {status}")
                    if status != 'ok' and 'error' in value:
                        print(f"     错误: {value['error']}")
                else:
                    print(f"  • {key}: {value}")
    
    # 总结
    print("\n" + "=" * 60)
    
    # 统计状态
    total_checks = 0
    ok_checks = 0
    warning_checks = 0
    error_checks = 0
    
    def count_status(data):
        nonlocal total_checks, ok_checks, warning_checks, error_checks
        if isinstance(data, dict):
            if 'status' in data:
                total_checks += 1
                status = data['status']
                if status == 'ok':
                    ok_checks += 1
                elif status == 'warning':
                    warning_checks += 1
                else:
                    error_checks += 1
            else:
                for value in data.values():
                    count_status(value)
    
    for data in results.values():
        count_status(data)
    
    print(f"📈 总检查项: {total_checks}")
    print(f"✅ 正常: {ok_checks}")
    print(f"⚠️ 警告: {warning_checks}")
    print(f"❌ 错误: {error_checks}")
    
    if error_checks == 0 and warning_checks == 0:
        print("\n🎉 系统状态良好，所有检查通过！")
        print("\n💡 建议进行运行时配置验证:")
        print("  python runtime_config_check.py    # 详细运行时验证")
        print("  python quick_config_check.py      # 快速配置检查")
        print("  python main.py                    # 启动系统")
    elif error_checks == 0:
        print(f"\n⚠️ 系统基本正常，有 {warning_checks} 个警告项需要注意")
        print("\n🔧 建议进一步检查:")
        print("  python runtime_config_check.py    # 运行时配置验证")
        print("  python quick_config_check.py      # 快速配置检查")
    else:
        print(f"\n❌ 系统存在 {error_checks} 个错误，需要修复")
        print("\n🔧 故障排除建议:")
        print("  python quick_config_check.py      # 快速诊断配置问题")
        print("  python runtime_config_check.py    # 详细运行时验证")

    return 0 if error_checks == 0 else 1

if __name__ == "__main__":
    import os
    exit(asyncio.run(main()))

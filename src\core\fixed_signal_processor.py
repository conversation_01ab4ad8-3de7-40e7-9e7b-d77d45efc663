#!/usr/bin/env python3
"""
固定策略信号处理器
专门处理上币信号并使用固定交易策略
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from collections import defaultdict

from ..models import TradingSignal, SignalSource
from ..trading.fixed_strategy import get_fixed_strategy
from ..notifications.feishu_notifier import FeishuOptimizedNotifier
from .token_blacklist_manager import get_blacklist_manager
from ..database import unified_db, TradingSignalRecord

logger = logging.getLogger(__name__)

class FixedSignalProcessor:
    """固定策略信号处理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.signal_history: List[TradingSignal] = []
        self.processed_signals: Set[str] = set()  # 防止重复处理
        self.symbol_exchanges: Dict[str, Set[str]] = defaultdict(set)  # 跟踪每个代币的交易所
        
        # 初始化黑名单管理器
        self.blacklist_manager = get_blacklist_manager()

        # 初始化优化版通知器
        feishu_config = config.get('notifications', {}).get('feishu', {})
        self.feishu_notifier = FeishuOptimizedNotifier(feishu_config)

        # 信号过滤配置
        self.signal_cooldown = 300  # 5分钟冷却时间
        self.last_signal_time: Dict[str, datetime] = {}
        
        logger.info("🎯 固定策略信号处理器初始化完成")
    
    async def process_listing_signal(self, signal: TradingSignal) -> bool:
        """
        处理上币信号
        
        Args:
            signal: 交易信号
            
        Returns:
            bool: 是否成功处理
        """
        try:
            logger.info(f"🔔 收到上币信号: {signal.symbol} ({signal.source.value})")
            logger.info(f"   公告时间: {signal.timestamp}")
            logger.info(f"   内容: {signal.content[:100]}...")

            # 1. 检查是否为Token Swap消息
            swap_tokens = self.blacklist_manager.process_message(signal.content)
            if swap_tokens:
                logger.warning(f"🚫 检测到Token Swap消息，已将代币加入黑名单: {swap_tokens}")
                # 发送失败通知
                if self.feishu_notifier:
                    session_id = self.feishu_notifier.send_signal_open_notification(signal, 0)
                    if session_id:
                        self.feishu_notifier.send_trading_failed_notification(session_id, "Token Swap检测，已加入黑名单")
                return False

            # 2. 检查代币是否在黑名单中
            if self.blacklist_manager.is_token_blacklisted(signal.symbol):
                logger.warning(f"🚫 代币在黑名单中，跳过交易: {signal.symbol}")
                # 发送失败通知
                if self.feishu_notifier:
                    session_id = self.feishu_notifier.send_signal_open_notification(signal, 0)
                    if session_id:
                        self.feishu_notifier.send_trading_failed_notification(session_id, "代币在黑名单中 (Token Swap)")
                return False

            # 3. 生成信号唯一标识
            signal_id = f"{signal.symbol}_{signal.source.value}_{signal.timestamp.isoformat()}"

            # 4. 检查是否已处理过
            if signal_id in self.processed_signals:
                logger.warning(f"⚠️ 信号已处理过，跳过: {signal.symbol}")
                return False

            # 5. 检查信号冷却时间
            if not self._check_signal_cooldown(signal):
                return False
            
            # 4. 保存信号到数据库
            signal_record = TradingSignalRecord(
                source=signal.source.value,
                symbol=signal.symbol,
                timestamp=signal.timestamp,
                content=signal.content,
                exchange=signal.exchange,
                confidence=signal.confidence,
                priority=signal.priority,
                metadata=signal.metadata,
                processed=False
            )

            signal_db_id = unified_db.save_trading_signal(signal_record)
            if signal_db_id:
                logger.info(f"信号已保存到数据库 (ID: {signal_db_id})")

            # 5. 记录信号到内存（用于冷却检查）
            self.signal_history.append(signal)
            self.processed_signals.add(signal_id)
            self.last_signal_time[signal.symbol] = signal.timestamp
            
            # 6. 信号通知将在固定策略中发送
            # 不在这里发送通知，避免重复

            # 7. 跟踪交易所信息
            exchange = self._extract_exchange_from_signal(signal)
            if exchange:
                self.symbol_exchanges[signal.symbol].add(exchange)
                logger.info(f"📊 {signal.symbol} 已在交易所上线: {list(self.symbol_exchanges[signal.symbol])}")
            
            # 8. 检查是否多个交易所上线
            multiple_exchanges = len(self.symbol_exchanges[signal.symbol]) > 1

            # 9. 使用固定策略处理信号
            fixed_strategy = get_fixed_strategy()
            if fixed_strategy:
                # 将通知器传递给固定策略，让策略处理通知
                success, session_id = await fixed_strategy.process_listing_signal(
                    signal=signal,
                    exchange=exchange or 'unknown',
                    announcement_time=signal.timestamp,
                    multiple_exchanges=multiple_exchanges,
                    notifier=self.feishu_notifier
                )

                if success:
                    logger.info(f"✅ 固定策略处理成功: {signal.symbol}")
                    # 通知已在固定策略中发送

                else:
                    logger.warning(f"❌ 固定策略处理失败: {signal.symbol}")
                    # 失败通知已在固定策略中发送
                
                return success
            else:
                logger.error("❌ 固定策略未初始化")
                return False
                
        except Exception as e:
            logger.error(f"❌ 处理上币信号失败: {signal.symbol} - {e}")
            
            # 发送错误通知
            try:
                await self.feishu_notifier.send_message(
                    f"⚠️ 信号处理异常\n"
                    f"代币: {signal.symbol}\n"
                    f"错误: {str(e)}"
                )
            except:
                pass
                
            return False
    
    def _check_signal_cooldown(self, signal: TradingSignal) -> bool:
        """检查信号冷却时间"""
        if signal.symbol in self.last_signal_time:
            time_diff = (signal.timestamp - self.last_signal_time[signal.symbol]).total_seconds()
            if time_diff < self.signal_cooldown:
                logger.warning(f"⏰ 信号冷却中: {signal.symbol} (还需等待 {self.signal_cooldown - time_diff:.0f} 秒)")
                return False
        return True
    
    def _extract_exchange_from_signal(self, signal: TradingSignal) -> Optional[str]:
        """从信号中提取交易所信息"""
        try:
            content_lower = signal.content.lower()
            
            # 常见交易所关键词
            exchanges = {
                'binance': ['binance', '币安'],
                'okx': ['okx', 'okex'],
                'bybit': ['bybit'],
                'gate': ['gate.io', 'gate'],
                'huobi': ['huobi', '火币'],
                'kucoin': ['kucoin'],
                'mexc': ['mexc'],
                'bitget': ['bitget'],
                'coinbase': ['coinbase'],
                'kraken': ['kraken'],
                'upbit': ['upbit'],
                'bithumb': ['bithumb']
            }
            
            for exchange, keywords in exchanges.items():
                for keyword in keywords:
                    if keyword in content_lower:
                        return exchange
            
            # 如果没有找到，尝试从信号源推断
            if signal.source == SignalSource.TELEGRAM:
                # 可以根据频道名称推断交易所
                pass
            
            return None
            
        except Exception as e:
            logger.error(f"提取交易所信息失败: {e}")
            return None
    
    def get_signal_statistics(self) -> Dict:
        """获取信号统计"""
        try:
            total_signals = len(self.signal_history)
            
            # 按来源统计
            source_stats = defaultdict(int)
            for signal in self.signal_history:
                source_stats[signal.source.value] += 1
            
            # 按时间统计（最近24小时）
            now = datetime.now()
            recent_signals = [
                s for s in self.signal_history 
                if (now - s.timestamp).total_seconds() < 86400
            ]
            
            # 交易所统计
            exchange_stats = {}
            for symbol, exchanges in self.symbol_exchanges.items():
                for exchange in exchanges:
                    if exchange not in exchange_stats:
                        exchange_stats[exchange] = 0
                    exchange_stats[exchange] += 1
            
            return {
                'total_signals': total_signals,
                'recent_24h': len(recent_signals),
                'by_source': dict(source_stats),
                'by_exchange': exchange_stats,
                'unique_symbols': len(self.symbol_exchanges),
                'multi_exchange_symbols': len([
                    symbol for symbol, exchanges in self.symbol_exchanges.items()
                    if len(exchanges) > 1
                ])
            }
            
        except Exception as e:
            logger.error(f"获取信号统计失败: {e}")
            return {}
    
    def get_recent_signals(self, limit: int = 50) -> List[Dict]:
        """获取最近的信号"""
        try:
            recent_signals = sorted(
                self.signal_history, 
                key=lambda x: x.timestamp, 
                reverse=True
            )[:limit]
            
            result = []
            for signal in recent_signals:
                result.append({
                    'symbol': signal.symbol,
                    'source': signal.source.value,
                    'timestamp': signal.timestamp.isoformat(),
                    'content': signal.content[:200],
                    'confidence': signal.confidence,
                    'exchanges': list(self.symbol_exchanges.get(signal.symbol, set()))
                })
            
            return result
            
        except Exception as e:
            logger.error(f"获取最近信号失败: {e}")
            return []
    
    async def cleanup_old_data(self):
        """清理旧数据"""
        try:
            now = datetime.now()
            cutoff_time = now - timedelta(days=7)  # 保留7天数据
            
            # 清理信号历史
            old_count = len(self.signal_history)
            self.signal_history = [
                s for s in self.signal_history 
                if s.timestamp > cutoff_time
            ]
            new_count = len(self.signal_history)
            
            # 清理已处理信号集合（保留最近的）
            if len(self.processed_signals) > 10000:
                # 只保留最近的5000个
                recent_signals = sorted(self.signal_history, key=lambda x: x.timestamp, reverse=True)[:5000]
                recent_ids = set()
                for signal in recent_signals:
                    signal_id = f"{signal.symbol}_{signal.source.value}_{signal.timestamp.isoformat()}"
                    recent_ids.add(signal_id)
                self.processed_signals = recent_ids
            
            # 清理冷却时间记录
            self.last_signal_time = {
                symbol: timestamp for symbol, timestamp in self.last_signal_time.items()
                if timestamp > cutoff_time
            }
            
            if old_count != new_count:
                logger.info(f"🧹 清理旧数据: 信号历史 {old_count} -> {new_count}")
                
        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
    
    async def start_cleanup_task(self):
        """启动清理任务"""
        while True:
            try:
                await asyncio.sleep(3600)  # 每小时清理一次
                await self.cleanup_old_data()
            except Exception as e:
                logger.error(f"清理任务异常: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟再试

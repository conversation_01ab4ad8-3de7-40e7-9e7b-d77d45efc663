#!/usr/bin/env python3
"""
运行时配置验证工具
独立运行，验证系统配置的实际可用性
"""
import asyncio
import sys
import json
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from src.utils import load_config
from src.core.runtime_config_validator import RuntimeConfigValidator


def print_validation_result(name: str, result: dict, indent: int = 0):
    """打印验证结果"""
    prefix = "  " * indent
    status = result.get('status', 'unknown')
    message = result.get('message', '无消息')
    
    # 状态图标
    status_icons = {
        'ok': '✅',
        'warning': '⚠️',
        'error': '❌',
        'disabled': '⏸️',
        'no_data': '📭',
        'unknown': '❓'
    }
    
    icon = status_icons.get(status, '❓')
    print(f"{prefix}{icon} {name}: {message}")
    
    # 打印详细信息
    details = result.get('details', {})
    if details and isinstance(details, dict):
        for key, value in details.items():
            if isinstance(value, dict):
                print(f"{prefix}  • {key}:")
                for sub_key, sub_value in value.items():
                    print(f"{prefix}    - {sub_key}: {sub_value}")
            elif isinstance(value, list):
                print(f"{prefix}  • {key}: {', '.join(map(str, value))}")
            else:
                print(f"{prefix}  • {key}: {value}")


async def main():
    """主函数"""
    print("🔍 运行时配置验证工具")
    print("=" * 50)
    
    try:
        # 1. 加载配置
        print("📋 加载配置文件...")
        config = load_config()
        if not config:
            print("❌ 配置文件加载失败")
            return 1
        
        print("✅ 配置文件加载成功")
        print()
        
        # 2. 创建验证器
        print("🔧 初始化运行时配置验证器...")
        validator = RuntimeConfigValidator(config)
        print("✅ 验证器初始化完成")
        print()
        
        # 3. 执行验证
        print("🔍 开始运行时配置验证...")
        print("-" * 30)
        
        start_time = datetime.now()
        results = await validator.validate_all_runtime_configs()
        end_time = datetime.now()
        
        # 4. 显示结果
        print()
        print("📊 验证结果:")
        print("=" * 30)
        
        overall_status = results.get('overall_status', 'unknown')
        timestamp = results.get('timestamp', 'unknown')
        validations = results.get('validations', {})
        
        print(f"总体状态: {overall_status}")
        print(f"验证时间: {timestamp}")
        print(f"验证耗时: {(end_time - start_time).total_seconds():.2f}秒")
        print()
        
        # 按类别显示验证结果
        categories = {
            '交易配置': ['binance_api', 'copy_trading'],
            '监控配置': ['telegram'],
            '通知配置': ['feishu'],
            '网络连接': ['network'],
            '系统资源': ['ports']
        }
        
        for category, validation_keys in categories.items():
            print(f"📁 {category}:")
            for key in validation_keys:
                if key in validations:
                    print_validation_result(key, validations[key], indent=1)
            print()
        
        # 5. 显示其他验证结果
        other_validations = {k: v for k, v in validations.items() 
                           if k not in [item for sublist in categories.values() for item in sublist]}
        
        if other_validations:
            print("📁 其他验证:")
            for key, result in other_validations.items():
                print_validation_result(key, result, indent=1)
            print()
        
        # 6. 总结和建议
        print("📋 验证总结:")
        print("-" * 20)
        
        error_count = sum(1 for v in validations.values() if v.get('status') == 'error')
        warning_count = sum(1 for v in validations.values() if v.get('status') == 'warning')
        ok_count = sum(1 for v in validations.values() if v.get('status') == 'ok')
        disabled_count = sum(1 for v in validations.values() if v.get('status') == 'disabled')
        
        print(f"✅ 正常: {ok_count}")
        print(f"⚠️ 警告: {warning_count}")
        print(f"❌ 错误: {error_count}")
        print(f"⏸️ 禁用: {disabled_count}")
        print()
        
        # 7. 建议
        if overall_status == 'error':
            print("🚨 发现严重问题，建议修复后再启动系统:")
            for name, result in validations.items():
                if result.get('status') == 'error':
                    print(f"  • {name}: {result.get('message', '未知错误')}")
            print()
            return 1
            
        elif overall_status == 'warning':
            print("⚠️ 发现警告，系统可以启动但建议检查:")
            for name, result in validations.items():
                if result.get('status') == 'warning':
                    print(f"  • {name}: {result.get('message', '未知警告')}")
            print()
            
        else:
            print("🎉 所有配置验证通过，系统可以正常启动！")
            print()
        
        # 8. 保存验证结果
        result_file = f"runtime_validation_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"📄 验证结果已保存到: {result_file}")
        
        return 0 if overall_status in ['ok', 'warning'] else 1
        
    except Exception as e:
        print(f"❌ 验证过程异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断验证")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        sys.exit(1)

# 🚀 智能交易机器人 - 最终版本

一个基于Telegram信号监控的自动化加密货币交易机器人，支持多交易所监控和固定策略交易。

**🎉 版本状态**: 生产就绪 | **📊 功能完整度**: 100% | **🔧 系统状态**: 稳定运行

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![Docker](https://img.shields.io/badge/Docker-Ready-green.svg)](https://docker.com)
[![Web UI](https://img.shields.io/badge/Web_UI-Available-brightgreen.svg)](http://localhost:8081)

## ✨ 核心特性

### 🎯 **固定交易策略**
- **统一策略**: 所有交易所信号使用相同的固定策略
- **参数配置**: 可配置投入金额、杠杆倍数、仓位模式
- **智能平仓**: 分阶段平仓策略，智能仓位管理
- **风险控制**: 可配置回撤阈值和监控周期

### 📡 **多源信号监控**
- **Telegram监控**: 实时监控多个频道的上币信号
- **交易所支持**: Upbit、Binance、Coinbase、Bithumb
- **智能过滤**: 自动过滤Token Swap等无效信号
- **黑名单管理**: 动态维护代币黑名单

### 🔔 **完整通知系统**
- **三条通知方案**: 信号开仓 → 交易完成 → 历史统计
- **飞书集成**: 实时推送交易结果和系统状态
- **会话跟踪**: 完整的交易生命周期管理

### 🌐 **Web管理界面**
- **实时监控**: 系统状态、交易统计、信号历史
- **管理功能**: 系统控制、配置管理、日志查看
- **数据分析**: 历史表现分析、盈亏统计
- **响应式设计**: 支持桌面和移动端访问

## 🚀 快速开始

### 方式一：本地运行（推荐）

1. **克隆项目**
```bash
git clone <repository-url>
cd goldbot
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置系统**
```bash
# 编辑配置文件
cp config.yaml.example config.yaml
# 根据需要修改配置
```

4. **启动系统**
```bash
python start_bot.py
```

5. **访问Web界面**
```
http://localhost:8081
```

### 方式二：Docker部署

1. **构建并启动**
```bash
docker-compose up -d
```

2. **查看日志**
```bash
docker-compose logs -f goldbot
```

3. **访问Web界面**
```
http://localhost:8080
```

## 📋 系统要求

- **Python**: 3.11+
- **内存**: 最少512MB，推荐1GB+
- **存储**: 最少1GB可用空间
- **网络**: 稳定的互联网连接
- **操作系统**: Windows 10+, Linux, macOS

## 🔧 配置说明

### 核心配置 (config.yaml)

```yaml
# 固定交易策略
fixed_strategy:
  enabled: true
  amount: xxx          # 投入金额 (USDT)
  leverage: xx         # 杠杆倍数
  first_close_time: xx # 首次平仓时间 (秒)
  first_close_percentage: xx # 首次平仓比例 (%)

# Telegram监控
monitoring:
  telegram:
    api_id: "your_api_id"
    api_hash: "your_api_hash"
    phone: "your_phone_number"
    channels:
      - "@BWEnews"
      - "@binance_announcements"

# 币安交易
trading:
  binance:
    api_key: "your_api_key"
    api_secret: "your_api_secret"
    testnet: false

# 飞书通知
notifications:
  feishu:
    enabled: true
    webhook_url: "your_webhook_url"
```

## 📊 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Telegram      │    │   信号处理器     │    │   交易执行器     │
│   监控器        │───▶│   固定策略      │───▶│   币安期货      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web管理界面   │    │   通知系统      │    │   数据存储      │
│   实时监控      │    │   飞书推送      │    │   SQLite数据库  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🎯 使用流程

1. **信号监控**: Telegram监控器实时监控多个频道
2. **信号处理**: 提取代币信息，检查黑名单和冷却时间
3. **合约检查**: 验证交易所期货合约是否存在
4. **开仓交易**: 使用固定策略执行开仓操作
5. **仓位管理**: 分阶段平仓，智能监控剩余仓位
6. **智能平仓**: 根据配置的回撤阈值或时间触发平仓
7. **通知推送**: 实时推送交易结果到通知系统

## 📈 监控和管理

### Web管理界面功能
- **系统状态**: 实时显示系统运行状态
- **交易统计**: 总交易数、胜率、盈亏统计
- **信号历史**: 最近的交易信号记录
- **交易历史**: 详细的交易记录和分析
- **系统控制**: 启动/停止系统功能

### 通知系统内容
- **信号开仓**: 代币信息、开仓价格、投入金额
- **交易完成**: 平仓价格、收益率、交易时长
- **历史统计**: 总交易数、胜率、总盈亏

## 🛠️ 故障排除

### 常见问题

1. **Telegram连接失败**
   - 检查网络连接
   - 运行 `python setup_telegram_session.py` 重新认证

2. **币安API错误**
   - 验证API密钥和权限
   - 检查IP白名单设置

3. **端口被占用**
   - 修改 `config.yaml` 中的 `web.port` 配置
   - 或终止占用端口的程序

4. **数据库错误**
   - 删除损坏的数据库文件，系统会自动重建

## 📚 文档

- [Docker部署指南](DOCKER_DEPLOYMENT.md)
- [Telegram设置指南](TELEGRAM_SETUP.md)
- [固定策略详解](docs/FIXED_STRATEGY_GUIDE.md)
- [配置管理指南](docs/CONFIG_MANAGEMENT.md)
- [项目结构说明](FINAL_PROJECT_STRUCTURE.md)

## 🤝 支持

如有问题或建议，请通过以下方式联系：
- 📧 Email: <EMAIL>
- 💬 Telegram: @support_bot
- 🐛 Issues: GitHub Issues

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

---

**⚠️ 风险提示**: 加密货币交易存在高风险，请谨慎投资，本软件仅供学习和研究使用。

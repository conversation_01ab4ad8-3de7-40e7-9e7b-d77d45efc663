#!/usr/bin/env python3
"""
生成telegram_session.session文件
用于所有Telegram功能（监控、数据收集等）
"""
import asyncio
import os
from telethon import TelegramClient
from loguru import logger

# 从配置文件读取的Telegram配置
API_ID = 26145597
API_HASH = "859206f58db62ec957089a7e9ff11d38"
PHONE = "+8613375386798"
SESSION_NAME = "telegram_session"

async def create_session():
    """创建Telegram session"""
    try:
        logger.info(f"开始创建 {SESSION_NAME}.session...")
        
        # 检查文件是否已存在
        if os.path.exists(f"{SESSION_NAME}.session"):
            response = input(f"{SESSION_NAME}.session 已存在，是否覆盖？(y/N): ")
            if response.lower() != 'y':
                logger.info(f"跳过 {SESSION_NAME}.session")
                return True
        
        # 创建客户端
        client = TelegramClient(SESSION_NAME, API_ID, API_HASH)
        
        # 连接并认证
        print(f"\n使用电话号码: {PHONE}")
        print("将会发送验证码到你的Telegram...")
        print("请准备接收验证码...")
        
        await client.start(phone=PHONE)
        
        # 获取当前用户信息
        me = await client.get_me()
        logger.info(f"认证成功! 用户: {me.first_name} (@{me.username})")
        
        # 断开连接
        await client.disconnect()
        
        logger.info(f"✅ {SESSION_NAME}.session 创建成功!")
        
        # 显示使用说明
        print(f"\n📦 Docker部署说明:")
        print(f"1. 将 {SESSION_NAME}.session 复制到服务器项目目录")
        print(f"2. 运行: ./deploy.sh 或 deploy.bat")
        print(f"3. 会话文件将自动用于所有Telegram功能")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建 {SESSION_NAME}.session 失败: {e}")
        return False

async def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("🤖 Goldbot Telegram 会话文件生成器")
    logger.info("统一会话文件，用于所有Telegram功能")
    logger.info("=" * 60)
    
    print(f"\n📋 将要生成: {SESSION_NAME}.session")
    print(f"📱 使用的电话号码: {PHONE}")
    print("⚠️  请确保:")
    print("   1. 手机能正常接收Telegram验证码")
    print("   2. 网络连接正常")
    print("   3. 已安装Telegram客户端")
    
    response = input(f"\n是否继续？(y/N): ")
    if response.lower() != 'y':
        logger.info("用户取消操作")
        return
    
    try:
        success = await create_session()
        if success:
            logger.info("✅ 会话文件生成成功！")
        else:
            logger.error("❌ 会话文件生成失败！")
    except KeyboardInterrupt:
        logger.warning("用户中断操作")
    except Exception as e:
        logger.error(f"程序异常: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 操作被用户中断")
    except Exception as e:
        logger.error(f"程序异常: {e}")

"""
交易路由器 - 根据配置选择交易模式
"""
import asyncio
from typing import Dict, Any, Optional
from loguru import logger
from datetime import datetime

from .interfaces.trader_interface import TraderInterface, TradingResult
from ..models import TradingSignal


class TradingRouter:
    """交易路由器 - 统一管理普通交易和跟单交易"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.trading_config = config.get('trading', {})
        self.copy_config = config.get('copy_trading', {})
        
        # 交易器实例
        self.normal_trader: Optional[TraderInterface] = None
        self.copy_trader: Optional[TraderInterface] = None
        
        # 当前交易模式
        self.current_mode = self._get_trading_mode()
        
        logger.info(f"交易路由器初始化完成 - 当前模式: {self.current_mode}")
    
    def _get_trading_mode(self) -> str:
        """获取当前交易模式"""
        # 从配置文件获取交易模式
        config_mode = self.trading_config.get('mode', 'normal')
        if config_mode in ['normal', 'copy_trading']:
            return config_mode

        # 默认值
        return 'normal'
    
    async def initialize(self, normal_trader: TraderInterface, copy_trader: TraderInterface = None):
        """
        初始化交易器
        
        Args:
            normal_trader: 普通交易器实例
            copy_trader: 跟单交易器实例 (可选)
        """
        self.normal_trader = normal_trader
        self.copy_trader = copy_trader
        
        # 验证当前模式的交易器是否可用
        if self.current_mode == 'copy_trading' and not self.copy_trader:
            logger.warning("⚠️ 跟单模式不可用，切换到普通模式")
            self.current_mode = 'normal'
        
        logger.info(f"✅ 交易路由器初始化完成 - 模式: {self.current_mode}")
    
    async def execute_trade(self, signal: TradingSignal, **kwargs) -> TradingResult:
        """
        执行交易 - 根据当前模式路由到对应的交易器
        
        Args:
            signal: 交易信号
            **kwargs: 额外参数
            
        Returns:
            TradingResult: 交易结果
        """
        try:
            # 选择交易器
            trader = self._get_active_trader()
            if not trader:
                return TradingResult(
                    success=False,
                    message=f"交易器不可用 - 模式: {self.current_mode}"
                )
            
            # 记录交易开始
            logger.info(f"🚀 开始执行交易 - 模式: {self.current_mode}, 代币: {signal.symbol}")
            
            # 执行交易
            start_time = datetime.now()
            result_dict = await trader.execute_trade(signal, **kwargs)
            end_time = datetime.now()
            
            # 转换结果
            result = TradingResult(
                success=result_dict.get('success', False),
                order_id=result_dict.get('order_id'),
                price=result_dict.get('price', 0.0),
                quantity=result_dict.get('quantity', 0.0),
                mode=self.current_mode,
                message=result_dict.get('message', '')
            )
            
            # 记录执行时间
            execution_time = (end_time - start_time).total_seconds() * 1000
            
            if result.success:
                logger.info(f"✅ 交易执行成功 - {signal.symbol}, 模式: {self.current_mode}, 耗时: {execution_time:.1f}ms")
            else:
                logger.error(f"❌ 交易执行失败 - {signal.symbol}, 模式: {self.current_mode}, 原因: {result.message}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 交易路由异常: {e}")
            return TradingResult(
                success=False,
                mode=self.current_mode,
                message=f"交易路由异常: {str(e)}"
            )
    
    async def close_position(self, symbol: str, percentage: float = 100.0) -> TradingResult:
        """
        平仓
        
        Args:
            symbol: 交易对符号
            percentage: 平仓百分比
            
        Returns:
            TradingResult: 平仓结果
        """
        try:
            trader = self._get_active_trader()
            if not trader:
                return TradingResult(
                    success=False,
                    message=f"交易器不可用 - 模式: {self.current_mode}"
                )
            
            logger.info(f"📤 开始平仓 - {symbol}, 比例: {percentage}%, 模式: {self.current_mode}")
            
            result_dict = await trader.close_position(symbol, percentage)
            
            result = TradingResult(
                success=result_dict.get('success', False),
                order_id=result_dict.get('order_id'),
                price=result_dict.get('price', 0.0),
                quantity=result_dict.get('quantity', 0.0),
                mode=self.current_mode,
                message=result_dict.get('message', '')
            )
            
            if result.success:
                logger.info(f"✅ 平仓成功 - {symbol}, 模式: {self.current_mode}")
            else:
                logger.error(f"❌ 平仓失败 - {symbol}, 原因: {result.message}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 平仓异常: {e}")
            return TradingResult(
                success=False,
                mode=self.current_mode,
                message=f"平仓异常: {str(e)}"
            )
    
    async def switch_mode(self, new_mode: str) -> bool:
        """
        切换交易模式
        
        Args:
            new_mode: 新的交易模式 ('normal' 或 'copy_trading')
            
        Returns:
            bool: 切换是否成功
        """
        if new_mode not in ['normal', 'copy_trading']:
            logger.error(f"❌ 无效的交易模式: {new_mode}")
            return False
        
        if new_mode == 'copy_trading' and not self.copy_trader:
            logger.error("❌ 跟单交易器未初始化，无法切换到跟单模式")
            return False
        
        old_mode = self.current_mode
        self.current_mode = new_mode
        
        logger.info(f"🔄 交易模式已切换: {old_mode} → {new_mode}")
        return True
    
    def _get_active_trader(self) -> Optional[TraderInterface]:
        """获取当前活跃的交易器"""
        if self.current_mode == 'copy_trading':
            return self.copy_trader
        else:
            return self.normal_trader
    
    def get_status(self) -> Dict[str, Any]:
        """获取路由器状态"""
        return {
            'current_mode': self.current_mode,
            'normal_trader_available': self.normal_trader is not None,
            'copy_trader_available': self.copy_trader is not None,
            'active_trader_type': self._get_active_trader().get_trader_type() if self._get_active_trader() else None
        }
    
    async def get_position_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取仓位信息"""
        trader = self._get_active_trader()
        if trader:
            return await trader.get_position_info(symbol)
        return None
    
    async def get_account_balance(self) -> Optional[Dict[str, Any]]:
        """获取账户余额"""
        trader = self._get_active_trader()
        if trader:
            return await trader.get_account_balance()
        return None

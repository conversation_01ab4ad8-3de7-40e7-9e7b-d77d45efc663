#!/usr/bin/env python3
"""
优化版通知器功能测试
只测试通知功能，不涉及实际交易
"""
import asyncio
import sys
import os
from datetime import datetime
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.utils import load_config, setup_logger
from src.notifications.feishu_notifier import FeishuOptimizedNotifier
from src.models import TradingSignal, SignalSource, TokenInfo


async def test_complete_notification_flow():
    """测试完整的3条通知流程"""
    
    # 设置日志
    setup_logger(level="DEBUG")
    
    logger.info("=== 优化版通知器功能测试 ===")
    
    try:
        # 加载配置
        config = load_config("config.yaml")
        
        # 检查飞书配置
        feishu_config = config.get('notifications', {}).get('feishu', {})
        if not feishu_config.get('webhook_url'):
            logger.error("飞书Webhook URL未配置")
            return False
        
        # 初始化优化版通知器
        notifier = FeishuOptimizedNotifier(feishu_config)
        
        if not notifier.enabled:
            logger.error("优化版飞书通知器未启用")
            return False
        
        logger.info("优化版飞书通知器初始化成功")
        
        # 创建测试信号
        test_signal = TradingSignal(
            source=SignalSource.TELEGRAM,
            symbol="BTC",
            timestamp=datetime.now(),
            content="🚀 Binance Will List BTC (Bitcoin) perpetual futures. Trading will start immediately. This is a major listing announcement for Bitcoin futures trading on Binance platform.",
            tokens=[TokenInfo(symbol="BTC", market_cap=1000000000000)],
            confidence=0.95,
            exchange="binance",
            metadata={'market_cap': 1000000000000}
        )
        
        logger.info("=== 测试完整的3条通知流程 ===")
        
        # 通知1/3: 信号开仓通知
        logger.info("1️⃣ 发送信号开仓通知")
        session_id = notifier.send_signal_open_notification(test_signal, 45000.0)
        
        if not session_id:
            logger.error("❌ 信号开仓通知发送失败")
            return False
        
        logger.info(f"✅ 信号开仓通知发送成功: {session_id}")
        
        await asyncio.sleep(3)
        
        # 模拟56秒后部分平仓80%
        logger.info("⏳ 模拟56秒后部分平仓80%")
        notifier.update_session_partial_close(session_id, 46500.0, 80)
        logger.info("✅ 部分平仓信息已更新")
        
        await asyncio.sleep(2)
        
        # 通知2/3: 交易完成通知
        logger.info("2️⃣ 发送交易完成通知")
        result2 = notifier.send_trading_complete_notification(session_id, 46000.0, "5分钟到期")
        
        if not result2:
            logger.error("❌ 交易完成通知发送失败")
            return False
        
        logger.info("✅ 交易完成通知发送成功")
        
        await asyncio.sleep(3)
        
        # 通知3/3: 历史统计通知
        logger.info("3️⃣ 发送历史统计通知")
        result3 = notifier.send_statistics_notification()
        
        if not result3:
            logger.error("❌ 历史统计通知发送失败")
            return False
        
        logger.info("✅ 历史统计通知发送成功")
        
        logger.info("🎉 完整的3条通知流程测试成功！")
        return True
        
    except Exception as e:
        logger.error(f"通知流程测试异常: {e}")
        return False


async def test_failed_notification_flow():
    """测试失败通知流程"""
    
    logger.info("=== 测试失败通知流程 ===")
    
    try:
        config = load_config("config.yaml")
        feishu_config = config.get('notifications', {}).get('feishu', {})
        notifier = FeishuOptimizedNotifier(feishu_config)
        
        # 创建失败信号
        failed_signal = TradingSignal(
            source=SignalSource.TELEGRAM,
            symbol="FAILED",
            timestamp=datetime.now(),
            content="This is a test signal that should fail",
            tokens=[TokenInfo(symbol="FAILED", market_cap=1000000)],
            confidence=0.8,
            exchange="binance",
            metadata={'market_cap': 1000000}
        )
        
        logger.info("1️⃣ 发送失败信号开仓通知")
        session_id = notifier.send_signal_open_notification(failed_signal, 0)  # 价格为0表示未开仓
        
        if not session_id:
            logger.error("❌ 失败信号开仓通知发送失败")
            return False
        
        logger.info(f"✅ 失败信号开仓通知发送成功: {session_id}")
        
        await asyncio.sleep(2)
        
        logger.info("2️⃣ 发送交易失败通知")
        result2 = notifier.send_trading_failed_notification(session_id, "合约不存在或网络错误")
        
        if not result2:
            logger.error("❌ 交易失败通知发送失败")
            return False
        
        logger.info("✅ 交易失败通知发送成功")
        
        await asyncio.sleep(2)
        
        logger.info("3️⃣ 发送统计通知")
        result3 = notifier.send_statistics_notification()
        
        if not result3:
            logger.error("❌ 统计通知发送失败")
            return False
        
        logger.info("✅ 统计通知发送成功")
        
        logger.info("🎉 失败通知流程测试成功！")
        return True
        
    except Exception as e:
        logger.error(f"失败通知流程测试异常: {e}")
        return False


async def test_multiple_trades():
    """测试多个交易的统计"""
    
    logger.info("=== 测试多个交易的统计 ===")
    
    try:
        config = load_config("config.yaml")
        feishu_config = config.get('notifications', {}).get('feishu', {})
        notifier = FeishuOptimizedNotifier(feishu_config)
        
        # 模拟3个交易
        trades = [
            {"symbol": "ETH", "open_price": 3000.0, "close_price": 3150.0, "reason": "5分钟到期"},
            {"symbol": "ADA", "open_price": 1.5, "close_price": 1.4, "reason": "20%回撤止损"},
            {"symbol": "SOL", "open_price": 100.0, "close_price": 105.0, "reason": "5分钟到期"}
        ]
        
        for i, trade in enumerate(trades, 1):
            logger.info(f"模拟交易 {i}: {trade['symbol']}")
            
            # 创建信号
            signal = TradingSignal(
                source=SignalSource.TELEGRAM,
                symbol=trade['symbol'],
                timestamp=datetime.now(),
                content=f"Binance Will List {trade['symbol']} perpetual futures",
                tokens=[TokenInfo(symbol=trade['symbol'], market_cap=10000000000)],
                confidence=0.9,
                exchange="binance",
                metadata={'market_cap': 10000000000}
            )
            
            # 信号开仓通知
            session_id = notifier.send_signal_open_notification(signal, trade['open_price'])
            
            await asyncio.sleep(1)
            
            # 模拟部分平仓
            partial_price = trade['open_price'] * 1.02  # 2%盈利
            notifier.update_session_partial_close(session_id, partial_price, 80)
            
            await asyncio.sleep(1)
            
            # 交易完成通知
            notifier.send_trading_complete_notification(session_id, trade['close_price'], trade['reason'])
            
            await asyncio.sleep(1)
        
        # 最终统计通知
        logger.info("发送最终统计通知")
        result = notifier.send_statistics_notification()
        
        if result:
            logger.info("✅ 多交易统计测试成功！")
            return True
        else:
            logger.error("❌ 多交易统计测试失败")
            return False
        
    except Exception as e:
        logger.error(f"多交易统计测试异常: {e}")
        return False


async def main():
    """主函数"""
    logger.info("开始优化版通知器功能测试...")
    
    # 测试1: 完整通知流程
    test1_result = await test_complete_notification_flow()
    
    await asyncio.sleep(3)
    
    # 测试2: 失败通知流程
    test2_result = await test_failed_notification_flow()
    
    await asyncio.sleep(3)
    
    # 测试3: 多交易统计
    test3_result = await test_multiple_trades()
    
    logger.info("=== 通知功能测试结果汇总 ===")
    logger.info(f"完整通知流程: {'✓' if test1_result else '✗'}")
    logger.info(f"失败通知流程: {'✓' if test2_result else '✗'}")
    logger.info(f"多交易统计: {'✓' if test3_result else '✗'}")
    
    all_success = all([test1_result, test2_result, test3_result])
    logger.info(f"总体结果: {'✓ 全部成功' if all_success else '✗ 部分失败'}")
    
    if all_success:
        logger.info("🎉 优化版通知器功能测试成功！")
        logger.info("💡 通知器已成功集成到系统中:")
        logger.info("  ✅ 通知1: 信号开仓（包含完整上币信息）")
        logger.info("  ✅ 通知2: 交易完成（包含详细盈亏）")
        logger.info("  ✅ 通知3: 历史统计（汇总表现）")
        logger.info("🚀 系统已准备好使用优化版通知！")
        return True
    else:
        logger.error("❌ 通知功能测试失败，请检查配置")
        return False


if __name__ == "__main__":
    asyncio.run(main())

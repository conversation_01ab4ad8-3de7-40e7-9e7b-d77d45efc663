"""
普通交易器 - 适配现有的FixedStrategy
"""
from typing import Dict, Any, Optional
from loguru import logger

from .interfaces.trader_interface import TraderInterface
from .fixed_strategy import FixedTradingStrategy
from ..models import TradingSignal


class NormalTrader(TraderInterface):
    """普通交易器 - 封装现有的FixedStrategy"""
    
    def __init__(self, fixed_strategy: FixedTradingStrategy):
        self.strategy = fixed_strategy
        logger.info("普通交易器初始化完成")
    
    async def execute_trade(self, signal: TradingSignal, **kwargs) -> Dict[str, Any]:
        """
        执行普通交易
        
        Args:
            signal: 交易信号
            **kwargs: 额外参数
            
        Returns:
            Dict: 交易结果
        """
        try:
            # 使用现有的固定策略处理信号
            success, session_id = await self.strategy.process_listing_signal(
                signal=signal,
                exchange=kwargs.get('exchange', 'binance'),
                announcement_time=signal.timestamp,
                multiple_exchanges=kwargs.get('multiple_exchanges', False),
                notifier=kwargs.get('notifier')
            )
            
            if success:
                # 获取仓位信息
                position = self.strategy.active_positions.get(signal.symbol)
                if position:
                    return {
                        'success': True,
                        'order_id': session_id,
                        'price': position.open_price,
                        'quantity': position.quantity,
                        'mode': 'normal',
                        'message': '普通交易执行成功'
                    }
                else:
                    return {
                        'success': True,
                        'order_id': session_id,
                        'price': 0.0,
                        'quantity': 0.0,
                        'mode': 'normal',
                        'message': '交易信号处理完成'
                    }
            else:
                return {
                    'success': False,
                    'message': '固定策略执行失败'
                }
                
        except Exception as e:
            logger.error(f"❌ 普通交易异常: {e}")
            return {
                'success': False,
                'message': f'普通交易异常: {str(e)}'
            }
    
    async def close_position(self, symbol: str, percentage: float = 100.0) -> Dict[str, Any]:
        """
        平仓
        
        Args:
            symbol: 交易对符号
            percentage: 平仓百分比
            
        Returns:
            Dict: 平仓结果
        """
        try:
            # 获取当前仓位
            position = self.strategy.active_positions.get(symbol)
            if not position:
                return {
                    'success': False,
                    'message': f'{symbol} 无持仓'
                }
            
            # 使用固定策略的平仓方法
            close_result = await self.strategy._close_partial_position(position, percentage)
            
            if close_result:
                return {
                    'success': True,
                    'order_id': f"close_{symbol}_{int(percentage)}",
                    'price': position.current_price,
                    'quantity': position.quantity * (percentage / 100.0),
                    'message': f'平仓成功 {percentage}%'
                }
            else:
                return {
                    'success': False,
                    'message': '平仓失败'
                }
                
        except Exception as e:
            logger.error(f"❌ 普通交易平仓异常: {e}")
            return {
                'success': False,
                'message': f'平仓异常: {str(e)}'
            }
    
    async def get_position_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取仓位信息"""
        try:
            position = self.strategy.active_positions.get(symbol)
            if position:
                return {
                    'symbol': position.symbol,
                    'size': position.quantity,
                    'side': 'LONG',  # 固定策略只做多
                    'markPrice': position.current_price,
                    'entryPrice': position.open_price,
                    'unrealizedPnl': (position.current_price - position.open_price) * position.quantity,
                    'percentage': ((position.current_price - position.open_price) / position.open_price) * 100,
                    'status': position.status.value
                }
            return None
            
        except Exception as e:
            logger.error(f"❌ 获取仓位信息异常: {e}")
            return None
    
    async def get_account_balance(self) -> Dict[str, Any]:
        """获取账户余额"""
        try:
            if self.strategy.binance_client:
                # 使用币安客户端获取余额
                balance = await self.strategy.binance_client.futures_account_balance()
                
                # 转换格式
                usdt_balance = None
                for item in balance:
                    if item['asset'] == 'USDT':
                        usdt_balance = item
                        break
                
                if usdt_balance:
                    return {
                        'asset': 'USDT',
                        'balance': float(usdt_balance['balance']),
                        'available': float(usdt_balance['availableBalance']),
                        'crossWalletBalance': float(usdt_balance['crossWalletBalance'])
                    }
            
            return {}
            
        except Exception as e:
            logger.error(f"❌ 获取账户余额异常: {e}")
            return {}
    
    def get_trader_type(self) -> str:
        """获取交易器类型"""
        return 'normal'
    
    def get_active_positions(self) -> Dict[str, Any]:
        """获取所有活跃仓位"""
        positions = {}
        for symbol, position in self.strategy.active_positions.items():
            positions[symbol] = {
                'symbol': position.symbol,
                'quantity': position.quantity,
                'open_price': position.open_price,
                'current_price': position.current_price,
                'status': position.status.value,
                'open_time': position.open_time.isoformat(),
                'unrealized_pnl': (position.current_price - position.open_price) * position.quantity
            }
        return positions

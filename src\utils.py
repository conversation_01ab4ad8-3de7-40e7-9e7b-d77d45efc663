"""
工具函数
"""
import re
import yaml
import os
from typing import List, Dict, Any, Optional
from datetime import datetime
from loguru import logger


def load_config(config_path: str = "config.yaml") -> Dict[str, Any]:
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
        logger.info(f"配置文件加载成功: {config_path}")
        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        raise


def extract_tokens_from_text(text: str) -> List[str]:
    """从文本中提取代币符号 - 使用统一规则"""
    try:
        import sys
        import os
        # 添加正确的路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)

        from monitors.unified_rules import unified_rules
        return unified_rules.extract_symbols_from_text(text)
    except (ImportError, Exception) as e:
        # 如果导入失败，使用备用逻辑
        import re

        # 基本的代币提取逻辑
        pattern1 = r'\(([A-Z]+)\)'
        tokens1 = re.findall(pattern1, text)

        pattern2 = r'\$([A-Z]+)'
        tokens2 = re.findall(pattern2, text)

        all_tokens = list(set(tokens1 + tokens2))

        exclude_words = {
            'USD', 'USDT', 'USDC', 'BTC', 'ETH', 'BNB', 'API', 'URL', 'HTTP', 'HTTPS',
            'NEW', 'LISTING', 'MARKET', 'SUPPORT', 'TRADING', 'EXCHANGE', 'BINANCE',
            'UPBIT', 'COINBASE', 'TELEGRAM', 'BOT', 'AUTO', 'FUTURES', 'SPOT'
        }

        filtered_tokens = [token for token in all_tokens if token not in exclude_words and not re.search(r'\d', token)]
        return filtered_tokens


def extract_market_cap(text: str) -> Optional[float]:
    """从文本中提取市值信息"""
    # 匹配市值相关的数字
    patterns = [
        r'market\s*cap[:\s]*\$?([0-9,]+(?:\.[0-9]+)?)\s*([kmb])?',
        r'mcap[:\s]*\$?([0-9,]+(?:\.[0-9]+)?)\s*([kmb])?',
        r'cap[:\s]*\$?([0-9,]+(?:\.[0-9]+)?)\s*([kmb])?'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text.lower())
        if match:
            value = float(match.group(1).replace(',', ''))
            unit = match.group(2)
            
            if unit:
                if unit.lower() == 'k':
                    value *= 1000
                elif unit.lower() == 'm':
                    value *= 1000000
                elif unit.lower() == 'b':
                    value *= 1000000000
                    
            return value
    
    return None


def setup_logger(log_file: str = "trading_bot.log", level: str = "INFO"):
    """设置日志"""
    logger.remove()  # 移除默认处理器
    
    # 添加文件处理器
    logger.add(
        log_file,
        rotation="10 MB",
        retention="30 days",
        level=level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        encoding="utf-8"
    )
    
    # 添加控制台处理器
    logger.add(
        lambda msg: print(msg, end=""),
        level=level,
        format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | <cyan>{name}:{function}:{line}</cyan> | {message}"
    )
    
    logger.info("日志系统初始化完成")


def validate_symbol(symbol: str) -> bool:
    """验证代币符号格式"""
    if not symbol or len(symbol) < 2:
        return False
    
    # 检查是否为纯大写字母
    if not symbol.isupper() or not symbol.isalpha():
        return False
        
    # 检查长度
    if len(symbol) > 10:
        return False
        
    return True


def format_timestamp(timestamp: datetime) -> str:
    """格式化时间戳"""
    return timestamp.strftime("%Y-%m-%d %H:%M:%S")


def safe_float(value: Any, default: float = 0.0) -> float:
    """安全转换为浮点数"""
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def safe_int(value: Any, default: int = 0) -> int:
    """安全转换为整数"""
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


def save_config(config: Dict[str, Any], config_file: str = "config.yaml") -> bool:
    """保存配置到文件"""
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        return True
    except Exception as e:
        logger.error(f"保存配置文件失败: {e}")
        return False

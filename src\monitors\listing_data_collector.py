#!/usr/bin/env python3
"""
上币数据收集器
通过监控系统自动收集四大交易所的上币信息
"""
import asyncio
import os
import re
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any
from telethon import TelegramClient
from loguru import logger
from .unified_rules import unified_rules

class ListingDataCollector:
    """上币数据收集器"""
    
    def __init__(self):
        self.api_id = 26145597
        self.api_hash = "859206f58db62ec957089a7e9ff11d38"
        self.client = None
        
        # 使用统一规则
        self.exchange_keywords = unified_rules.exchange_keywords
    
    async def initialize(self) -> bool:
        """初始化Telegram客户端"""
        try:
            # 使用统一的会话文件名
            session_file = 'telegram_session.session'
            if os.path.exists(session_file):
                try:
                    # 尝试读取会话文件
                    with open(session_file, 'rb') as f:
                        f.read(100)  # 读取前100字节测试
                except Exception as e:
                    logger.warning(f"⚠️ 检测到损坏的会话文件，正在删除: {e}")
                    os.remove(session_file)

            self.client = TelegramClient('telegram_session', self.api_id, self.api_hash)
            await self.client.start()

            me = await self.client.get_me()
            logger.info(f"✅ Telegram连接成功: {me.first_name}")
            return True

        except Exception as e:
            error_msg = str(e)
            if "database disk image is malformed" in error_msg:
                logger.error(f"❌ Telegram会话文件损坏，请删除 telegram_session.session 文件后重试")
            elif "phone number" in error_msg.lower():
                logger.error(f"❌ Telegram需要手机号验证，请先运行: python setup_telegram_session.py")
            else:
                logger.error(f"❌ Telegram初始化失败: {e}")
            return False
    
    def extract_symbols_from_text(self, text: str) -> List[str]:
        """从文本中提取代币符号 - 使用统一规则"""
        return unified_rules.extract_symbols_from_text(text)


    
    def detect_exchange_from_text(self, text: str) -> str:
        """从文本中检测交易所 - 使用统一规则"""
        return unified_rules.detect_exchange_from_text(text)
    
    def extract_marketcap_from_text(self, text: str, symbols: List[str]) -> Dict[str, str]:
        """从文本中提取MarketCap信息 - 使用统一规则"""
        return unified_rules.extract_marketcap_from_text(text, symbols)
    



    
    def is_listing_message(self, text: str) -> bool:
        """判断是否为上币消息 - 使用统一规则"""
        return unified_rules.is_listing_message(text)
    
    async def collect_channel_listings(self, channel: str, days: int = 90) -> List[Dict[str, Any]]:
        """收集频道的上币信息"""
        try:
            logger.info(f"📡 收集频道 {channel} 最近{days}天的上币信息...")
            
            entity = await self.client.get_entity(channel)
            
            # 计算时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            listings = []
            count = 0
            
            async for message in self.client.iter_messages(entity):
                # 转换为相同的时区进行比较
                message_date_naive = message.date.replace(tzinfo=None)
                if message_date_naive < start_date:
                    break
                
                count += 1
                
                if message.text and self.is_listing_message(message.text):
                    symbols = self.extract_symbols_from_text(message.text)
                    exchange = self.detect_exchange_from_text(message.text)

                    if exchange != 'unknown' and symbols:
                        # 提取MarketCap
                        marketcaps = self.extract_marketcap_from_text(message.text, symbols)

                        # 为每个代币创建记录
                        for symbol in symbols:
                            listing_record = {
                                'symbol': symbol,
                                'exchange': exchange,
                                'announcement_time': (message.date + timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S'),
                                'marketcap': marketcaps.get(symbol, ''),
                                'message_id': message.id,
                                'channel': channel,
                                'message_text': message.text[:500]  # 保存前500字符
                            }
                            listings.append(listing_record)

                        logger.info(f"✅ 收集: {exchange.upper()} - {symbols}")
            
            logger.info(f"✅ {channel}: 检查了 {count} 条消息，收集到 {len(listings)} 条上币信息")
            return listings
            
        except Exception as e:
            logger.error(f"❌ 收集频道 {channel} 失败: {e}")
            return []
    
    async def collect_listings(self, days: int = 90) -> List[Dict[str, Any]]:
        """收集所有频道的上币信息"""
        try:
            channels = ['@BWEnews', '@binance_announcements']
            
            all_listings = []
            
            for channel in channels:
                listings = await self.collect_channel_listings(channel, days)
                all_listings.extend(listings)
            
            # 按时间排序
            all_listings.sort(key=lambda x: x['telegram_time'], reverse=True)
            
            logger.info(f"✅ 总计收集到 {len(all_listings)} 条上币信息")
            return all_listings
            
        except Exception as e:
            logger.error(f"❌ 收集上币信息失败: {e}")
            return []
    
    async def cleanup(self):
        """清理资源"""
        if self.client:
            await self.client.disconnect()
            logger.info("✅ Telegram客户端已断开连接")

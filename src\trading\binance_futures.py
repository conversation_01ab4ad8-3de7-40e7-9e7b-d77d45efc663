"""
币安期货交易模块
实现逐仓开单、杠杆设置、止损等功能
"""
import asyncio
from decimal import Decimal, ROUND_DOWN
from typing import Optional, Dict, Any, List
from datetime import datetime
from loguru import logger

try:
    from binance.client import Client
    from binance.exceptions import BinanceAPIException, BinanceOrderException
    from binance.enums import *
except ImportError:
    logger.error("请安装python-binance: pip install python-binance")
    raise

from ..models import TradingOrder, OrderSide, OrderStatus, TradingConfig, TradingSignal
from ..utils import safe_float, safe_int


class BinanceFuturesTrader:
    """币安期货交易器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.api_key = config.get('api_key')
        self.api_secret = config.get('api_secret')
        self.testnet = config.get('testnet', True)
        
        if not all([self.api_key, self.api_secret]):
            raise ValueError("币安API配置不完整，请检查api_key和api_secret")
            
        # 初始化客户端
        self.client = Client(
            api_key=self.api_key,
            api_secret=self.api_secret,
            testnet=self.testnet
        )
        
        # 交易配置
        self.trading_config = TradingConfig(
            amount=safe_float(config.get('amount', 100)),
            leverage=safe_int(config.get('leverage', 10)),
            stop_loss_percent=safe_float(config.get('stop_loss_percent', 80)),
            testnet=self.testnet
        )
        
        logger.info(f"币安期货交易器初始化完成 (测试网: {self.testnet})")
        
    async def get_symbol_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取交易对信息"""
        try:
            # 确保symbol格式正确 (例如: BTCUSDT)
            if not symbol.endswith('USDT'):
                symbol = f"{symbol}USDT"
                
            exchange_info = self.client.futures_exchange_info()
            
            for symbol_info in exchange_info['symbols']:
                if symbol_info['symbol'] == symbol:
                    return symbol_info
                    
            logger.warning(f"未找到交易对信息: {symbol}")
            return None
            
        except Exception as e:
            logger.error(f"获取交易对信息失败: {e}")
            return None
            
    async def get_current_price(self, symbol: str) -> Optional[float]:
        """获取当前价格"""
        try:
            if not symbol.endswith('USDT'):
                symbol = f"{symbol}USDT"
                
            ticker = self.client.futures_symbol_ticker(symbol=symbol)
            return safe_float(ticker['price'])
            
        except Exception as e:
            logger.error(f"获取价格失败 {symbol}: {e}")
            return None
            
    def calculate_quantity(self, symbol: str, price: float, amount: float) -> float:
        """计算下单数量"""
        try:
            # 基础数量计算
            quantity = amount / price
            
            # 这里应该根据交易对的精度要求进行调整
            # 简化处理，保留6位小数
            return round(quantity, 6)
            
        except Exception as e:
            logger.error(f"计算下单数量失败: {e}")
            return 0.0
            
    async def set_leverage(self, symbol: str, leverage: int) -> bool:
        """设置杠杆倍数"""
        try:
            if not symbol.endswith('USDT'):
                symbol = f"{symbol}USDT"
                
            result = self.client.futures_change_leverage(
                symbol=symbol,
                leverage=leverage
            )
            
            logger.info(f"设置杠杆成功 {symbol}: {leverage}x")
            return True
            
        except BinanceAPIException as e:
            if "leverage not modified" in str(e).lower():
                logger.info(f"杠杆已经是 {leverage}x，无需修改")
                return True
            else:
                logger.error(f"设置杠杆失败 {symbol}: {e}")
                return False
        except Exception as e:
            logger.error(f"设置杠杆异常 {symbol}: {e}")
            return False
            
    async def set_margin_type(self, symbol: str, margin_type: str = "ISOLATED") -> bool:
        """设置保证金模式（逐仓/全仓）"""
        try:
            if not symbol.endswith('USDT'):
                symbol = f"{symbol}USDT"
                
            result = self.client.futures_change_margin_type(
                symbol=symbol,
                marginType=margin_type
            )
            
            logger.info(f"设置保证金模式成功 {symbol}: {margin_type}")
            return True
            
        except BinanceAPIException as e:
            if "no need to change margin type" in str(e).lower():
                logger.info(f"保证金模式已经是 {margin_type}，无需修改")
                return True
            else:
                logger.error(f"设置保证金模式失败 {symbol}: {e}")
                return False
        except Exception as e:
            logger.error(f"设置保证金模式异常 {symbol}: {e}")
            return False
            
    async def place_market_order(
        self, 
        symbol: str, 
        side: OrderSide, 
        quantity: float
    ) -> Optional[Dict[str, Any]]:
        """下市价单"""
        try:
            if not symbol.endswith('USDT'):
                symbol = f"{symbol}USDT"
                
            # 转换订单方向
            binance_side = SIDE_BUY if side == OrderSide.LONG else SIDE_SELL
            
            order = self.client.futures_create_order(
                symbol=symbol,
                side=binance_side,
                type=ORDER_TYPE_MARKET,
                quantity=quantity
            )
            
            logger.info(f"市价单下单成功 {symbol}: {side.value} {quantity}")
            return order
            
        except BinanceOrderException as e:
            logger.error(f"下单失败 {symbol}: {e}")
            return None
        except Exception as e:
            logger.error(f"下单异常 {symbol}: {e}")
            return None
            
    async def place_stop_loss_order(
        self, 
        symbol: str, 
        side: OrderSide, 
        quantity: float, 
        stop_price: float
    ) -> Optional[Dict[str, Any]]:
        """下止损单"""
        try:
            if not symbol.endswith('USDT'):
                symbol = f"{symbol}USDT"
                
            # 止损单的方向与开仓相反
            binance_side = SIDE_SELL if side == OrderSide.LONG else SIDE_BUY
            
            order = self.client.futures_create_order(
                symbol=symbol,
                side=binance_side,
                type=ORDER_TYPE_STOP_MARKET,
                quantity=quantity,
                stopPrice=stop_price,
                reduceOnly=True  # 只减仓
            )
            
            logger.info(f"止损单下单成功 {symbol}: {stop_price}")
            return order
            
        except Exception as e:
            logger.error(f"止损单下单失败 {symbol}: {e}")
            return None
            
    async def execute_trade(self, signal: TradingSignal) -> Optional[TradingOrder]:
        """执行交易"""
        symbol = signal.symbol
        if not symbol.endswith('USDT'):
            symbol = f"{symbol}USDT"
            
        logger.info(f"开始执行交易: {symbol}")
        
        try:
            # 1. 检查交易对是否存在
            symbol_info = await self.get_symbol_info(symbol)
            if not symbol_info:
                logger.error(f"交易对不存在: {symbol}")
                return None
                
            # 2. 获取当前价格
            current_price = await self.get_current_price(symbol)
            if not current_price:
                logger.error(f"无法获取价格: {symbol}")
                return None
                
            # 3. 设置保证金模式为逐仓
            if not await self.set_margin_type(symbol, "ISOLATED"):
                logger.warning(f"设置逐仓模式失败: {symbol}")
                
            # 4. 设置杠杆
            if not await self.set_leverage(symbol, self.trading_config.leverage):
                logger.warning(f"设置杠杆失败: {symbol}")
                
            # 5. 计算下单数量
            quantity = self.calculate_quantity(
                symbol, 
                current_price, 
                self.trading_config.amount
            )
            
            if quantity <= 0:
                logger.error(f"计算下单数量失败: {symbol}")
                return None
                
            # 6. 下市价单开仓 (默认做多)
            order_side = OrderSide.LONG
            market_order = await self.place_market_order(symbol, order_side, quantity)
            
            if not market_order:
                logger.error(f"开仓失败: {symbol}")
                return None
                
            # 7. 计算止损价格
            stop_loss_price = current_price * (1 - self.trading_config.stop_loss_percent / 100)
            
            # 8. 下止损单
            stop_order = await self.place_stop_loss_order(
                symbol, 
                order_side, 
                quantity, 
                stop_loss_price
            )
            
            # 9. 创建交易记录
            trading_order = TradingOrder(
                symbol=symbol,
                side=order_side,
                amount=self.trading_config.amount,
                leverage=self.trading_config.leverage,
                stop_loss_percent=self.trading_config.stop_loss_percent,
                status=OrderStatus.FILLED,
                order_id=market_order['orderId'],
                filled_price=safe_float(market_order.get('avgPrice', current_price)),
                filled_at=datetime.now()
            )
            
            logger.info(f"交易执行成功: {symbol} - 价格: {current_price} - 数量: {quantity}")
            return trading_order
            
        except Exception as e:
            logger.error(f"执行交易异常 {symbol}: {e}")
            return None
            
    def get_exchange_info(self) -> Optional[Dict[str, Any]]:
        """获取交易所信息"""
        try:
            return self.client.futures_exchange_info()
        except Exception as e:
            logger.error(f"获取交易所信息失败: {e}")
            return None

    async def get_account_balance(self) -> Optional[Dict[str, Any]]:
        """获取账户余额"""
        try:
            account = self.client.futures_account()
            return account
        except Exception as e:
            logger.error(f"获取账户余额失败: {e}")
            return None
            
    async def get_positions(self) -> List[Dict[str, Any]]:
        """获取持仓信息"""
        try:
            positions = self.client.futures_position_information()
            # 过滤掉没有持仓的
            active_positions = [pos for pos in positions if float(pos['positionAmt']) != 0]
            return active_positions
        except Exception as e:
            logger.error(f"获取持仓信息失败: {e}")
            return []

# 🌍 环境变量文件说明

## 📋 文件概述

项目中有三个环境变量相关文件，各有不同用途：

| 文件 | 用途 | 内容 | 版本控制 |
|------|------|------|----------|
| `.env` | **实际配置** | 真实的API密钥和配置值 | ❌ 不提交 |
| `.env.example` | **配置示例** | 示例值和配置说明 | ✅ 提交 |
| `.env.template` | **部署模板** | 占位符，供脚本使用 | ✅ 提交 |

## 📁 文件详细说明

### 1. `.env` - 实际配置文件
```bash
# 您的真实配置
BINANCE_API_KEY=vaiGFrmItkZ0iZqHOjHXFxrGk5Pk21oWblhh3rZZMTjR0f5zGAzbyO8Cc1N6Lmc0
BINANCE_API_SECRET=Lm2ED1ahqFBKeKRAFRzUjkqL7jcDRDVhZv56Cfgi1qw8S7mpn351hEznjXASml5R
TELEGRAM_API_ID=26145597
```

**特点:**
- 🔒 包含真实的API密钥和敏感信息
- 🚫 **绝不提交到Git** (已在.gitignore中)
- 🎯 程序运行时实际读取的配置
- 👤 每个用户/环境都不同

### 2. `.env.example` - 配置示例文件
```bash
# 配置示例和说明
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_api_secret_here
TELEGRAM_API_ID=your_telegram_api_id
```

**特点:**
- 📚 给新用户看的配置示例
- 🔓 不包含真实敏感信息
- ✅ 可以安全提交到Git
- 📖 包含详细的配置说明

**使用方法:**
```bash
# 新用户首次配置
cp .env.example .env
# 然后编辑 .env 填入真实值
nano .env
```

### 3. `.env.template` - 部署模板文件
```bash
# 自动化部署模板
BINANCE_API_KEY={{BINANCE_API_KEY}}
BINANCE_API_SECRET={{BINANCE_API_SECRET}}
TRADING_AMOUNT={{TRADING_AMOUNT:-100}}
```

**特点:**
- 🤖 供自动化脚本和Docker使用
- 🔧 包含占位符 `{{变量名}}`
- 📦 支持默认值 `{{变量名:-默认值}}`
- 🚀 适用于CI/CD和容器化部署

**使用场景:**
```bash
# Docker部署时自动替换
envsubst < .env.template > .env

# 或者在docker-compose.yml中使用
environment:
  - BINANCE_API_KEY=${BINANCE_API_KEY}
```

## 🔄 文件关系图

```
新用户配置流程:
.env.example → 复制 → .env → 编辑填入真实值

自动化部署流程:
.env.template → 脚本处理 → .env → 容器使用

配置同步:
.env ←→ config.yaml (通过sync_configs.py)
```

## 🛠️ 常用操作

### 首次配置
```bash
# 1. 复制示例文件
cp .env.example .env

# 2. 编辑配置
nano .env

# 3. 填入您的真实API密钥
```

### Docker部署
```bash
# 使用模板生成配置
export BINANCE_API_KEY="your_key"
export TELEGRAM_API_ID="your_id"
envsubst < .env.template > .env

# 或者直接在docker-compose中使用环境变量
```

### 配置同步
```bash
# 同步环境变量到配置文件
python scripts/sync_configs.py --sync
```

## 🔐 安全最佳实践

### ✅ 应该做的
- 将真实API密钥只放在 `.env` 文件中
- 确保 `.env` 在 `.gitignore` 中
- 定期轮换API密钥
- 使用不同环境的不同配置

### ❌ 不应该做的
- 不要将真实API密钥放在示例文件中
- 不要提交 `.env` 文件到版本控制
- 不要在代码中硬编码敏感信息
- 不要在日志中输出敏感信息

## 🚨 安全检查清单

- [ ] `.env` 文件在 `.gitignore` 中
- [ ] `.env.example` 不包含真实密钥
- [ ] `.env.template` 使用占位符
- [ ] 生产环境使用强密码
- [ ] 定期检查是否意外提交敏感信息

## 🔧 故障排除

### 问题1: 配置不生效
```bash
# 检查文件是否存在
ls -la .env

# 检查文件内容格式
cat .env | grep -v "^#" | grep "="
```

### 问题2: Docker中配置不正确
```bash
# 检查容器内的环境变量
docker exec container_name env | grep BINANCE
```

### 问题3: 配置文件不一致
```bash
# 运行配置同步工具
python scripts/sync_configs.py --check
python scripts/sync_configs.py --sync
```

## 📚 相关文档

- [配置管理指南](./CONFIG_MANAGEMENT.md)
- [部署指南](./QUICK_DEPLOY_GUIDE.md)
- [Docker部署](../README-Docker.md)

---

💡 **记住**: 
- `.env` = 您的真实配置（不提交）
- `.env.example` = 配置示例（可提交）
- `.env.template` = 部署模板（可提交）

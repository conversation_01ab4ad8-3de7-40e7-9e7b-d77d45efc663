"""
系统健康监控模块
实现全面的系统健康检查，包括API连接状态、内存使用、错误统计等
"""
import asyncio
import psutil
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from loguru import logger


@dataclass
class HealthCheckResult:
    """健康检查结果"""
    component: str
    status: str  # 'healthy', 'warning', 'critical', 'unknown'
    message: str
    details: Dict[str, Any]
    check_time: datetime
    response_time_ms: Optional[float] = None


class SystemHealthMonitor:
    """系统健康监控器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.check_interval = config.get('health_check_interval', 30)
        self.warning_thresholds = config.get('warning_thresholds', {
            'cpu_percent': 80,
            'memory_percent': 85,
            'disk_percent': 90,
            'error_rate': 0.05,
            'response_time_ms': 5000
        })
        self.critical_thresholds = config.get('critical_thresholds', {
            'cpu_percent': 95,
            'memory_percent': 95,
            'disk_percent': 95,
            'error_rate': 0.15,
            'response_time_ms': 10000
        })
        
        # 健康检查历史
        self.health_history: List[HealthCheckResult] = []
        self.max_history_size = 1000
        
        logger.info("系统健康监控器初始化完成")
        
    async def perform_full_health_check(self, components: Dict[str, Any]) -> List[HealthCheckResult]:
        """执行完整的健康检查"""
        results = []
        
        try:
            # 1. 系统资源检查
            results.extend(await self._check_system_resources())
            
            # 2. API连接检查
            results.extend(await self._check_api_connections(components))
            
            # 3. 组件状态检查
            results.extend(await self._check_component_status(components))
            
            # 4. 数据库/文件系统检查
            results.extend(await self._check_storage_health())
            
            # 5. 网络连接检查
            results.extend(await self._check_network_connectivity())
            
            # 保存检查结果
            self._save_health_results(results)
            
            return results
            
        except Exception as e:
            logger.error(f"健康检查异常: {e}")
            return [HealthCheckResult(
                component="health_monitor",
                status="critical",
                message=f"健康检查异常: {e}",
                details={},
                check_time=datetime.now()
            )]
            
    async def _check_system_resources(self) -> List[HealthCheckResult]:
        """检查系统资源"""
        results = []
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_status = self._get_status_by_threshold(cpu_percent, 'cpu_percent')
            results.append(HealthCheckResult(
                component="system_cpu",
                status=cpu_status,
                message=f"CPU使用率: {cpu_percent:.1f}%",
                details={'cpu_percent': cpu_percent, 'cpu_count': psutil.cpu_count()},
                check_time=datetime.now()
            ))
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_status = self._get_status_by_threshold(memory.percent, 'memory_percent')
            results.append(HealthCheckResult(
                component="system_memory",
                status=memory_status,
                message=f"内存使用率: {memory.percent:.1f}%",
                details={
                    'memory_percent': memory.percent,
                    'total_gb': round(memory.total / (1024**3), 2),
                    'available_gb': round(memory.available / (1024**3), 2),
                    'used_gb': round(memory.used / (1024**3), 2)
                },
                check_time=datetime.now()
            ))
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_status = self._get_status_by_threshold(disk_percent, 'disk_percent')
            results.append(HealthCheckResult(
                component="system_disk",
                status=disk_status,
                message=f"磁盘使用率: {disk_percent:.1f}%",
                details={
                    'disk_percent': disk_percent,
                    'total_gb': round(disk.total / (1024**3), 2),
                    'free_gb': round(disk.free / (1024**3), 2),
                    'used_gb': round(disk.used / (1024**3), 2)
                },
                check_time=datetime.now()
            ))
            
        except Exception as e:
            logger.error(f"系统资源检查异常: {e}")
            results.append(HealthCheckResult(
                component="system_resources",
                status="critical",
                message=f"系统资源检查失败: {e}",
                details={},
                check_time=datetime.now()
            ))
            
        return results
        
    async def _check_api_connections(self, components: Dict[str, Any]) -> List[HealthCheckResult]:
        """检查API连接状态"""
        results = []
        
        # 检查币安API
        if 'signal_processor' in components:
            processor = components['signal_processor']
            if hasattr(processor, 'trader'):
                result = await self._check_binance_api(processor.trader)
                results.append(result)
                
        # 检查飞书API
        if 'signal_processor' in components:
            processor = components['signal_processor']
            if hasattr(processor, 'feishu_notifier'):
                result = await self._check_feishu_api(processor.feishu_notifier)
                results.append(result)
                
        return results
        
    async def _check_binance_api(self, trader) -> HealthCheckResult:
        """检查币安API连接"""
        try:
            start_time = datetime.now()
            
            # 尝试获取账户信息
            account = await trader.get_account_balance()
            
            response_time = (datetime.now() - start_time).total_seconds() * 1000
            
            if account is not None:
                status = self._get_status_by_threshold(response_time, 'response_time_ms')
                return HealthCheckResult(
                    component="binance_api",
                    status=status,
                    message=f"币安API连接正常",
                    details={
                        'response_time_ms': response_time,
                        'account_type': account.get('accountType', 'unknown'),
                        'can_trade': account.get('canTrade', False)
                    },
                    check_time=datetime.now(),
                    response_time_ms=response_time
                )
            else:
                return HealthCheckResult(
                    component="binance_api",
                    status="critical",
                    message="币安API连接失败",
                    details={'response_time_ms': response_time},
                    check_time=datetime.now(),
                    response_time_ms=response_time
                )
                
        except Exception as e:
            return HealthCheckResult(
                component="binance_api",
                status="critical",
                message=f"币安API检查异常: {e}",
                details={'error': str(e)},
                check_time=datetime.now()
            )
            
    async def _check_feishu_api(self, notifier) -> HealthCheckResult:
        """检查飞书API连接"""
        try:
            if not notifier.enabled:
                return HealthCheckResult(
                    component="feishu_api",
                    status="warning",
                    message="飞书通知未启用",
                    details={'enabled': False},
                    check_time=datetime.now()
                )
                
            # 简单的连接测试
            start_time = datetime.now()
            
            # 这里可以发送一个测试消息或ping
            # 暂时只检查配置
            response_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return HealthCheckResult(
                component="feishu_api",
                status="healthy",
                message="飞书API配置正常",
                details={
                    'enabled': True,
                    'webhook_configured': bool(notifier.webhook_url),
                    'response_time_ms': response_time
                },
                check_time=datetime.now(),
                response_time_ms=response_time
            )
            
        except Exception as e:
            return HealthCheckResult(
                component="feishu_api",
                status="warning",
                message=f"飞书API检查异常: {e}",
                details={'error': str(e)},
                check_time=datetime.now()
            )
            
    async def _check_component_status(self, components: Dict[str, Any]) -> List[HealthCheckResult]:
        """检查组件状态"""
        results = []
        
        for name, component in components.items():
            try:
                if component is None:
                    results.append(HealthCheckResult(
                        component=name,
                        status="critical",
                        message=f"组件{name}未初始化",
                        details={},
                        check_time=datetime.now()
                    ))
                    continue
                    
                # 检查组件是否有健康检查方法
                if hasattr(component, 'health_check'):
                    result = await component.health_check()
                    results.append(result)
                else:
                    # 基本的存在性检查
                    results.append(HealthCheckResult(
                        component=name,
                        status="healthy",
                        message=f"组件{name}运行正常",
                        details={'initialized': True},
                        check_time=datetime.now()
                    ))
                    
            except Exception as e:
                results.append(HealthCheckResult(
                    component=name,
                    status="critical",
                    message=f"组件{name}检查异常: {e}",
                    details={'error': str(e)},
                    check_time=datetime.now()
                ))
                
        return results
        
    async def _check_storage_health(self) -> List[HealthCheckResult]:
        """检查存储健康状态"""
        results = []
        
        try:
            # 检查重要文件是否可写
            test_files = ['trading_stats.json', 'system_state.json']
            
            for file_path in test_files:
                try:
                    # 尝试写入测试
                    with open(f"{file_path}.test", 'w') as f:
                        f.write('test')
                    
                    # 删除测试文件
                    import os
                    os.remove(f"{file_path}.test")
                    
                    results.append(HealthCheckResult(
                        component=f"storage_{file_path}",
                        status="healthy",
                        message=f"文件{file_path}可正常读写",
                        details={'writable': True},
                        check_time=datetime.now()
                    ))
                    
                except Exception as e:
                    results.append(HealthCheckResult(
                        component=f"storage_{file_path}",
                        status="warning",
                        message=f"文件{file_path}读写异常: {e}",
                        details={'writable': False, 'error': str(e)},
                        check_time=datetime.now()
                    ))
                    
        except Exception as e:
            results.append(HealthCheckResult(
                component="storage_health",
                status="critical",
                message=f"存储健康检查异常: {e}",
                details={'error': str(e)},
                check_time=datetime.now()
            ))
            
        return results
        
    async def _check_network_connectivity(self) -> List[HealthCheckResult]:
        """检查网络连接"""
        results = []
        
        # 测试网络连接
        test_urls = [
            ('binance', 'https://api.binance.com/api/v3/ping'),
            ('upbit', 'https://api.upbit.com/v1/market/all'),
            ('google_dns', 'https://8.8.8.8')
        ]
        
        for name, url in test_urls:
            try:
                start_time = datetime.now()
                
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                    async with session.get(url) as response:
                        response_time = (datetime.now() - start_time).total_seconds() * 1000
                        
                        if response.status == 200:
                            status = self._get_status_by_threshold(response_time, 'response_time_ms')
                            results.append(HealthCheckResult(
                                component=f"network_{name}",
                                status=status,
                                message=f"{name}网络连接正常",
                                details={
                                    'url': url,
                                    'status_code': response.status,
                                    'response_time_ms': response_time
                                },
                                check_time=datetime.now(),
                                response_time_ms=response_time
                            ))
                        else:
                            results.append(HealthCheckResult(
                                component=f"network_{name}",
                                status="warning",
                                message=f"{name}网络连接异常: HTTP {response.status}",
                                details={
                                    'url': url,
                                    'status_code': response.status,
                                    'response_time_ms': response_time
                                },
                                check_time=datetime.now(),
                                response_time_ms=response_time
                            ))
                            
            except Exception as e:
                results.append(HealthCheckResult(
                    component=f"network_{name}",
                    status="critical",
                    message=f"{name}网络连接失败: {e}",
                    details={'url': url, 'error': str(e)},
                    check_time=datetime.now()
                ))
                
        return results
        
    def _get_status_by_threshold(self, value: float, metric_type: str) -> str:
        """根据阈值获取状态"""
        try:
            warning_threshold = self.warning_thresholds.get(metric_type, float('inf'))
            critical_threshold = self.critical_thresholds.get(metric_type, float('inf'))
            
            if value >= critical_threshold:
                return "critical"
            elif value >= warning_threshold:
                return "warning"
            else:
                return "healthy"
                
        except Exception:
            return "unknown"
            
    def _save_health_results(self, results: List[HealthCheckResult]):
        """保存健康检查结果"""
        try:
            # 添加到历史记录
            self.health_history.extend(results)
            
            # 限制历史记录大小
            if len(self.health_history) > self.max_history_size:
                self.health_history = self.health_history[-self.max_history_size:]
                
        except Exception as e:
            logger.error(f"保存健康检查结果异常: {e}")
            
    def get_health_summary(self) -> Dict[str, Any]:
        """获取健康状态摘要"""
        try:
            if not self.health_history:
                return {'status': 'unknown', 'message': '暂无健康检查数据'}
                
            # 获取最近的检查结果
            recent_results = [r for r in self.health_history if (datetime.now() - r.check_time).total_seconds() < 300]
            
            if not recent_results:
                return {'status': 'unknown', 'message': '健康检查数据过期'}
                
            # 统计状态
            status_counts = {}
            for result in recent_results:
                status_counts[result.status] = status_counts.get(result.status, 0) + 1
                
            # 确定总体状态
            if status_counts.get('critical', 0) > 0:
                overall_status = 'critical'
            elif status_counts.get('warning', 0) > 0:
                overall_status = 'warning'
            else:
                overall_status = 'healthy'
                
            return {
                'status': overall_status,
                'total_checks': len(recent_results),
                'status_breakdown': status_counts,
                'last_check_time': max(r.check_time for r in recent_results).isoformat(),
                'critical_issues': [r.message for r in recent_results if r.status == 'critical'],
                'warnings': [r.message for r in recent_results if r.status == 'warning']
            }
            
        except Exception as e:
            logger.error(f"获取健康状态摘要异常: {e}")
            return {'status': 'error', 'message': f'获取摘要异常: {e}'}

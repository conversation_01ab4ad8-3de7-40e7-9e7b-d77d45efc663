"""
优化版飞书通知器
实现3条通知方案：信号开仓、交易完成、历史统计
"""
import json
import time
import hmac
import hashlib
import base64
from datetime import datetime
from typing import Dict, Any, Optional, List
from loguru import logger
import requests

from ..models import TradingSignal


class TradingSession:
    """交易会话，跟踪单次交易的完整生命周期"""
    
    def __init__(self, signal: TradingSignal):
        self.signal = signal
        self.symbol = signal.symbol
        self.exchange = signal.exchange
        self.start_time = datetime.now()
        
        # 交易状态
        self.status = "信号检测"
        self.open_price: Optional[float] = None
        self.open_time: Optional[datetime] = None
        self.current_price: Optional[float] = None
        self.peak_price: Optional[float] = None
        
        # 平仓记录
        self.partial_close_price: Optional[float] = None
        self.partial_close_time: Optional[datetime] = None
        self.partial_close_percentage: float = 0
        
        self.final_close_price: Optional[float] = None
        self.final_close_time: Optional[datetime] = None
        self.close_reason: Optional[str] = None
        
        # 盈亏计算
        self.total_profit: float = 0
        self.profit_percentage: float = 0
        
        # 错误信息
        self.error_message: Optional[str] = None
        
        # 固定参数
        self.amount = 100  # 固定投入金额
        self.leverage = 10  # 固定杠杆
    
    def update_open(self, price: float):
        """更新开仓信息"""
        self.open_price = price
        self.open_time = datetime.now()
        self.current_price = price
        self.peak_price = price
        self.status = "已开仓"
    
    def update_partial_close(self, price: float, percentage: float):
        """更新部分平仓信息"""
        self.partial_close_price = price
        self.partial_close_time = datetime.now()
        self.partial_close_percentage = percentage
        self.current_price = price
        
        # 更新峰值价格
        if price > self.peak_price:
            self.peak_price = price
    
    def update_final_close(self, price: float, reason: str):
        """更新最终平仓信息"""
        self.final_close_price = price
        self.final_close_time = datetime.now()
        self.close_reason = reason
        self.current_price = price
        self.status = "已平仓"
        
        # 更新峰值价格
        if price > self.peak_price:
            self.peak_price = price
        
        # 计算总盈亏
        if self.open_price and self.open_price > 0:
            # 部分平仓盈亏
            partial_profit = 0
            if self.partial_close_price and self.partial_close_percentage > 0:
                partial_profit = (self.partial_close_price - self.open_price) / self.open_price * (self.partial_close_percentage / 100)
            
            # 最终平仓盈亏
            final_percentage = 100 - self.partial_close_percentage
            final_profit = (price - self.open_price) / self.open_price * (final_percentage / 100)
            
            # 总盈亏百分比
            self.profit_percentage = (partial_profit + final_profit) * 100
            
            # 总盈亏金额
            self.total_profit = self.amount * self.leverage * (partial_profit + final_profit)
    
    def update_error(self, error_message: str):
        """更新错误信息"""
        self.error_message = error_message
        self.status = "交易失败"


class FeishuOptimizedNotifier:
    """优化版飞书通知器 - 3条通知方案"""
    
    def __init__(self, config: Dict[str, Any]):
        self.webhook_url = config.get('webhook_url', '')
        self.webhook_sign = config.get('webhook_sign', '')
        self.enabled = config.get('enabled', False) and bool(self.webhook_url)
        
        # 活跃交易会话
        self.active_sessions: Dict[str, TradingSession] = {}
        
        # 历史统计
        self.completed_sessions: List[TradingSession] = []
        
        if self.enabled:
            logger.info("优化版飞书通知器初始化完成")
        else:
            logger.warning("优化版飞书通知器未启用或配置不完整")
    
    def _generate_sign(self, timestamp: int) -> str:
        """生成签名"""
        if not self.webhook_sign:
            return ""
        
        string_to_sign = f"{timestamp}\n{self.webhook_sign}"
        hmac_code = hmac.new(
            string_to_sign.encode("utf-8"),
            digestmod=hashlib.sha256
        ).digest()
        sign = base64.b64encode(hmac_code).decode('utf-8')
        return sign
    
    def _send_message(self, content: Dict[str, Any]) -> bool:
        """发送消息到飞书"""
        if not self.enabled:
            return False
            
        try:
            timestamp = int(time.time())
            sign = self._generate_sign(timestamp)
            
            payload = {
                "timestamp": str(timestamp),
                "sign": sign,
                **content
            }
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    logger.debug("飞书消息发送成功")
                    return True
                else:
                    logger.error(f"飞书消息发送失败: {result}")
                    return False
            else:
                logger.error(f"飞书API请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"发送飞书消息异常: {e}")
            return False
    
    def send_signal_open_notification(self, signal: TradingSignal, open_price: float) -> str:
        """发送信号开仓通知（通知1/3）"""
        try:
            # 创建交易会话
            session = TradingSession(signal)
            session.update_open(open_price)
            session_id = f"{signal.symbol}_{int(time.time())}"
            self.active_sessions[session_id] = session
            
            # 构建信号开仓通知
            signal_time = signal.timestamp.strftime('%H:%M:%S')
            open_time = session.open_time.strftime('%H:%M:%S')
            
            # 提取市值信息
            market_cap = signal.metadata.get('market_cap') if signal.metadata else None
            market_cap_text = f" | 市值: ${market_cap:,.0f}" if market_cap else ""
            
            content = {
                "msg_type": "interactive",
                "card": {
                    "elements": [
                        {
                            "tag": "div",
                            "text": {
                                "content": f"**代币**: {signal.symbol}  **交易所**: {signal.exchange.upper()}{market_cap_text}\n\n**信号时间**: {signal_time}  **开仓时间**: {open_time}\n\n**开仓价格**: ${open_price:.6f}\n**投入金额**: ${session.amount}  **杠杆**: {session.leverage}x\n\n**信号内容**:\n{signal.content[:120]}{'...' if len(signal.content) > 120 else ''}",
                                "tag": "lark_md"
                            }
                        }
                    ],
                    "header": {
                        "title": {
                            "content": f"🎯 {signal.symbol} 上币开仓",
                            "tag": "plain_text"
                        },
                        "template": "blue"
                    }
                }
            }
            
            success = self._send_message(content)
            if success:
                logger.info(f"发送信号开仓通知: {session_id}")
                return session_id
            else:
                return ""
                
        except Exception as e:
            logger.error(f"发送信号开仓通知异常: {e}")
            return ""
    
    def update_session_partial_close(self, session_id: str, price: float, percentage: float):
        """更新会话部分平仓信息"""
        if session_id in self.active_sessions:
            self.active_sessions[session_id].update_partial_close(price, percentage)
    
    def send_trading_complete_notification(self, session_id: str, final_price: float, reason: str) -> bool:
        """发送交易完成通知（通知2/3）"""
        try:
            if session_id not in self.active_sessions:
                logger.error(f"交易会话不存在: {session_id}")
                return False
            
            session = self.active_sessions[session_id]
            session.update_final_close(final_price, reason)
            
            # 移动到已完成会话
            self.completed_sessions.append(session)
            del self.active_sessions[session_id]
            
            # 计算时间和盈亏
            duration = (session.final_close_time - session.open_time).total_seconds()
            open_time = session.open_time.strftime('%H:%M:%S')
            partial_time = session.partial_close_time.strftime('%H:%M:%S') if session.partial_close_time else ""
            final_time = session.final_close_time.strftime('%H:%M:%S')
            
            # 计算各阶段盈亏
            partial_profit = ((session.partial_close_price - session.open_price) / session.open_price * 100) if session.partial_close_price else 0
            final_profit = ((session.final_close_price - session.open_price) / session.open_price * 100) if session.final_close_price else 0
            peak_profit = ((session.peak_price - session.open_price) / session.open_price * 100) if session.peak_price else 0
            
            # 确定通知颜色和图标
            if session.profit_percentage > 0:
                icon, color = "💰", "green"
            else:
                icon, color = "📉", "red"
            
            content = {
                "msg_type": "interactive",
                "card": {
                    "elements": [
                        {
                            "tag": "div",
                            "text": {
                                "content": f"**代币**: {session.symbol}  **交易所**: {session.exchange.upper()}\n\n**开仓价格**: ${session.open_price:.6f}  ({open_time})\n\n**部分平仓**: ${session.partial_close_price:.6f}  ({partial_time})\n└ 平仓比例: {session.partial_close_percentage}%  收益: {partial_profit:+.2f}%\n\n**最终平仓**: ${session.final_close_price:.6f}  ({final_time})\n└ 平仓比例: {100-session.partial_close_percentage}%  收益: {final_profit:+.2f}%\n\n**峰值价格**: ${session.peak_price:.6f}  ({peak_profit:+.2f}%)\n**平仓原因**: {session.close_reason}\n\n**总盈亏**: ${session.total_profit:+.2f}  ({session.profit_percentage:+.2f}%)\n**交易时长**: {int(duration)}秒",
                                "tag": "lark_md"
                            }
                        }
                    ],
                    "header": {
                        "title": {
                            "content": f"{icon} {session.symbol} 交易结果",
                            "tag": "plain_text"
                        },
                        "template": color
                    }
                }
            }
            
            success = self._send_message(content)
            if success:
                logger.info(f"发送交易完成通知: {session_id}")
            return success
            
        except Exception as e:
            logger.error(f"发送交易完成通知异常: {e}")
            return False

    def send_trading_failed_notification(self, session_id: str, error_message: str) -> bool:
        """发送交易失败通知"""
        try:
            if session_id not in self.active_sessions:
                logger.error(f"交易会话不存在: {session_id}")
                return False

            session = self.active_sessions[session_id]
            session.update_error(error_message)

            # 移动到已完成会话
            self.completed_sessions.append(session)
            del self.active_sessions[session_id]

            content = {
                "msg_type": "interactive",
                "card": {
                    "elements": [
                        {
                            "tag": "div",
                            "text": {
                                "content": f"**代币**: {session.symbol}  **交易所**: {session.exchange.upper()}\n\n**失败原因**: {error_message}\n\n**信号时间**: {session.signal.timestamp.strftime('%H:%M:%S')}\n**检测时间**: {session.start_time.strftime('%H:%M:%S')}",
                                "tag": "lark_md"
                            }
                        }
                    ],
                    "header": {
                        "title": {
                            "content": f"❌ {session.symbol} 交易失败",
                            "tag": "plain_text"
                        },
                        "template": "red"
                    }
                }
            }

            success = self._send_message(content)
            if success:
                logger.info(f"发送交易失败通知: {session_id}")
            return success

        except Exception as e:
            logger.error(f"发送交易失败通知异常: {e}")
            return False

    def send_statistics_notification(self) -> bool:
        """发送历史统计通知（通知3/3）"""
        try:
            if not self.completed_sessions:
                logger.warning("没有已完成的交易会话，跳过统计通知")
                return True

            # 计算统计数据
            total_trades = len(self.completed_sessions)
            successful_trades = len([s for s in self.completed_sessions if s.status == "已平仓" and s.profit_percentage > 0])
            failed_trades = len([s for s in self.completed_sessions if s.status == "交易失败"])

            total_profit = sum([s.total_profit for s in self.completed_sessions if s.status == "已平仓"])
            win_rate = (successful_trades / total_trades * 100) if total_trades > 0 else 0

            # 最近交易摘要（最多显示5个）
            recent_sessions = self.completed_sessions[-5:]
            recent_summary = []
            for session in recent_sessions:
                if session.status == "已平仓":
                    profit_text = f"${session.total_profit:+.2f}"
                else:
                    profit_text = "失败"
                recent_summary.append(f"{session.symbol}: {profit_text}")

            # 确定通知颜色和图标
            if total_profit > 0:
                icon, color = "📈", "green"
                title = "交易统计报告 - 盈利"
            elif total_profit < 0:
                icon, color = "📉", "red"
                title = "交易统计报告 - 亏损"
            else:
                icon, color = "📊", "blue"
                title = "交易统计报告"

            content = {
                "msg_type": "interactive",
                "card": {
                    "elements": [
                        {
                            "tag": "div",
                            "text": {
                                "content": f"**总交易**: {total_trades}  **成功**: {successful_trades}  **失败**: {failed_trades}\n\n**总盈亏**: ${total_profit:+.2f}\n**胜率**: {win_rate:.1f}%\n\n**最近交易**:\n{chr(10).join(recent_summary)}\n\n**统计时间**: {datetime.now().strftime('%m-%d %H:%M')}",
                                "tag": "lark_md"
                            }
                        }
                    ],
                    "header": {
                        "title": {
                            "content": f"{icon} 交易统计",
                            "tag": "plain_text"
                        },
                        "template": color
                    }
                }
            }

            success = self._send_message(content)
            logger.info("发送历史统计通知")
            return success

        except Exception as e:
            logger.error(f"发送统计通知异常: {e}")
            return False

    async def send_message(self, message: str) -> bool:
        """发送简单文本消息"""
        try:
            content = {
                "msg_type": "text",
                "content": {
                    "text": message
                }
            }
            return self._send_message(content)
        except Exception as e:
            logger.error(f"发送简单消息异常: {e}")
            return False

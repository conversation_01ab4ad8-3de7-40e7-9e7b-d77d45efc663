"""
Web管理界面服务器
提供系统监控、控制和配置管理的Web界面
"""
import asyncio
import json
import os
import yaml
from datetime import datetime
from typing import Dict, Any, List

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Request, Depends
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from loguru import logger
from .admin_auth import initialize_admin_auth, get_auth_manager, verify_admin_token

# 添加系统路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

try:
    from analysis.price_performance_analyzer import price_analyzer
    from database.exchange_database_manager import UnifiedDatabaseManager, ListingEvent
except ImportError:
    # 备用导入方式
    price_analyzer = None
    UnifiedDatabaseManager = None
    ListingEvent = None

# 临时注释掉导入，专注于上币数据功能
# from ..core.system_controller import SystemController, SystemStatus
# from ..analysis.binance_announcement_analyzer import BinanceAnnouncementAnalyzer


class WebServer:
    """Web服务器"""
    
    def __init__(self, system_controller=None, config: Dict[str, Any] = None):
        self.system_controller = system_controller
        self.config = config or {}
        self.app = FastAPI(title="交易机器人管理系统", version="1.0.0")

        # WebSocket连接管理
        self.active_connections: List[WebSocket] = []

        # 初始化管理员认证
        self.auth_manager = initialize_admin_auth(self.config)

        # Binance历史分析器（暂时注释）
        # self.binance_analyzer = BinanceAnnouncementAnalyzer()

        # 配置CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        # 设置路由
        self._setup_routes()

        # 注册状态变化回调（暂时注释）
        if self.system_controller:
            self.system_controller.add_status_callback(self._on_status_change)

        logger.info("Web服务器初始化完成")

    def _validate_config_params(self, config: dict):
        """验证配置参数"""
        strategy = config.get('strategy', {})

        # 验证策略参数 - 放宽限制，提供更多灵活性
        if 'amount' in strategy:
            amount = strategy['amount']
            if not isinstance(amount, (int, float)) or amount < 1 or amount > 100000:
                raise ValueError("交易金额必须在1-100000之间")

        if 'leverage' in strategy:
            leverage = strategy['leverage']
            if not isinstance(leverage, int) or leverage < 1 or leverage > 125:
                raise ValueError("杠杆倍数必须在1-125之间")

        if 'first_close_time' in strategy:
            close_time = strategy['first_close_time']
            if not isinstance(close_time, int) or close_time < 5 or close_time > 3600:
                raise ValueError("首次平仓时间必须在5-3600秒之间")

        if 'first_close_percentage' in strategy:
            percentage = strategy['first_close_percentage']
            if not isinstance(percentage, int) or percentage < 1 or percentage > 99:
                raise ValueError("首次平仓比例必须在1-99%之间")

    async def _update_config_file(self, new_config: dict):
        """更新配置文件 - 只更新内存配置，避免重写文件"""
        import yaml
        import os

        config_file = "config.yaml"

        # 读取当前配置到内存
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                current_config = yaml.safe_load(f)
        else:
            current_config = {}

        # 记录配置变化日志
        logger.info("⚠️ 配置更新：为保持注释格式，仅更新内存配置。如需持久化，请手动编辑config.yaml文件")

        # 更新币安API配置
        if 'binance' in new_config:
            if 'trading' not in current_config:
                current_config['trading'] = {}
            if 'binance' not in current_config['trading']:
                current_config['trading']['binance'] = {}

            binance_data = new_config['binance']
            if binance_data.get('api_key'):
                current_config['trading']['binance']['api_key'] = binance_data['api_key']
            if binance_data.get('api_secret'):
                current_config['trading']['binance']['api_secret'] = binance_data['api_secret']
            current_config['trading']['binance']['testnet'] = binance_data.get('testnet', False)

        # 更新跟单API配置
        if 'copy_trading' in new_config:
            if 'copy_trading' not in current_config:
                current_config['copy_trading'] = {}

            copy_data = new_config['copy_trading']
            if copy_data.get('api_url'):
                current_config['copy_trading']['api_url'] = copy_data['api_url']
            if copy_data.get('api_key'):
                current_config['copy_trading']['api_key'] = copy_data['api_key']

        # 更新策略配置
        if 'strategy' in new_config:
            if 'fixed_strategy' not in current_config:
                current_config['fixed_strategy'] = {}
            current_config['fixed_strategy'].update(new_config['strategy'])

        # 更新交易模式
        if 'trading' in new_config:
            if 'trading' not in current_config:
                current_config['trading'] = {}
            if 'mode' in new_config['trading']:
                current_config['trading']['mode'] = new_config['trading']['mode']

        # 更新飞书通知配置
        if 'notifications' in new_config:
            if 'notifications' not in current_config:
                current_config['notifications'] = {}
            if 'feishu' not in current_config['notifications']:
                current_config['notifications']['feishu'] = {}

            notif_data = new_config['notifications']
            current_config['notifications']['feishu']['enabled'] = notif_data.get('feishu_enabled', False)
            if notif_data.get('feishu_webhook'):
                current_config['notifications']['feishu']['webhook_url'] = notif_data['feishu_webhook']
            if notif_data.get('feishu_secret'):
                current_config['notifications']['feishu']['secret'] = notif_data['feishu_secret']

        # 同时更新内存配置和文件配置
        self.config.update(current_config)

        # 只更新特定的配置值，保留注释和格式
        self._update_config_values(config_file, current_config)

        logger.info("✅ 配置已同步到内存和文件，立即生效")

        # 触发配置重载
        await self._trigger_config_reload()

    def _update_config_values(self, config_file: str, new_config: dict):
        """更新配置文件中的特定值，保留注释和格式"""
        try:
            # 简化版本：只更新关键配置值，保留文件结构
            # 这里可以实现更复杂的配置文件解析和更新逻辑
            # 目前先使用简单的YAML写入，后续可以优化
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True, indent=2)

        except Exception as e:
            logger.error(f"更新配置文件值失败: {e}")

    async def _trigger_config_reload(self):
        """触发配置重载"""
        try:
            # 方法1: 通过系统控制器触发配置重载
            if self.system_controller and hasattr(self.system_controller, 'config_manager'):
                await self.system_controller.config_manager.reload_config()
                logger.info("配置重载完成")
                return

            # 方法2: 直接更新配置文件触发文件监控
            logger.info("通过文件更新触发配置重载")
            import yaml
            import os

            config_file = "config.yaml"
            if os.path.exists(config_file):
                # 读取当前配置文件
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = yaml.safe_load(f)

                # 更新文件配置
                if 'fixed_strategy' in file_config:
                    file_config['fixed_strategy'].update(self.config.get('fixed_strategy', {}))

                # 写回文件（这会触发文件监控）
                with open(config_file, 'w', encoding='utf-8') as f:
                    yaml.dump(file_config, f, default_flow_style=False, allow_unicode=True)

                logger.info("配置文件已更新，等待文件监控触发重载")
            else:
                logger.warning("配置文件不存在，无法触发重载")

        except Exception as e:
            logger.warning(f"配置重载失败: {e}")
            # 即使重载失败，也不抛出异常，因为配置文件已经更新
        
    def _setup_routes(self):
        """设置路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard():
            """主页面"""
            return self._get_dashboard_html()



        @self.app.get("/analysis", response_class=HTMLResponse)
        async def analysis_page():
            """历史分析页面"""
            return self._get_analysis_html()

        @self.app.get("/trading-history", response_class=HTMLResponse)
        async def trading_history_page():
            """交易历史页面"""
            return self._get_trading_history_html()



        @self.app.get("/api/listing-data")
        async def get_listing_data():
            """获取上币数据"""
            try:
                return await self._get_listing_data()
            except Exception as e:
                logger.error(f"获取上币数据异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/refresh-listing-data")
        async def refresh_listing_data():
            """刷新上币数据 - 通过监控系统自动获取最近3个月数据"""
            try:
                return await self._refresh_listing_data()
            except Exception as e:
                logger.error(f"刷新上币数据异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))
            
        @self.app.get("/api/status")
        async def get_status():
            """获取系统状态"""
            try:
                if self.system_controller:
                    health = self.system_controller.get_system_health()
                    return {
                        "status": health.status.value,
                        "uptime": health.uptime_seconds,
                        "cpu_percent": health.cpu_percent,
                        "memory_percent": health.memory_percent,
                        "active_monitors": health.active_monitors,
                        "active_positions": health.active_positions,
                        "total_signals": health.total_signals,
                        "total_trades": health.total_trades,
                        "error_count": health.error_count,
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    return {
                        "status": "unknown",
                        "message": "系统控制器未初始化",
                        "timestamp": datetime.now().isoformat()
                    }
            except Exception as e:
                logger.error(f"获取系统状态异常: {e}")
                return {
                    "status": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }

        @self.app.get("/api/system-status")
        async def get_system_status():
            """获取详细系统状态 - 用于监控和诊断"""
            try:
                if not self.system_controller:
                    return {
                        "status": "error",
                        "message": "系统控制器未初始化",
                        "components": {},
                        "error_rate": 100.0,
                        "uptime_seconds": 0,
                        "timestamp": datetime.now().isoformat()
                    }

                # 获取系统健康状态
                health = self.system_controller.get_system_health()

                # 获取组件状态
                components = {}
                if hasattr(self.system_controller, 'components'):
                    for name, component in self.system_controller.components.items():
                        components[name] = component is not None

                # 计算错误率
                error_rate = 0.0
                if hasattr(self.system_controller, '_calculate_error_rate'):
                    error_rate = self.system_controller._calculate_error_rate() * 100

                return {
                    "status": health.status.value if health else "unknown",
                    "components": components,
                    "error_rate": error_rate,
                    "uptime_seconds": health.uptime_seconds if health else 0,
                    "cpu_percent": health.cpu_percent if health else 0,
                    "memory_percent": health.memory_percent if health else 0,
                    "active_monitors": health.active_monitors if health else 0,
                    "active_positions": health.active_positions if health else 0,
                    "total_signals": health.total_signals if health else 0,
                    "total_trades": health.total_trades if health else 0,
                    "error_count": health.error_count if health else 0,
                    "timestamp": datetime.now().isoformat()
                }
            except Exception as e:
                logger.error(f"获取详细系统状态异常: {e}")
                return {
                    "status": "error",
                    "error": str(e),
                    "components": {},
                    "error_rate": 100.0,
                    "uptime_seconds": 0,
                    "timestamp": datetime.now().isoformat()
                }
                
        @self.app.post("/api/start")
        async def start_system():
            """启动系统"""
            try:
                success = await self.system_controller.start_system()
                return {"success": success, "message": "系统启动成功" if success else "系统启动失败"}
            except Exception as e:
                logger.error(f"启动系统异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))
                
        @self.app.post("/api/stop")
        async def stop_system():
            """停止系统"""
            try:
                success = await self.system_controller.stop_system("Web界面请求")
                return {"success": success, "message": "系统停止成功" if success else "系统停止失败"}
            except Exception as e:
                logger.error(f"停止系统异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))
                
        @self.app.post("/api/restart")
        async def restart_system():
            """重启系统"""
            try:
                success = await self.system_controller.restart_system("Web界面请求")
                return {"success": success, "message": "系统重启成功" if success else "系统重启失败"}
            except Exception as e:
                logger.error(f"重启系统异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))
                
        @self.app.get("/api/trading-stats")
        async def get_trading_stats():
            """获取交易统计"""
            try:
                from ..database import unified_db
                stats = unified_db.get_trading_statistics()

                # 获取活跃持仓数量
                import sqlite3
                conn = sqlite3.connect(unified_db.db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM trading_history WHERE status = 'open'")
                active_positions = cursor.fetchone()[0]
                conn.close()

                stats['active_positions'] = active_positions
                return stats

            except Exception as e:
                logger.error(f"获取交易统计异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/log-stats")
        async def get_log_stats():
            """获取日志统计信息"""
            try:
                from ..core.log_manager import get_log_manager
                log_manager = get_log_manager()

                if log_manager:
                    stats = log_manager.get_log_statistics()
                    return stats
                else:
                    return {"error": "日志管理器未初始化"}

            except Exception as e:
                logger.error(f"获取日志统计异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/cleanup-logs")
        async def cleanup_logs(token: str = Depends(verify_admin_token)):
            """手动清理日志 - 需要管理员权限"""
            try:
                from ..core.log_manager import get_log_manager
                log_manager = get_log_manager()

                if log_manager:
                    log_manager.force_cleanup()
                    return {"success": True, "message": "日志清理完成"}
                else:
                    return {"success": False, "message": "日志管理器未初始化"}

            except Exception as e:
                logger.error(f"手动清理日志异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/trading-mode")
        async def get_trading_mode():
            """获取当前交易模式"""
            try:
                from ..trading.fixed_strategy import get_fixed_strategy
                strategy = get_fixed_strategy()

                if strategy and strategy.trading_router:
                    status = strategy.trading_router.get_status()
                    return {
                        "success": True,
                        "current_mode": status['current_mode'],
                        "normal_available": status['normal_trader_available'],
                        "copy_available": status['copy_trader_available']
                    }
                else:
                    return {"success": False, "message": "交易策略未初始化"}

            except Exception as e:
                logger.error(f"获取交易模式异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        # ==================== 管理员认证API ====================

        @self.app.post("/api/admin/login")
        async def admin_login(request: Request, login_data: dict):
            """管理员登录"""
            try:
                password = login_data.get('password')
                client_ip = request.client.host

                if not password:
                    raise HTTPException(status_code=400, detail="密码不能为空")

                # 验证密码
                if not self.auth_manager.verify_password(password):
                    logger.warning(f"管理员登录失败: 密码错误 from {client_ip}")
                    raise HTTPException(status_code=401, detail="密码错误")

                # 检查IP白名单
                if not self.auth_manager.check_ip_whitelist(client_ip):
                    logger.warning(f"管理员登录失败: IP不在白名单 {client_ip}")
                    raise HTTPException(status_code=403, detail="IP地址不允许")

                # 生成令牌
                token = self.auth_manager.generate_token(client_ip)
                logger.info(f"管理员登录成功 from {client_ip}")

                return {
                    "success": True,
                    "token": token,
                    "expires_in": self.auth_manager.token_expire_hours * 3600
                }

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"管理员登录异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/admin/logout")
        async def admin_logout(token: str = Depends(verify_admin_token)):
            """管理员登出"""
            try:
                self.auth_manager.revoke_token(token)
                return {"success": True, "message": "已退出登录"}
            except Exception as e:
                logger.error(f"管理员登出异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/admin/verify")
        async def verify_admin_status(token: str = Depends(verify_admin_token)):
            """验证管理员状态"""
            return {"success": True, "admin": True}



        # ==================== 管理员配置API ====================

        @self.app.get("/api/admin/config")
        async def get_config(token: str = Depends(verify_admin_token)):
            """获取当前配置"""
            try:
                # 从配置文件读取最新配置，确保与文件同步
                import yaml
                config_file = "config.yaml"
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        file_config = yaml.safe_load(f)
                    # 使用文件配置，确保获取最新数据
                    current_config = file_config
                    # 同时更新内存配置
                    self.config = current_config
                except Exception as e:
                    logger.warning(f"读取配置文件失败，使用内存配置: {e}")
                    # 如果读取文件失败，使用内存配置
                    current_config = self.config

                # 获取各部分配置（适应新的配置结构）
                binance_config = current_config.get('trading', {}).get('binance', {})
                copy_trading_config = current_config.get('copy_trading', {})
                fixed_strategy_config = current_config.get('fixed_strategy', {})
                feishu_config = current_config.get('notifications', {}).get('feishu', {})

                return {
                    "binance": {
                        "api_key": binance_config.get('api_key', ''),
                        "api_secret": '***' if binance_config.get('api_secret') else '',  # 隐藏密钥
                        "testnet": binance_config.get('testnet', False)
                    },
                    "copy_trading": {
                        "api_url": copy_trading_config.get('api_url', ''),
                        "api_key": copy_trading_config.get('api_key', '')
                    },
                    "strategy": {
                        "amount": fixed_strategy_config.get('amount', 100),
                        "leverage": fixed_strategy_config.get('leverage', 10),
                        "first_close_time": fixed_strategy_config.get('first_close_time', 56),
                        "first_close_percentage": fixed_strategy_config.get('first_close_percentage', 80),
                        "drawdown_threshold": fixed_strategy_config.get('drawdown_threshold', 20)
                    },
                    "trading": {
                        "mode": current_config.get('trading', {}).get('mode', 'normal')
                    },
                    "notifications": {
                        "feishu_enabled": feishu_config.get('enabled', False),
                        "feishu_webhook": feishu_config.get('webhook_url', ''),
                        "feishu_secret": '***' if feishu_config.get('secret') else ''  # 隐藏密钥
                    }
                }

            except Exception as e:
                logger.error(f"获取配置异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/admin/config")
        async def update_config(new_config: dict, token: str = Depends(verify_admin_token)):
            """更新配置"""
            try:
                # 验证配置参数
                self._validate_config_params(new_config)

                # 更新配置文件
                await self._update_config_file(new_config)

                # 触发配置重载
                await self._trigger_config_reload()

                logger.info(f"配置更新成功: {new_config}")
                return {"success": True, "message": "配置更新成功"}

            except Exception as e:
                logger.error(f"配置更新失败: {e}")
                raise HTTPException(status_code=500, detail=f"配置更新失败: {str(e)}")

        # ==================== 管理员系统控制API ====================

        @self.app.post("/api/start")
        async def start_system(token: str = Depends(verify_admin_token)):
            """启动系统 - 需要管理员权限"""
            try:
                if self.system_controller:
                    await self.system_controller.start()
                    return {"success": True, "message": "系统启动成功"}
                else:
                    return {"success": False, "message": "系统控制器未初始化"}
            except Exception as e:
                logger.error(f"启动系统异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/stop")
        async def stop_system(token: str = Depends(verify_admin_token)):
            """停止交易系统 - 需要管理员权限（保持Web界面运行）"""
            try:
                if self.system_controller:
                    # 只停止交易相关组件，保持Web服务器运行
                    success = await self.system_controller.stop_trading_components()
                    return {"success": success, "message": "交易系统停止成功，Web界面保持运行" if success else "交易系统停止失败"}
                else:
                    return {"success": False, "message": "系统控制器未初始化"}
            except Exception as e:
                logger.error(f"停止交易系统异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/restart")
        async def restart_system(token: str = Depends(verify_admin_token)):
            """重启系统 - 需要管理员权限"""
            try:
                if self.system_controller:
                    await self.system_controller.restart()
                    return {"success": True, "message": "系统重启成功"}
                else:
                    return {"success": False, "message": "系统控制器未初始化"}
            except Exception as e:
                logger.error(f"重启系统异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/runtime-validation")
        async def get_runtime_validation():
            """获取运行时配置验证结果"""
            try:
                if self.system_controller:
                    results = await self.system_controller.get_runtime_validation_results()
                    if results:
                        return results
                    else:
                        return {
                            "status": "no_data",
                            "message": "暂无验证结果",
                            "timestamp": datetime.now().isoformat()
                        }
                else:
                    return {
                        "status": "error",
                        "message": "系统控制器未初始化",
                        "timestamp": datetime.now().isoformat()
                    }
            except Exception as e:
                logger.error(f"获取运行时验证结果异常: {e}")
                return {
                    "status": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }

        @self.app.post("/api/runtime-validation/trigger")
        async def trigger_runtime_validation(request: dict = None, token: str = Depends(verify_admin_token)):
            """手动触发运行时配置验证 - 需要管理员权限"""
            try:
                if self.system_controller:
                    config_type = request.get('config_type') if request else None
                    results = await self.system_controller.validate_runtime_config(config_type)
                    return results
                else:
                    return {
                        "status": "error",
                        "message": "系统控制器未初始化"
                    }
            except Exception as e:
                logger.error(f"触发运行时验证异常: {e}")
                return {
                    "status": "error",
                    "error": str(e)
                }

        @self.app.post("/api/switch-trading-mode")
        async def switch_trading_mode(request: dict, token: str = Depends(verify_admin_token)):
            """切换交易模式 - 需要管理员权限"""
            try:
                new_mode = request.get('mode')
                if new_mode not in ['normal', 'copy_trading']:
                    return {"success": False, "message": "无效的交易模式"}

                from ..trading.fixed_strategy import get_fixed_strategy
                strategy = get_fixed_strategy()

                if strategy and strategy.trading_router:
                    success = await strategy.trading_router.switch_mode(new_mode)
                    if success:
                        return {"success": True, "message": f"已切换到{new_mode}模式"}
                    else:
                        return {"success": False, "message": "切换失败"}
                else:
                    return {"success": False, "message": "交易策略未初始化"}

            except Exception as e:
                logger.error(f"切换交易模式异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/trading-history")
        async def get_trading_history(limit: int = 50):
            """获取交易历史"""
            try:
                from ..database import unified_db
                history = unified_db.get_trading_history(limit=limit)

                # 转换为字典格式
                history_data = []
                for record in history:
                    history_data.append({
                        'order_id': record.order_id,
                        'symbol': record.symbol,
                        'side': record.side,
                        'entry_price': record.entry_price,
                        'exit_price': record.exit_price,
                        'amount': record.amount,
                        'leverage': record.leverage,
                        'profit': record.profit,
                        'profit_percent': record.profit_percent,
                        'status': record.status,
                        'entry_time': record.entry_time.isoformat() if record.entry_time else None,
                        'exit_time': record.exit_time.isoformat() if record.exit_time else None,
                        'duration_minutes': record.duration_minutes,
                        'exchange_source': record.exchange_source,
                        'signal_source': record.signal_source,
                        'close_reason': record.close_reason
                    })

                return {
                    'success': True,
                    'data': history_data,
                    'total': len(history_data)
                }

            except Exception as e:
                logger.error(f"获取交易历史异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/trading-signals")
        async def get_trading_signals(limit: int = 50):
            """获取交易信号历史"""
            try:
                from ..database import unified_db
                signals = unified_db.get_trading_signals(limit=limit)

                # 转换为字典格式
                signals_data = []
                for signal in signals:
                    signals_data.append({
                        'id': signal.id,
                        'source': signal.source,
                        'symbol': signal.symbol,
                        'timestamp': signal.timestamp.isoformat(),
                        'content': signal.content[:100] + '...' if len(signal.content) > 100 else signal.content,
                        'exchange': signal.exchange,
                        'confidence': signal.confidence,
                        'priority': signal.priority,
                        'processed': signal.processed
                    })

                return {
                    'success': True,
                    'data': signals_data,
                    'total': len(signals_data)
                }

            except Exception as e:
                logger.error(f"获取交易信号异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/exchanges")
        async def get_exchanges():
            """获取支持的交易所列表"""
            return {
                "exchanges": [
                    {"id": "upbit", "name": "Upbit", "emoji": "🏆", "description": "最激进策略"},
                    {"id": "binance", "name": "Binance", "emoji": "🥈", "description": "积极策略"},
                    {"id": "bithumb", "name": "Bithumb", "emoji": "🥉", "description": "保守策略"},
                    {"id": "coinbase", "name": "Coinbase", "emoji": "📊", "description": "最保守策略"}
                ]
            }



        @self.app.get("/api/exchanges/{exchange}/statistics")
        async def get_exchange_statistics_api(exchange: str):
            """获取指定交易所的统计数据"""
            try:
                from ..database import unified_db

                stats = unified_db.get_statistics(exchange)
                return {
                    "exchange": exchange,
                    "statistics": stats
                }
            except Exception as e:
                logger.error(f"获取 {exchange} 统计数据异常: {e}")
                return {"error": str(e)}

        @self.app.get("/api/statistics")
        async def get_statistics():
            """获取统计信息"""
            try:
                if 'signal_processor' in self.system_controller.components:
                    processor = self.system_controller.components['signal_processor']
                    stats = processor.get_statistics()
                    return stats
                else:
                    return {"error": "信号处理器未初始化"}
            except Exception as e:
                logger.error(f"获取统计信息异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))
                
        @self.app.get("/api/positions")
        async def get_positions():
            """获取持仓信息"""
            try:
                if 'signal_processor' in self.system_controller.components:
                    processor = self.system_controller.components['signal_processor']
                    if hasattr(processor, 'position_manager'):
                        positions = processor.position_manager.get_position_summary()
                        return positions
                return {"error": "持仓管理器未初始化"}
            except Exception as e:
                logger.error(f"获取持仓信息异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        # 固定策略API
        @self.app.get("/api/fixed-strategy/status")
        async def get_fixed_strategy_status():
            """获取固定策略状态"""
            try:
                from ..trading.fixed_strategy import get_fixed_strategy
                fixed_strategy = get_fixed_strategy()

                if fixed_strategy:
                    active_positions = fixed_strategy.get_active_positions()
                    return {
                        "enabled": True,
                        "active_positions": len(active_positions),
                        "positions": [
                            {
                                "symbol": pos.symbol,
                                "status": pos.status.value,
                                "open_time": pos.open_time.isoformat(),
                                "remaining_percentage": pos.remaining_percentage
                            }
                            for pos in active_positions
                        ]
                    }
                else:
                    return {"enabled": False, "active_positions": 0, "positions": []}
            except Exception as e:
                logger.error(f"获取固定策略状态异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/fixed-strategy/manual-close/{symbol}")
        async def manual_close_position(symbol: str):
            """手动平仓"""
            try:
                from ..trading.fixed_strategy import get_fixed_strategy
                fixed_strategy = get_fixed_strategy()

                if fixed_strategy:
                    success = await fixed_strategy.manual_close_position(symbol)
                    return {"success": success, "message": "手动平仓成功" if success else "手动平仓失败"}
                else:
                    return {"success": False, "message": "固定策略未初始化"}
            except Exception as e:
                logger.error(f"手动平仓异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        # 黑名单管理API
        @self.app.get("/api/blacklist")
        async def get_blacklist():
            """获取代币黑名单"""
            try:
                from ..core.token_blacklist_manager import get_blacklist_manager
                blacklist_manager = get_blacklist_manager()
                return blacklist_manager.get_blacklist_info()
            except Exception as e:
                logger.error(f"获取黑名单异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.delete("/api/blacklist/{token}")
        async def remove_from_blacklist(token: str):
            """从黑名单中移除代币"""
            try:
                from ..core.token_blacklist_manager import get_blacklist_manager
                blacklist_manager = get_blacklist_manager()
                success = blacklist_manager.remove_token_from_blacklist(token)
                return {"success": success, "message": f"代币 {token} 移除成功" if success else f"代币 {token} 不在黑名单中"}
            except Exception as e:
                logger.error(f"移除黑名单代币异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        # Binance历史分析API
        @self.app.get("/api/exchanges")
        async def get_exchanges():
            """获取支持的交易所列表"""
            return {
                'data_source': 'binance',
                'description': '基于Binance历史行情数据的分析',
                'exchanges': [
                    {'id': 'upbit', 'name': 'Upbit', 'emoji': '🏆', 'description': '最激进策略 • 高波动率 (数据来源: Binance)'},
                    {'id': 'binance', 'name': 'Binance', 'emoji': '🥈', 'description': '积极策略 • 中等波动率 (数据来源: Binance)'},
                    {'id': 'coinbase', 'name': 'Coinbase', 'emoji': '📊', 'description': '最保守策略 • 低波动率 (数据来源: Binance)'},
                    {'id': 'bithumb', 'name': 'Bithumb', 'emoji': '🥉', 'description': '保守策略 • 稳定波动率 (数据来源: Binance)'}
                ]
            }

        @self.app.get("/api/exchanges/{exchange}/data")
        async def get_exchange_data(exchange: str, limit: int = 10):
            """获取交易所数据"""
            try:
                logger.info(f"📊 获取 {exchange.upper()} 数据...")

                # 尝试从数据库获取数据
                try:
                    from ..database import unified_db

                    data = unified_db.get_exchange_data(exchange, limit)
                    stats = unified_db.get_statistics(exchange)

                    if data:
                        # 转换为Web界面需要的格式
                        formatted_data = []
                        for item in data:
                            formatted_data.append({
                                'symbol': item.symbol,
                                'price': item.price,
                                'volume': item.volume,
                                'change_24h': item.change_24h or 0,
                                'timestamp': item.timestamp.isoformat(),
                                'listing_type': item.listing_type or 'unknown'
                            })

                        return {
                            "exchange": exchange,
                            "data": formatted_data,
                            "statistics": stats,
                            "total_records": len(formatted_data),
                            "source": "database"
                        }
                except Exception as db_error:
                    logger.warning(f"数据库查询失败: {db_error}")
                    # 返回空数据
                    statistics = {'total_listings': 0, 'avg_performance': 0}
                    history_data = []

                # 转换为Web界面需要的格式
                formatted_data = []
                for item in history_data:
                    formatted_item = {
                        'symbol': item['symbol'],
                        'announcement_time': item['announcement_time'],
                        'listing_price': item['announcement_price'],
                        'initial_market_cap': 0,  # Binance API不直接提供市值
                        'peak_market_cap': 0,     # 可以根据价格变化估算

                        # 价格变化数据
                        'change_1min': item['change_1min'], 'change_5min': item['change_5min'],
                        'change_15min': item['change_15min'], 'change_30min': item['change_30min'],
                        'change_1hour': item['change_1hour'], 'change_4hour': item['change_4hour'],
                        'change_12hour': item['change_12hour'], 'change_1day': item['change_1day'],
                        'change_3day': item['change_3day'],

                        # 关键指标
                        'max_gain': item['max_gain'], 'max_loss': item['max_loss'],
                        'peak_time_hours': item['peak_time_hours'],
                        'max_drawdown_from_peak': item['max_drawdown_from_peak'],
                        'volatility_3day': item['volatility_3day'],

                        # 价格时间线
                        'price_timeline': item['price_timeline']
                    }
                    formatted_data.append(formatted_item)

                return {
                    'exchange': exchange,
                    'data_source': 'binance',
                    'total_records': len(formatted_data),
                    'statistics': statistics,
                    'data': formatted_data
                }

            except Exception as e:
                logger.error(f"❌ 获取 {exchange} 数据失败: {e}")
                return {
                    'exchange': exchange,
                    'error': str(e),
                    'data_source': 'binance',
                    'total_records': 0,
                    'statistics': {'total_listings': 0},
                    'data': []
                }

        # 分析API已移除 - 固定策略不需要历史分析

            except Exception as e:
                logger.error(f"❌ 分析公告异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/contracts")
        async def get_contracts():
            """获取合约信息"""
            try:
                if 'signal_processor' in self.system_controller.components:
                    processor = self.system_controller.components['signal_processor']
                    if hasattr(processor, 'contract_manager'):
                        summary = processor.contract_manager.get_contracts_summary()
                        return summary
                return {"error": "合约管理器未初始化"}
            except Exception as e:
                logger.error(f"获取合约信息异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/contracts/refresh")
        async def refresh_contracts():
            """刷新合约信息"""
            try:
                if 'signal_processor' in self.system_controller.components:
                    processor = self.system_controller.components['signal_processor']
                    if hasattr(processor, 'contract_manager'):
                        success = await processor.contract_manager.refresh_contracts()
                        return {"success": success, "message": "合约信息刷新成功" if success else "合约信息刷新失败"}
                return {"error": "合约管理器未初始化"}
            except Exception as e:
                logger.error(f"刷新合约信息异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))
                
        @self.app.get("/api/analysis-stats")
        async def get_analysis_stats():
            """获取分析统计信息"""
            try:
                if not price_analyzer:
                    return {"success": False, "message": "价格分析器未初始化"}

                import sqlite3
                conn = sqlite3.connect('exchange_data.db')
                cursor = conn.cursor()

                # 统计信息
                cursor.execute("SELECT COUNT(*) FROM listing_events")
                total_tokens = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM price_performance WHERE analyzed = 1")
                analyzed_tokens = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM price_performance WHERE analyzed = 0")
                failed_tokens = cursor.fetchone()[0]

                # 最佳表现
                cursor.execute('''
                    SELECT symbol, exchange, max_gain, time_to_peak
                    FROM price_performance
                    WHERE analyzed = 1 AND max_gain IS NOT NULL
                    ORDER BY max_gain DESC
                    LIMIT 5
                ''')
                top_performers = cursor.fetchall()

                conn.close()

                return {
                    "success": True,
                    "stats": {
                        "total_tokens": total_tokens,
                        "analyzed_tokens": analyzed_tokens,
                        "failed_tokens": failed_tokens,
                        "success_rate": round((analyzed_tokens / total_tokens * 100), 1) if total_tokens > 0 else 0,
                        "top_performers": [
                            {
                                "symbol": row[0],
                                "exchange": row[1],
                                "max_gain": round(row[2], 2),
                                "time_to_peak": row[3]
                            } for row in top_performers
                        ]
                    }
                }

            except Exception as e:
                logger.error(f"获取分析统计失败: {e}")
                return {"success": False, "message": f"获取统计失败: {str(e)}"}

        @self.app.post("/api/analyze-prices")
        async def analyze_prices():
            """分析价格表现"""
            try:
                if not price_analyzer:
                    return {"success": False, "message": "价格分析器未初始化"}

                # 异步执行价格分析
                import asyncio

                def run_analysis():
                    try:
                        # 强制重新分析所有代币
                        price_analyzer.analyze_all_listings(force_reanalyze=True)
                        return {"success": True, "message": "合约价格重新分析完成，已更新所有有USDT永续合约的代币数据"}
                    except Exception as e:
                        logger.error(f"价格分析失败: {e}")
                        return {"success": False, "message": f"分析失败: {str(e)}"}

                # 在后台运行分析
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(None, run_analysis)

                return result

            except Exception as e:
                logger.error(f"启动价格分析失败: {e}")
                return {"success": False, "message": f"启动分析失败: {str(e)}"}

        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket连接"""
            await self._handle_websocket(websocket)
            
    async def _handle_websocket(self, websocket: WebSocket):
        """处理WebSocket连接"""
        await websocket.accept()
        self.active_connections.append(websocket)
        
        try:
            while True:
                try:
                    # 发送实时状态更新
                    health = self.system_controller.get_system_health()
                    await websocket.send_json({
                        "type": "status_update",
                        "data": {
                            "status": health.status.value,
                            "uptime": health.uptime_seconds,
                            "cpu_percent": health.cpu_percent,
                            "memory_percent": health.memory_percent,
                            "active_positions": health.active_positions,
                            "error_count": health.error_count,
                            "timestamp": datetime.now().isoformat()
                        }
                    })

                    await asyncio.sleep(5)  # 每5秒更新一次

                except asyncio.CancelledError:
                    # 优雅处理取消异常
                    break
                except Exception as e:
                    logger.error(f"WebSocket发送数据异常: {e}")
                    break

        except WebSocketDisconnect:
            pass
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"WebSocket异常: {e}")
        finally:
            # 清理连接
            if websocket in self.active_connections:
                self.active_connections.remove(websocket)
            try:
                if websocket.client_state != websocket.client_state.DISCONNECTED:
                    await websocket.close()
            except:
                pass
                
    async def _on_status_change(self, status):
        """系统状态变化回调"""
        try:
            message = {
                "type": "status_change",
                "data": {
                    "status": status.value,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            # 广播给所有WebSocket连接
            for connection in self.active_connections.copy():
                try:
                    await connection.send_json(message)
                except:
                    self.active_connections.remove(connection)
                    
        except Exception as e:
            logger.error(f"状态变化通知异常: {e}")
            
    def _get_dashboard_html(self) -> str:
        """获取仪表板HTML - 在原有界面基础上添加管理员功能"""
        return self._old_dashboard_html()



    def _old_dashboard_html(self) -> str:
        """原来的仪表板HTML（备用）"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能交易机器人管理系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .container { width: 100%; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .header h1 { margin-bottom: 10px; font-size: 2.2em; }
        .nav { display: flex; gap: 15px; margin-top: 15px; }
        .nav a { color: white; text-decoration: none; padding: 10px 20px; border-radius: 6px; transition: all 0.3s; background: rgba(255,255,255,0.1); }
        .nav a:hover, .nav a.active { background: rgba(255,255,255,0.2); transform: translateY(-2px); }
        .status-bar { display: flex; align-items: center; gap: 20px; margin-top: 15px; }
        .status-indicator { padding: 8px 16px; border-radius: 25px; font-weight: bold; font-size: 0.9em; transition: all 0.3s; }
        .status-running { background: #27ae60; color: white; }
        .status-stopped { background: #e74c3c; color: white; }
        .status-starting { background: #f39c12; color: white; }
        .status-unknown { background: #95a5a6; color: white; }

        /* 按钮状态样式 */
        .btn:disabled { opacity: 0.6; cursor: not-allowed; }
        .btn-loading { position: relative; }
        .btn-loading::after { content: ''; position: absolute; width: 16px; height: 16px; margin: auto; border: 2px solid transparent; border-top-color: #ffffff; border-radius: 50%; animation: spin 1s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

        /* 交易所选择器样式 */
        .exchange-selector { background: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .exchange-selector h2 { margin-bottom: 15px; color: #2c3e50; }
        .exchange-buttons { display: flex; gap: 15px; flex-wrap: wrap; }
        .exchange-btn {
            padding: 15px 25px; border: none; border-radius: 8px; cursor: pointer;
            font-size: 1.1em; font-weight: bold; transition: all 0.3s;
            display: flex; align-items: center; gap: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .exchange-btn:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
        .exchange-btn.active { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.3); }
        .exchange-btn.upbit { background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white; }
        .exchange-btn.binance { background: linear-gradient(135deg, #f9ca24, #f0932b); color: white; }
        .exchange-btn.bithumb { background: linear-gradient(135deg, #6c5ce7, #a29bfe); color: white; }
        .exchange-btn.coinbase { background: linear-gradient(135deg, #00b894, #00cec9); color: white; }
        .status-stopped { background: #e74c3c; }
        .status-starting { background: #f39c12; }
        .status-error { background: #c0392b; }
        .controls { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn:hover { opacity: 0.8; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .metric-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric-title { font-size: 14px; color: #666; margin-bottom: 10px; }
        .metric-value { font-size: 24px; font-weight: bold; color: #2c3e50; }
        .logs { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .log-entry { padding: 5px 0; border-bottom: 1px solid #eee; font-family: monospace; font-size: 12px; }
        .progress-bar { width: 100%; height: 20px; background: #ecf0f1; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: #3498db; transition: width 0.3s; }
        .strategy-item, .analysis-item, .trade-item {
            display: flex; justify-content: space-between; align-items: center;
            padding: 8px 0; border-bottom: 1px solid #eee;
        }
        .strategy-exchange, .analysis-exchange { font-weight: bold; color: #2c3e50; }
        .strategy-details, .analysis-performance { color: #666; font-size: 14px; }
        .trade-time { color: #666; font-size: 12px; }
        .trade-symbol { font-weight: bold; color: #2c3e50; }
        .trade-exchange { color: #3498db; font-size: 12px; }
        .trade-action { color: #f39c12; font-size: 12px; }
        .trade-result { font-weight: bold; }
        .positive { color: #27ae60; }
        .negative { color: #e74c3c; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 交易机器人管理系统</h1>
            <!-- 管理员登录按钮 -->
            <button id="admin-login-btn" style="position: absolute; top: 20px; right: 20px; background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); padding: 8px 16px; border-radius: 20px; cursor: pointer;" onclick="showAdminLogin()">
                🔧 管理员登录
            </button>
            <div class="nav" style="margin-top: 10px;">
                <a href="/" style="color: white; text-decoration: none; padding: 5px 10px; border-radius: 4px; background: #3498db;">🏠 主页</a>
                <a href="/analysis" style="color: white; text-decoration: none; padding: 5px 10px; border-radius: 4px;">📊 历史分析</a>
                <a href="/trading-history" style="color: white; text-decoration: none; padding: 5px 10px; border-radius: 4px;">📈 交易记录</a>
            </div>
            <div class="status-bar" style="margin-top: 10px;">
                <span class="status-indicator" id="status">系统状态: 未知</span>
                <span id="uptime">运行时间: --</span>
            </div>
        </div>
        


        <!-- 管理员控制面板 -->
        <div id="admin-controls" class="controls" style="display: none; background: #fff3cd; border: 2px solid #ffc107; border-radius: 8px;">
            <h3 style="color: #856404;">🔧 管理员控制面板</h3>
            <div style="margin: 10px 0;">
                <h4 style="color: #495057;">🎛️ 系统控制</h4>
                <button class="btn btn-success" onclick="startSystem()">🚀 启动系统</button>
                <button class="btn btn-danger" onclick="stopSystem()">🛑 停止系统</button>
                <button class="btn btn-warning" onclick="restartSystem()">🔄 重启系统</button>
            </div>
            <div style="margin: 10px 0;">
                <h4 style="color: #495057;">⚡ 交易管理</h4>
                <button class="btn btn-primary" onclick="switchTradingMode()">🔄 切换交易模式</button>
                <button class="btn btn-primary" onclick="showConfigModal()">⚙️ 修改配置</button>
            </div>
            <div style="margin: 10px 0;">
                <h4 style="color: #495057;">🔧 系统管理</h4>
                <button class="btn btn-primary" onclick="refreshData()">🔄 刷新数据</button>
                <button class="btn btn-warning" onclick="cleanupLogs()">🧹 清理日志</button>
            </div>
            <div style="margin-top: 15px; border-top: 1px solid #ddd; padding-top: 10px;">
                <button class="btn" style="background: #6c757d; color: white;" onclick="adminLogout()">🚪 退出管理</button>
            </div>
        </div>
        
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-title">CPU使用率</div>
                <div class="metric-value" id="cpu">--%</div>
                <div class="progress-bar"><div class="progress-fill" id="cpu-bar"></div></div>
            </div>
            <div class="metric-card">
                <div class="metric-title">内存使用率</div>
                <div class="metric-value" id="memory">--%</div>
                <div class="progress-bar"><div class="progress-fill" id="memory-bar"></div></div>
            </div>
            <div class="metric-card">
                <div class="metric-title">活跃监控</div>
                <div class="metric-value" id="monitors">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">活跃持仓</div>
                <div class="metric-value" id="positions">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">总信号数</div>
                <div class="metric-value" id="signals">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">总交易数</div>
                <div class="metric-value" id="trades">--</div>
            </div>
        </div>

        <!-- 交易统计区域 -->
        <div style="background: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 style="margin: 0; color: #2c3e50;">📊 实时交易统计</h3>
                <button onclick="refreshTradingStats()" class="btn btn-primary" style="padding: 8px 16px;">🔄 刷新</button>
            </div>

            <div class="metrics" id="trading-stats-detailed">
                <div class="metric-card">
                    <div class="metric-title">成功交易</div>
                    <div class="metric-value" id="successful-trades" style="color: #27ae60;">-</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">失败交易</div>
                    <div class="metric-value" id="failed-trades" style="color: #e74c3c;">-</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">平均盈利</div>
                    <div class="metric-value" id="avg-profit">$-</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">最大盈利</div>
                    <div class="metric-value" id="max-profit" style="color: #27ae60;">$-</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">最大亏损</div>
                    <div class="metric-value" id="max-loss" style="color: #e74c3c;">$-</div>
                </div>
            </div>
        </div>

        <!-- 最近信号区域 -->
        <div style="background: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 style="margin: 0; color: #2c3e50;">📡 最近交易信号</h3>
                <button onclick="refreshSignals()" class="btn btn-primary" style="padding: 8px 16px;">🔄 刷新</button>
            </div>

            <div id="recent-signals">
                <div style="text-align: center; color: #666; padding: 20px;">加载中...</div>
            </div>
        </div>






        
        <div class="logs">
            <h3>系统日志</h3>
            <div id="log-container" style="height: 300px; overflow-y: auto;">
                <div class="log-entry">系统启动中...</div>
            </div>
        </div>
    </div>
    
    <script>
        let ws = null;
        
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            ws = new WebSocket(`${protocol}//${window.location.host}/ws`);
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'status_update') {
                    updateStatus(data.data);
                } else if (data.type === 'status_change') {
                    addLogEntry(`系统状态变更: ${data.data.status}`);
                }
            };
            
            ws.onclose = function() {
                setTimeout(connectWebSocket, 5000);
            };
        }
        
        function updateStatus(data) {
            const statusEl = document.getElementById('status');
            if (!statusEl) return; // 安全检查

            // 更新状态显示
            let statusText = '';
            let statusClass = 'status-indicator';

            switch(data.status) {
                case 'running':
                    statusText = '🟢 系统运行中';
                    statusClass += ' status-running';
                    break;
                case 'stopped':
                    statusText = '🔴 系统已停止';
                    statusClass += ' status-stopped';
                    break;
                case 'starting':
                    statusText = '🟡 系统启动中';
                    statusClass += ' status-starting';
                    break;
                default:
                    statusText = '⚪ 状态未知';
                    statusClass += ' status-unknown';
            }

            statusEl.textContent = statusText;
            statusEl.className = statusClass;

            // 安全地更新其他信息
            const uptimeEl = document.getElementById('uptime');
            if (uptimeEl && data.uptime !== undefined) {
                uptimeEl.textContent = `运行时间: ${formatUptime(data.uptime)}`;
            }

            // 安全地更新CPU和内存信息
            const cpuEl = document.getElementById('cpu');
            const memoryEl = document.getElementById('memory');

            if (cpuEl && data.cpu_percent !== undefined) {
                cpuEl.textContent = `${data.cpu_percent.toFixed(1)}%`;
            }

            if (memoryEl && data.memory_percent !== undefined) {
                memoryEl.textContent = `${data.memory_percent.toFixed(1)}%`;
            }

            // 安全地更新统计信息
            const monitorsEl = document.getElementById('monitors');
            const positionsEl = document.getElementById('positions');
            const signalsEl = document.getElementById('signals');
            const tradesEl = document.getElementById('trades');
            const cpuBarEl = document.getElementById('cpu-bar');
            const memoryBarEl = document.getElementById('memory-bar');

            if (monitorsEl) monitorsEl.textContent = data.active_monitors || 0;
            if (positionsEl) positionsEl.textContent = data.active_positions || 0;
            if (signalsEl) signalsEl.textContent = data.total_signals || 0;
            if (tradesEl) tradesEl.textContent = data.total_trades || 0;

            if (cpuBarEl && data.cpu_percent !== undefined) {
                cpuBarEl.style.width = `${data.cpu_percent}%`;
            }
            if (memoryBarEl && data.memory_percent !== undefined) {
                memoryBarEl.style.width = `${data.memory_percent}%`;
            }
        }
        
        function formatUptime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}h ${minutes}m`;
        }
        
        function addLogEntry(message) {
            const container = document.getElementById('log-container');
            if (!container) return; // 安全检查

            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }
        
        async function startSystem() {
            const btn = event.target;
            const originalText = btn.innerHTML;

            try {
                // 显示加载状态
                btn.innerHTML = '⏳ 启动中...';
                btn.disabled = true;

                const response = await fetch('/api/start', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    addLogEntry(`✅ ${result.message}`);
                    btn.innerHTML = '✅ 启动成功';
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }, 2000);
                } else {
                    addLogEntry(`❌ ${result.message}`);
                    btn.innerHTML = '❌ 启动失败';
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }, 2000);
                }
            } catch (error) {
                addLogEntry(`❌ 启动失败: ${error.message}`);
                btn.innerHTML = '❌ 启动失败';
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 2000);
            }
        }

        async function stopSystem() {
            const btn = event.target;
            const originalText = btn.innerHTML;

            try {
                // 显示加载状态
                btn.innerHTML = '⏳ 停止中...';
                btn.disabled = true;

                const response = await fetch('/api/stop', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    addLogEntry(`✅ ${result.message}`);
                    btn.innerHTML = '✅ 停止成功';
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }, 2000);
                } else {
                    addLogEntry(`❌ ${result.message}`);
                    btn.innerHTML = '❌ 停止失败';
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }, 2000);
                }
            } catch (error) {
                addLogEntry(`❌ 停止失败: ${error.message}`);
                btn.innerHTML = '❌ 停止失败';
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 2000);
            }
        }

        async function restartSystem() {
            const btn = event.target;
            const originalText = btn.innerHTML;

            try {
                // 显示加载状态
                btn.innerHTML = '⏳ 重启中...';
                btn.disabled = true;

                const response = await fetch('/api/restart', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    addLogEntry(`✅ ${result.message}`);
                    btn.innerHTML = '✅ 重启成功';
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }, 3000);
                } else {
                    addLogEntry(`❌ ${result.message}`);
                    btn.innerHTML = '❌ 重启失败';
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }, 2000);
                }
            } catch (error) {
                addLogEntry(`❌ 重启失败: ${error.message}`);
                btn.innerHTML = '❌ 重启失败';
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 2000);
            }
        }
        
        async function refreshData() {
            const btn = event.target;
            const originalText = btn.innerHTML;

            try {
                // 显示加载状态
                btn.innerHTML = '⏳ 刷新中...';
                btn.disabled = true;

                const response = await fetch('/api/status');
                const data = await response.json();
                updateStatus(data);

                addLogEntry('✅ 数据刷新成功');
                btn.innerHTML = '✅ 刷新成功';

                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 1000);

            } catch (error) {
                addLogEntry(`❌ 刷新失败: ${error.message}`);
                btn.innerHTML = '❌ 刷新失败';

                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 2000);
            }
        }

        // 交易所选择功能
        let currentExchange = null;

        async function selectExchange(exchange) {
            try {
                // 更新按钮状态
                document.querySelectorAll('.exchange-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelector(`.exchange-btn.${exchange}`).classList.add('active');

                currentExchange = exchange;

                // 显示加载状态
                const dataDiv = document.getElementById('exchange-data');
                dataDiv.style.display = 'block';
                document.getElementById('exchange-title').innerHTML = `📊 ${exchange.toUpperCase()} 数据加载中...`;

                // 获取交易所数据
                const response = await fetch(`/api/exchanges/${exchange}/data?limit=10`);
                const result = await response.json();

                if (result.error) {
                    throw new Error(result.error);
                }

                displayExchangeData(result);

            } catch (error) {
                console.error('获取交易所数据失败:', error);
                document.getElementById('exchange-title').innerHTML = `❌ 获取 ${exchange.toUpperCase()} 数据失败: ${error.message}`;
            }
        }

        function displayExchangeData(result) {
            const exchange = result.exchange;
            const data = result.data || [];
            const stats = result.statistics || {};

            // 更新标题
            const exchangeNames = {
                'upbit': '🏆 Upbit (最激进策略) - Binance数据',
                'binance': '🥈 Binance (积极策略) - Binance数据',
                'bithumb': '🥉 Bithumb (保守策略) - Binance数据',
                'coinbase': '📊 Coinbase (最保守策略) - Binance数据'
            };

            document.getElementById('exchange-title').innerHTML = exchangeNames[exchange] || exchange.toUpperCase();

            // 显示统计数据 (使用Binance API字段)
            const statsHtml = `
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.2em; font-weight: bold; color: #2c3e50;">📊 监控上币数</div>
                    <div style="font-size: 2em; color: #3498db; margin: 5px 0;">${stats.total_listings || 0}</div>
                    <div style="font-size: 0.8em; color: #666;">个代币</div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.2em; font-weight: bold; color: #2c3e50;">📈 平均1小时涨幅</div>
                    <div style="font-size: 2em; color: #27ae60; margin: 5px 0;">${stats.avg_1hour_change || 0}%</div>
                    <div style="font-size: 0.8em; color: #666;">公告后1小时</div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.2em; font-weight: bold; color: #2c3e50;">🏆 平均最大涨幅</div>
                    <div style="font-size: 2em; color: #f39c12; margin: 5px 0;">${stats.avg_max_gain || 0}%</div>
                    <div style="font-size: 0.8em; color: #666;">3天内峰值</div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.2em; font-weight: bold; color: #2c3e50;">⏰ 平均达峰时间</div>
                    <div style="font-size: 2em; color: #e74c3c; margin: 5px 0;">${stats.avg_peak_time_hours || 0}</div>
                    <div style="font-size: 0.8em; color: #666;">小时</div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.2em; font-weight: bold; color: #2c3e50;">📉 平均最大回撤</div>
                    <div style="font-size: 2em; color: #e74c3c; margin: 5px 0;">${stats.avg_max_drawdown || 0}%</div>
                    <div style="font-size: 0.8em; color: #666;">从峰值回撤</div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.2em; font-weight: bold; color: #2c3e50;">✅ 盈利成功率</div>
                    <div style="font-size: 2em; color: #9b59b6; margin: 5px 0;">${stats.success_rate || 0}%</div>
                    <div style="font-size: 0.8em; color: #666;">正收益比例</div>
                </div>
            `;

            document.getElementById('exchange-stats').innerHTML = statsHtml;

            // 显示历史数据
            let historyHtml = '';

            if (data.length === 0) {
                historyHtml = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <div style="font-size: 3em; margin-bottom: 15px;">📭</div>
                        <div style="font-size: 1.2em;">暂无 ${exchange.toUpperCase()} 历史数据</div>
                        <div style="margin-top: 10px; font-size: 0.9em;">系统运行后将自动收集数据</div>
                    </div>
                `;
            } else {
                historyHtml = '<div style="display: grid; gap: 15px;">';

                data.forEach((item, index) => {
                    const changeColor = item.change_1hour >= 0 ? '#27ae60' : '#e74c3c';
                    const changeIcon = item.change_1hour >= 0 ? '📈' : '📉';

                    historyHtml += `
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid ${changeColor};">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <div style="font-size: 1.4em; font-weight: bold; color: #2c3e50;">
                                    🪙 ${item.symbol}
                                </div>
                                <div style="font-size: 0.9em; color: #666;">
                                    📅 公告时间: ${item.announcement_time}
                                </div>
                            </div>

                            <!-- 基础信息 -->
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-bottom: 15px; font-size: 0.9em;">
                                <div>💰 公告时价格: $${parseFloat(item.listing_price).toFixed(8)}</div>
                                <div>⏰ 达峰时间: ${item.peak_time_hours}小时</div>
                            </div>

                            <!-- 价格变化时间线 -->
                            <div style="margin-bottom: 15px;">
                                <div style="font-weight: bold; margin-bottom: 8px; color: #2c3e50;">📈 3天价格变化时间线:</div>
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 8px; font-size: 0.85em;">
                                    <div>1分钟: <span style="color: ${item.change_1min >= 0 ? '#27ae60' : '#e74c3c'};">${item.change_1min >= 0 ? '+' : ''}${item.change_1min}%</span></div>
                                    <div>5分钟: <span style="color: ${item.change_5min >= 0 ? '#27ae60' : '#e74c3c'};">${item.change_5min >= 0 ? '+' : ''}${item.change_5min}%</span></div>
                                    <div>1小时: <span style="color: ${item.change_1hour >= 0 ? '#27ae60' : '#e74c3c'};">${item.change_1hour >= 0 ? '+' : ''}${item.change_1hour}%</span></div>
                                    <div>4小时: <span style="color: ${item.change_4hour >= 0 ? '#27ae60' : '#e74c3c'};">${item.change_4hour >= 0 ? '+' : ''}${item.change_4hour}%</span></div>
                                    <div>1天: <span style="color: ${item.change_1day >= 0 ? '#27ae60' : '#e74c3c'};">${item.change_1day >= 0 ? '+' : ''}${item.change_1day}%</span></div>
                                    <div>3天: <span style="color: ${item.change_3day >= 0 ? '#27ae60' : '#e74c3c'};">${item.change_3day >= 0 ? '+' : ''}${item.change_3day}%</span></div>
                                </div>
                            </div>

                            <!-- 关键指标 -->
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; font-size: 0.9em;">
                                <div style="color: #27ae60;">🏆 最大涨幅: +${item.max_gain}%</div>
                                <div style="color: #e74c3c;">📉 最大跌幅: ${item.max_loss}%</div>
                                <div style="color: #e74c3c;">📊 峰值回撤: ${item.max_drawdown_from_peak}%</div>
                                <div style="color: #666;">🌊 3天波动率: ${item.volatility_3day}%</div>
                            </div>
                        </div>
                    `;
                });

                historyHtml += '</div>';
            }

            document.getElementById('history-list').innerHTML = historyHtml;
        }


        
        // 刷新交易统计
        async function refreshTradingStats() {
            try {
                const response = await fetch('/api/trading-stats');
                const stats = await response.json();

                // 安全地更新基础统计
                const tradesEl = document.getElementById('trades');
                const positionsEl = document.getElementById('positions');
                if (tradesEl) tradesEl.textContent = stats.total_trades || 0;
                if (positionsEl) positionsEl.textContent = stats.active_positions || 0;

                // 安全地更新详细统计
                const successfulTradesEl = document.getElementById('successful-trades');
                const failedTradesEl = document.getElementById('failed-trades');
                const avgProfitEl = document.getElementById('avg-profit');
                const maxProfitEl = document.getElementById('max-profit');
                const maxLossEl = document.getElementById('max-loss');

                if (successfulTradesEl) successfulTradesEl.textContent = stats.successful_trades || 0;
                if (failedTradesEl) failedTradesEl.textContent = stats.failed_trades || 0;
                if (avgProfitEl) avgProfitEl.textContent = `$${(stats.avg_profit || 0).toFixed(2)}`;
                if (maxProfitEl) maxProfitEl.textContent = `$${(stats.max_profit || 0).toFixed(2)}`;
                if (maxLossEl) maxLossEl.textContent = `$${(stats.max_loss || 0).toFixed(2)}`;

                addLogEntry('✅ 交易统计已更新');
            } catch (error) {
                console.error('刷新交易统计失败:', error);
                addLogEntry(`❌ 刷新交易统计失败: ${error.message}`);
            }
        }

        // 刷新最近信号
        async function refreshSignals() {
            try {
                const response = await fetch('/api/trading-signals?limit=10');
                const result = await response.json();

                const container = document.getElementById('recent-signals');
                if (!container) return; // 安全检查

                if (result.success && result.data.length > 0) {
                    let html = '<div style="display: grid; gap: 10px;">';

                    result.data.forEach(signal => {
                        const timestamp = new Date(signal.timestamp).toLocaleString('zh-CN');
                        const statusColor = signal.processed ? '#27ae60' : '#f39c12';
                        const statusText = signal.processed ? '已处理' : '待处理';

                        html += `
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid ${statusColor};">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                    <div style="font-weight: bold; color: #2c3e50;">🪙 ${signal.symbol}</div>
                                    <div style="font-size: 0.9em; color: #666;">${timestamp}</div>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style="background: #3498db; color: white; padding: 2px 8px; border-radius: 4px; font-size: 0.8em;">${signal.source.toUpperCase()}</span>
                                    <span style="background: #9b59b6; color: white; padding: 2px 8px; border-radius: 4px; font-size: 0.8em;">${signal.exchange.toUpperCase()}</span>
                                    <span style="background: ${statusColor}; color: white; padding: 2px 8px; border-radius: 4px; font-size: 0.8em;">${statusText}</span>
                                </div>
                                <div style="font-size: 0.9em; color: #666; line-height: 1.4;">${signal.content}</div>
                                <div style="margin-top: 8px; font-size: 0.8em; color: #999;">
                                    置信度: ${(signal.confidence * 100).toFixed(0)}% | 优先级: ${signal.priority}
                                </div>
                            </div>
                        `;
                    });

                    html += '</div>';
                    container.innerHTML = html;
                } else {
                    container.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #666;">
                            <div style="font-size: 3em; margin-bottom: 15px;">📭</div>
                            <div style="font-size: 1.2em;">暂无交易信号</div>
                            <div style="margin-top: 10px; font-size: 0.9em;">系统运行后将自动收集信号</div>
                        </div>
                    `;
                }

                // 安全地更新信号总数
                const signalsEl = document.getElementById('signals');
                if (signalsEl) signalsEl.textContent = result.total || 0;
                addLogEntry('✅ 交易信号已更新');

            } catch (error) {
                console.error('刷新信号失败:', error);
                document.getElementById('recent-signals').innerHTML = `
                    <div style="text-align: center; padding: 20px; color: #e74c3c;">
                        ❌ 加载失败: ${error.message}
                    </div>
                `;
                addLogEntry(`❌ 刷新信号失败: ${error.message}`);
            }
        }

        // 初始化
        connectWebSocket();
        refreshData();
        refreshTradingStats();
        refreshSignals();
        setInterval(refreshData, 10000); // 每10秒刷新一次
        setInterval(refreshTradingStats, 30000); // 每30秒刷新交易统计
        setInterval(refreshSignals, 60000); // 每60秒刷新信号

        // 检查管理员状态
        checkAdminStatus();
    </script>

    <!-- 管理员登录弹窗 -->
    <div id="login-modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
        <div style="background-color: white; margin: 15% auto; padding: 30px; border-radius: 12px; width: 90%; max-width: 400px;">
            <h3 style="text-align: center; color: #495057; margin-bottom: 20px;">🔐 管理员登录</h3>
            <div style="margin: 15px 0;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500;">管理员密码:</label>
                <input type="password" id="admin-password" placeholder="请输入管理员密码" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
            </div>
            <div style="margin-top: 30px; text-align: center;">
                <button onclick="adminLogin()" style="margin: 0 10px; padding: 10px 30px; background: #007bff; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 16px;">🔑 登录</button>
                <button onclick="closeLoginModal()" style="margin: 0 10px; padding: 10px 30px; background: #6c757d; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 16px;">❌ 取消</button>
            </div>
        </div>
    </div>

    <!-- 配置修改弹窗 -->
    <div id="config-modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
        <div style="background-color: white; margin: 2% auto; padding: 30px; border-radius: 12px; width: 95%; max-width: 800px; max-height: 90vh; overflow-y: auto;">
            <h3 style="text-align: center; color: #495057; margin-bottom: 20px;">⚙️ 系统配置</h3>

            <!-- 交易所API配置 -->
            <div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                <h4 style="color: #495057; margin-bottom: 15px;">🔑 币安API配置</h4>
                <div style="margin: 10px 0; display: flex; align-items: center; gap: 10px;">
                    <label style="min-width: 120px; font-weight: 500;">API Key:</label>
                    <input type="text" id="binance-api-key" placeholder="输入币安API Key" style="flex: 1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div style="margin: 10px 0; display: flex; align-items: center; gap: 10px;">
                    <label style="min-width: 120px; font-weight: 500;">API Secret:</label>
                    <input type="password" id="binance-api-secret" placeholder="输入币安API Secret" style="flex: 1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div style="margin: 10px 0;">
                    <label><input type="checkbox" id="testnet-mode"> 使用测试网（开发调试用）</label>
                </div>
            </div>

            <!-- 带单API配置 -->
            <div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                <h4 style="color: #495057; margin-bottom: 15px;">📡 带单API配置</h4>
                <div style="margin: 10px 0; display: flex; align-items: center; gap: 10px;">
                    <label style="min-width: 120px; font-weight: 500;">API地址:</label>
                    <input type="text" id="copy-api-url" placeholder="输入带单API地址" style="flex: 1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div style="margin: 10px 0; display: flex; align-items: center; gap: 10px;">
                    <label style="min-width: 120px; font-weight: 500;">API Key:</label>
                    <input type="text" id="copy-api-key" placeholder="输入带单API Key" style="flex: 1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
            </div>

            <!-- 策略配置 -->
            <div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                <h4 style="color: #495057; margin-bottom: 15px;">🎯 交易策略</h4>
                <div style="margin: 10px 0; display: flex; align-items: center; gap: 10px;">
                    <label style="min-width: 150px; font-weight: 500;">交易金额 (USDT):</label>
                    <input type="number" id="strategy-amount" min="1" max="100000" step="1" style="flex: 1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div style="margin: 10px 0; display: flex; align-items: center; gap: 10px;">
                    <label style="min-width: 150px; font-weight: 500;">杠杆倍数:</label>
                    <input type="number" id="strategy-leverage" min="1" max="125" step="1" style="flex: 1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;" placeholder="1-125倍">
                </div>
                <div style="margin: 10px 0; display: flex; align-items: center; gap: 10px;">
                    <label style="min-width: 150px; font-weight: 500;">首次平仓时间 (秒):</label>
                    <input type="number" id="first-close-time" min="5" max="3600" step="1" style="flex: 1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;" placeholder="5-3600秒">
                </div>
                <div style="margin: 10px 0; display: flex; align-items: center; gap: 10px;">
                    <label style="min-width: 150px; font-weight: 500;">首次平仓比例 (%):</label>
                    <input type="number" id="first-close-percentage" min="1" max="99" step="1" style="flex: 1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;" placeholder="1-99%">
                </div>
                <div style="margin: 10px 0; display: flex; align-items: center; gap: 10px;">
                    <label style="min-width: 150px; font-weight: 500;">回撤阈值 (%):</label>
                    <input type="number" id="drawdown-threshold" min="1" max="100" step="1" style="flex: 1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;" placeholder="1-100%">
                </div>
            </div>

            <!-- 交易模式 -->
            <div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                <h4 style="color: #495057; margin-bottom: 15px;">🔄 交易模式</h4>
                <div style="margin: 10px 0; display: flex; align-items: center; gap: 10px;">
                    <label style="min-width: 150px; font-weight: 500;">当前模式:</label>
                    <select id="trading-mode" style="flex: 1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="normal">普通交易</option>
                        <option value="copy_trading">跟单交易</option>
                    </select>
                </div>
            </div>

            <!-- 通知设置 -->
            <div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                <h4 style="color: #495057; margin-bottom: 15px;">📢 飞书通知设置</h4>
                <div style="margin: 10px 0;">
                    <label><input type="checkbox" id="feishu-enabled"> 启用飞书通知</label>
                </div>
                <div style="margin: 10px 0; display: flex; align-items: center; gap: 10px;">
                    <label style="min-width: 120px; font-weight: 500;">Webhook URL:</label>
                    <input type="text" id="feishu-webhook" placeholder="输入飞书机器人Webhook地址" style="flex: 1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div style="margin: 10px 0; display: flex; align-items: center; gap: 10px;">
                    <label style="min-width: 120px; font-weight: 500;">签名密钥:</label>
                    <input type="password" id="feishu-secret" placeholder="输入飞书机器人签名密钥（可选）" style="flex: 1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
            </div>

            <div style="margin-top: 30px; text-align: center; border-top: 1px solid #e9ecef; padding-top: 20px;">
                <button onclick="saveConfig()" style="margin: 0 10px; padding: 10px 30px; background: #007bff; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 16px;">💾 保存配置</button>
                <button onclick="closeConfigModal()" style="margin: 0 10px; padding: 10px 30px; background: #6c757d; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 16px;">❌ 取消</button>
            </div>
        </div>
    </div>

    <script>
        // 管理员功能JavaScript
        let isAdmin = false;
        let adminToken = localStorage.getItem('admin_token');

        // 检查管理员状态
        async function checkAdminStatus() {
            if (adminToken) {
                const isValid = await verifyAdminToken();
                if (isValid) {
                    showAdminControls();
                } else {
                    localStorage.removeItem('admin_token');
                    adminToken = null;
                }
            }
        }

        // 显示管理员登录弹窗
        function showAdminLogin() {
            document.getElementById('login-modal').style.display = 'block';
            document.getElementById('admin-password').focus();
        }

        // 关闭登录弹窗
        function closeLoginModal() {
            document.getElementById('login-modal').style.display = 'none';
            document.getElementById('admin-password').value = '';
        }

        // 管理员登录
        async function adminLogin() {
            const password = document.getElementById('admin-password').value;
            if (!password) {
                alert('请输入密码');
                return;
            }

            try {
                const response = await fetch('/api/admin/login', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({password: password})
                });

                if (response.ok) {
                    const data = await response.json();
                    adminToken = data.token;
                    localStorage.setItem('admin_token', adminToken);
                    isAdmin = true;

                    closeLoginModal();
                    showAdminControls();
                    alert('✅ 管理员登录成功！');
                } else {
                    const error = await response.json();
                    alert('❌ 登录失败：' + error.detail);
                }
            } catch (error) {
                alert('❌ 登录异常：' + error.message);
            }
        }

        // 验证管理员令牌
        async function verifyAdminToken() {
            if (!adminToken) return false;

            try {
                const response = await fetch('/api/admin/verify', {
                    headers: {'Authorization': `Bearer ${adminToken}`}
                });
                return response.ok;
            } catch (error) {
                return false;
            }
        }

        // 显示管理员控制面板
        function showAdminControls() {
            document.getElementById('admin-controls').style.display = 'block';
            document.getElementById('admin-login-btn').textContent = '👤 已登录';
            document.getElementById('admin-login-btn').style.background = 'rgba(40,167,69,0.8)';
            isAdmin = true;
        }

        // 隐藏管理员控制面板
        function hideAdminControls() {
            document.getElementById('admin-controls').style.display = 'none';
            document.getElementById('admin-login-btn').textContent = '🔧 管理员登录';
            document.getElementById('admin-login-btn').style.background = 'rgba(255,255,255,0.2)';
            isAdmin = false;
        }

        // 管理员登出
        async function adminLogout() {
            if (adminToken) {
                try {
                    await fetch('/api/admin/logout', {
                        method: 'POST',
                        headers: {'Authorization': `Bearer ${adminToken}`}
                    });
                } catch (error) {
                    console.log('登出请求失败:', error);
                }
            }

            localStorage.removeItem('admin_token');
            adminToken = null;
            hideAdminControls();
            alert('✅ 已退出管理员模式');
        }

        // 显示配置修改弹窗
        async function showConfigModal() {
            try {
                const response = await fetch('/api/admin/config', {
                    headers: {'Authorization': `Bearer ${adminToken}`}
                });

                if (response.ok) {
                    const config = await response.json();

                    // 填充币安API配置
                    document.getElementById('binance-api-key').value = config.binance.api_key;
                    document.getElementById('binance-api-secret').value = config.binance.api_secret === '***' ? '' : config.binance.api_secret;
                    document.getElementById('testnet-mode').checked = config.binance.testnet;

                    // 填充带单API配置
                    document.getElementById('copy-api-url').value = config.copy_trading.api_url;
                    document.getElementById('copy-api-key').value = config.copy_trading.api_key;

                    // 填充策略配置
                    document.getElementById('strategy-amount').value = config.strategy.amount;
                    document.getElementById('strategy-leverage').value = config.strategy.leverage;
                    document.getElementById('first-close-time').value = config.strategy.first_close_time;
                    document.getElementById('first-close-percentage').value = config.strategy.first_close_percentage;
                    document.getElementById('drawdown-threshold').value = config.strategy.drawdown_threshold;

                    // 填充交易模式
                    document.getElementById('trading-mode').value = config.trading.mode;

                    // 填充通知配置
                    document.getElementById('feishu-enabled').checked = config.notifications.feishu_enabled;
                    document.getElementById('feishu-webhook').value = config.notifications.feishu_webhook;
                    document.getElementById('feishu-secret').value = config.notifications.feishu_secret === '***' ? '' : config.notifications.feishu_secret;

                    document.getElementById('config-modal').style.display = 'block';
                } else {
                    alert('❌ 获取配置失败');
                }
            } catch (error) {
                alert('❌ 获取配置异常：' + error.message);
            }
        }

        // 关闭配置弹窗
        function closeConfigModal() {
            document.getElementById('config-modal').style.display = 'none';
        }

        // 保存配置
        async function saveConfig() {
            const newConfig = {
                binance: {
                    api_key: document.getElementById('binance-api-key').value,
                    api_secret: document.getElementById('binance-api-secret').value,
                    testnet: document.getElementById('testnet-mode').checked
                },
                copy_trading: {
                    api_url: document.getElementById('copy-api-url').value,
                    api_key: document.getElementById('copy-api-key').value
                },
                strategy: {
                    amount: parseInt(document.getElementById('strategy-amount').value),
                    leverage: parseInt(document.getElementById('strategy-leverage').value),
                    first_close_time: parseInt(document.getElementById('first-close-time').value),
                    first_close_percentage: parseInt(document.getElementById('first-close-percentage').value),
                    drawdown_threshold: parseInt(document.getElementById('drawdown-threshold').value)
                },
                trading: {
                    mode: document.getElementById('trading-mode').value
                },
                notifications: {
                    feishu_enabled: document.getElementById('feishu-enabled').checked,
                    feishu_webhook: document.getElementById('feishu-webhook').value,
                    feishu_secret: document.getElementById('feishu-secret').value
                }
            };

            try {
                const response = await fetch('/api/admin/config', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(newConfig)
                });

                if (response.ok) {
                    alert('✅ 配置保存成功！');
                    closeConfigModal();
                    setTimeout(() => location.reload(), 1000);
                } else {
                    const error = await response.json();
                    alert('❌ 保存失败：' + error.detail);
                }
            } catch (error) {
                alert('❌ 保存异常：' + error.message);
            }
        }

        // 切换交易模式
        async function switchTradingMode() {
            if (confirm('确定要切换交易模式吗？')) {
                await callAdminAPI('/api/switch-trading-mode', 'POST', '切换交易模式');
            }
        }

        // 清理日志
        async function cleanupLogs() {
            if (confirm('确定要清理日志文件吗？')) {
                await callAdminAPI('/api/cleanup-logs', 'POST', '清理日志');
            }
        }

        // 通用管理员API调用
        async function callAdminAPI(url, method, action) {
            try {
                const response = await fetch(url, {
                    method: method,
                    headers: {'Authorization': `Bearer ${adminToken}`}
                });

                if (response.ok) {
                    const result = await response.json();
                    alert(`✅ ${action}成功`);
                } else {
                    const error = await response.json();
                    alert(`❌ ${action}失败：` + error.detail);
                }
            } catch (error) {
                alert(`❌ ${action}异常：` + error.message);
            }
        }

        // 修改原有的系统控制函数，添加管理员权限检查
        async function startSystem() {
            if (!isAdmin) {
                alert('❌ 需要管理员权限');
                return;
            }
            if (confirm('确定要启动系统吗？')) {
                await callAdminAPI('/api/start', 'POST', '启动系统');
            }
        }

        async function stopSystem() {
            if (!isAdmin) {
                alert('❌ 需要管理员权限');
                return;
            }
            if (confirm('⚠️ 确定要停止交易系统吗？这将中断所有交易！')) {
                await callAdminAPI('/api/stop', 'POST', '停止系统');
            }
        }

        async function restartSystem() {
            if (!isAdmin) {
                alert('❌ 需要管理员权限');
                return;
            }
            if (confirm('确定要重启系统吗？')) {
                await callAdminAPI('/api/restart', 'POST', '重启系统');
            }
        }

        // 回车键登录
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && document.getElementById('login-modal').style.display === 'block') {
                adminLogin();
            }
        });

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const loginModal = document.getElementById('login-modal');
            const configModal = document.getElementById('config-modal');

            if (event.target === loginModal) {
                closeLoginModal();
            }
            if (event.target === configModal) {
                closeConfigModal();
            }
        };
    </script>
</body>
</html>
        """

    def _get_trading_history_html(self) -> str:
        """获取交易历史页面HTML"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易记录 - 交易机器人</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f6fa; }
        .container { width: 100%; padding: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .nav { display: flex; gap: 20px; margin-top: 10px; }
        .nav a { color: white; text-decoration: none; padding: 5px 10px; border-radius: 4px; }
        .nav a:hover { background: rgba(255,255,255,0.2); }
        .nav a.active { background: #3498db; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 15px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stat-number { font-size: 24px; font-weight: bold; color: #3498db; }
        .stat-label { color: #666; margin-top: 5px; }
        .history-table { width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .history-table th, .history-table td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        .history-table th { background: #f8f9fa; font-weight: 600; }
        .history-table tr:hover { background: #f8f9fa; }
        .profit-positive { color: #27ae60; font-weight: bold; }
        .profit-negative { color: #e74c3c; font-weight: bold; }
        .status-open { background: #f39c12; color: white; padding: 2px 8px; border-radius: 4px; font-size: 0.8em; }
        .status-closed { background: #27ae60; color: white; padding: 2px 8px; border-radius: 4px; font-size: 0.8em; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        .btn-primary { background: #3498db; color: white; }
        .btn:hover { opacity: 0.8; }
        .loading { text-align: center; padding: 40px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈 交易记录管理</h1>
            <div class="nav">
                <a href="/">🏠 主页</a>
                <a href="/analysis">📊 历史分析</a>
                <a href="/trading-history" class="active">📈 交易记录</a>
            </div>
        </div>

        <!-- 交易统计 -->
        <div class="stats-grid" id="trading-stats">
            <div class="stat-card">
                <div class="stat-number" id="total-trades">-</div>
                <div class="stat-label">总交易数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="win-rate">-%</div>
                <div class="stat-label">胜率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="total-profit">$-</div>
                <div class="stat-label">总盈亏</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="active-positions">-</div>
                <div class="stat-label">活跃持仓</div>
            </div>
        </div>

        <!-- 控制按钮 -->
        <div style="margin-bottom: 20px;">
            <button class="btn btn-primary" onclick="refreshHistory()">🔄 刷新数据</button>
        </div>

        <!-- 交易历史表格 -->
        <div style="background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="padding: 20px; border-bottom: 1px solid #eee;">
                <h3 style="margin: 0; color: #2c3e50;">📊 交易历史记录</h3>
            </div>

            <div id="history-container">
                <div class="loading">加载中...</div>
            </div>
        </div>
    </div>

    <script>
        // 加载交易统计
        async function loadTradingStats() {
            try {
                const response = await fetch('/api/trading-stats');
                const stats = await response.json();

                document.getElementById('total-trades').textContent = stats.total_trades || 0;
                document.getElementById('win-rate').textContent = `${stats.win_rate || 0}%`;
                document.getElementById('total-profit').textContent = `$${(stats.total_profit || 0).toFixed(2)}`;
                document.getElementById('active-positions').textContent = stats.active_positions || 0;

                // 设置盈亏颜色
                const profitElement = document.getElementById('total-profit');
                const profit = stats.total_profit || 0;
                profitElement.className = profit >= 0 ? 'stat-number profit-positive' : 'stat-number profit-negative';

                // 设置胜率颜色
                const winRateElement = document.getElementById('win-rate');
                const winRate = stats.win_rate || 0;
                winRateElement.className = winRate >= 50 ? 'stat-number profit-positive' : 'stat-number profit-negative';

            } catch (error) {
                console.error('加载交易统计失败:', error);
            }
        }

        // 加载交易历史
        async function loadTradingHistory() {
            try {
                const response = await fetch('/api/trading-history?limit=100');
                const result = await response.json();

                const container = document.getElementById('history-container');

                if (result.success && result.data.length > 0) {
                    let html = `
                        <table class="history-table">
                            <thead>
                                <tr>
                                    <th>订单ID</th>
                                    <th>代币</th>
                                    <th>方向</th>
                                    <th>开仓价</th>
                                    <th>平仓价</th>
                                    <th>数量</th>
                                    <th>杠杆</th>
                                    <th>盈亏</th>
                                    <th>盈亏率</th>
                                    <th>状态</th>
                                    <th>开仓时间</th>
                                    <th>持仓时长</th>
                                    <th>信号源</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;

                    result.data.forEach(trade => {
                        const entryTime = trade.entry_time ? new Date(trade.entry_time).toLocaleString('zh-CN') : '-';
                        const exitTime = trade.exit_time ? new Date(trade.exit_time).toLocaleString('zh-CN') : '-';
                        const duration = trade.duration_minutes ? `${Math.floor(trade.duration_minutes / 60)}h${trade.duration_minutes % 60}m` : '-';

                        const profitClass = trade.profit >= 0 ? 'profit-positive' : 'profit-negative';
                        const statusClass = trade.status === 'open' ? 'status-open' : 'status-closed';

                        html += `
                            <tr>
                                <td style="font-family: monospace; font-size: 0.9em;">${trade.order_id}</td>
                                <td><strong>${trade.symbol}</strong></td>
                                <td>${trade.side.toUpperCase()}</td>
                                <td>$${trade.entry_price.toFixed(8)}</td>
                                <td>${trade.exit_price ? '$' + trade.exit_price.toFixed(8) : '-'}</td>
                                <td>${trade.amount}</td>
                                <td>${trade.leverage}x</td>
                                <td class="${profitClass}">$${trade.profit.toFixed(2)}</td>
                                <td class="${profitClass}">${trade.profit_percent.toFixed(2)}%</td>
                                <td><span class="${statusClass}">${trade.status.toUpperCase()}</span></td>
                                <td style="font-size: 0.9em;">${entryTime}</td>
                                <td>${duration}</td>
                                <td style="font-size: 0.9em;">${trade.signal_source || '-'}</td>
                            </tr>
                        `;
                    });

                    html += `
                            </tbody>
                        </table>
                        <div style="padding: 15px; text-align: center; color: #666; font-size: 0.9em;">
                            显示 ${result.data.length} 条交易记录
                        </div>
                    `;

                    container.innerHTML = html;
                } else {
                    container.innerHTML = `
                        <div class="loading">
                            <div style="font-size: 3em; margin-bottom: 15px;">📭</div>
                            <div style="font-size: 1.2em;">暂无交易记录</div>
                            <div style="margin-top: 10px; font-size: 0.9em;">系统开始交易后将显示记录</div>
                        </div>
                    `;
                }

            } catch (error) {
                console.error('加载交易历史失败:', error);
                document.getElementById('history-container').innerHTML = `
                    <div class="loading" style="color: #e74c3c;">
                        ❌ 加载失败: ${error.message}
                    </div>
                `;
            }
        }

        // 刷新数据
        async function refreshHistory() {
            await loadTradingStats();
            await loadTradingHistory();
        }

        // 页面加载时初始化
        loadTradingStats();
        loadTradingHistory();

        // 每30秒自动刷新
        setInterval(refreshHistory, 30000);
    </script>
</body>
</html>
        """

    async def start_server(self, host: str = "0.0.0.0", port: int = 8080):
        """启动Web服务器"""
        try:
            logger.info(f"启动Web管理界面: http://{host}:{port}")
            config = uvicorn.Config(
                app=self.app,
                host=host,
                port=port,
                log_level="info"
            )
            server = uvicorn.Server(config)
            await server.serve()
        except OSError as e:
            if e.errno == 10048:  # 端口被占用
                logger.error(f"❌ 端口 {port} 被占用！")
                logger.error(f"请检查是否有其他程序占用了端口 {port}")
                logger.error(f"解决方案：")
                logger.error(f"1. 运行 'python check_ports.py' 查看端口占用情况")
                logger.error(f"2. 或者修改 config.yaml 中的 web.port 配置")
                logger.error(f"3. 或者终止占用端口的程序")
                raise e
            else:
                raise e
        except Exception as e:
            logger.error(f"启动Web服务器异常: {e}")
            raise
            
    async def stop_server(self):
        """停止Web服务器"""
        try:
            # 关闭所有WebSocket连接
            for connection in self.active_connections.copy():
                await connection.close()
            self.active_connections.clear()
            logger.info("Web服务器已停止")
        except Exception as e:
            logger.error(f"停止Web服务器异常: {e}")

    def _get_strategies_html(self) -> str:
        """获取策略管理页面HTML"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略管理 - 交易机器人</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .nav { display: flex; gap: 20px; margin-top: 10px; }
        .nav a { color: white; text-decoration: none; padding: 5px 10px; border-radius: 4px; }
        .nav a:hover { background: rgba(255,255,255,0.2); }
        .nav a.active { background: #3498db; }

        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-weight: bold; }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn:hover { opacity: 0.8; }
        .exchange-upbit { border-left: 4px solid #ff6b6b; }
        .exchange-binance { border-left: 4px solid #f39c12; }
        .exchange-bithumb { border-left: 4px solid #3498db; }
        .exchange-coinbase { border-left: 4px solid #9b59b6; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 交易策略管理</h1>
            <div class="nav">
                <a href="/">🏠 主页</a>
                <a href="/strategies" class="active">🎯 策略管理</a>
                <a href="/analysis">📊 历史分析</a>
                <a href="/trading-history">📈 交易记录</a>
            </div>
        </div>

        <div class="strategy-grid" id="strategy-grid">
            <!-- 固定策略信息 -->
            <div class="strategy-card fixed-strategy">
                <div class="strategy-title">🎯 固定交易策略</div>
                <div class="strategy-content">
                    <div class="strategy-param">
                        <span class="param-label">固定金额:</span>
                        <span class="param-value">100 USDT</span>
                    </div>
                    <div class="strategy-param">
                        <span class="param-label">杠杆倍数:</span>
                        <span class="param-value">10x</span>
                    </div>
                    <div class="strategy-param">
                        <span class="param-label">仓位模式:</span>
                        <span class="param-value">逐仓</span>
                    </div>
                    <div class="strategy-param">
                        <span class="param-label">首次平仓:</span>
                        <span class="param-value">56秒后80%</span>
                    </div>
                    <div class="strategy-param">
                        <span class="param-label">剩余管理:</span>
                        <span class="param-value">智能平仓</span>
                    </div>
                </div>
                <div class="strategy-status" id="strategy-status">
                    <span class="status-label">状态:</span>
                    <span class="status-value" id="status-value">加载中...</span>
                </div>
                <div class="strategy-positions" id="strategy-positions">
                    <!-- 活跃仓位将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        async function loadFixedStrategy() {
            try {
                const response = await fetch('/api/fixed-strategy/status');
                const status = await response.json();

                const statusValue = document.getElementById('status-value');
                const positionsDiv = document.getElementById('strategy-positions');

                if (status.enabled) {
                    statusValue.textContent = `运行中 (${status.active_positions} 个活跃仓位)`;
                    statusValue.className = 'status-value status-active';

                    if (status.positions && status.positions.length > 0) {
                        positionsDiv.innerHTML = '<h4>活跃仓位:</h4>';
                        status.positions.forEach(pos => {
                            const posDiv = document.createElement('div');
                            posDiv.className = 'position-item';
                            posDiv.innerHTML = `
                                <span class="position-symbol">${pos.symbol}</span>
                                <span class="position-status">${pos.status}</span>
                                <span class="position-remaining">${pos.remaining_percentage}%</span>
                                <button onclick="manualClose('${pos.symbol}')" class="close-btn">手动平仓</button>
                            `;
                            positionsDiv.appendChild(posDiv);
                        });
                    } else {
                        positionsDiv.innerHTML = '<p>暂无活跃仓位</p>';
                    }
                } else {
                    statusValue.textContent = '未启用';
                    statusValue.className = 'status-value status-inactive';
                    positionsDiv.innerHTML = '';
                }
            } catch (error) {
                console.error('加载固定策略状态失败:', error);
                document.getElementById('status-value').textContent = '加载失败';
            }
        }

        async function manualClose(symbol) {
            try {
                const response = await fetch(`/api/fixed-strategy/manual-close/${symbol}`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    alert(`${symbol} 手动平仓成功`);
                    loadFixedStrategy(); // 重新加载状态
                } else {
                    alert(`${symbol} 手动平仓失败: ${result.message}`);
                }
            } catch (error) {
                console.error('手动平仓失败:', error);
                alert('手动平仓失败');
            }
        }

        // 页面加载时获取固定策略状态
        loadFixedStrategy();

        // 每30秒刷新一次
        setInterval(loadFixedStrategy, 30000);
    </script>
</body>
</html>
        """

    def _get_analysis_html(self) -> str:
        """获取历史分析页面HTML"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史分析 - 交易机器人</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .container { width: 100%; padding: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .nav { display: flex; gap: 20px; margin-top: 10px; }
        .nav a { color: white; text-decoration: none; padding: 5px 10px; border-radius: 4px; }
        .nav a:hover { background: rgba(255,255,255,0.2); }
        .nav a.active { background: #3498db; }
        .controls { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .tabs { display: flex; gap: 10px; margin-bottom: 20px; }
        .tab { padding: 10px 20px; background: white; border: none; border-radius: 4px; cursor: pointer; }
        .tab.active { background: #3498db; color: white; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .analysis-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; }
        .analysis-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .listing-table { width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; }
        .listing-table th, .listing-table td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        .listing-table th { background: #f8f9fa; font-weight: 600; }
        .listing-table tr:hover { background: #f8f9fa; }
        .exchange-badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 600; }
        .exchange-coinbase { background: #1652f0; color: white; }
        .exchange-binance { background: #f3ba2f; color: black; }
        .exchange-upbit { background: #0066ff; color: white; }
        .exchange-bithumb { background: #ff6b35; color: white; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 15px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #3498db; }
        .stat-label { color: #666; margin-top: 5px; }
        .analysis-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #2c3e50; }
        .metric { display: flex; justify-content: space-between; margin-bottom: 8px; }
        .metric-label { font-weight: 500; }
        .metric-value { color: #666; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-weight: bold; margin: 5px; }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn:hover { opacity: 0.8; }
        .loading { text-align: center; padding: 40px; color: #666; }
        .positive { color: #27ae60; }
        .negative { color: #e74c3c; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 历史数据分析</h1>
            <div class="nav">
                <a href="/">🏠 主页</a>
                <a href="/analysis" class="active">📊 历史分析</a>
                <a href="/trading-history">📈 交易记录</a>
            </div>
        </div>

        <!-- 交易所历史数据分析 -->
        <div id="analysis-tab" class="tab-content active">
            <!-- 交易所选择器 -->
            <div class="analysis-card">
                <h3>📊 交易所历史数据分析</h3>


                <div style="margin-bottom: 15px;">
                    <button onclick="refreshListingData()" style="background: #27ae60; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                        🔄 刷新数据
                    </button>
                    <button onclick="analyzePrices(event)" style="background: #e67e22; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                        🔄 重新分析合约价格
                    </button>
                    <button onclick="showAnalysisStats()" style="background: #9b59b6; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                        📊 查看分析统计
                    </button>
                    <span id="analysis-status" style="color: #666; font-size: 14px;">重新分析所有有USDT永续合约的代币价格表现（强制更新最新数据）</span>
                </div>

                <!-- 分析统计显示区域 -->
                <div id="analysis-stats" style="display: none; margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #9b59b6;">
                    <h4 style="margin: 0 0 10px 0; color: #9b59b6;">📊 价格分析统计</h4>
                    <div id="stats-content">加载中...</div>
                </div>
                <div class="exchange-buttons" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <button class="exchange-btn upbit" onclick="selectExchangeHistory('upbit')" style="padding: 15px; border: none; border-radius: 8px; cursor: pointer; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        🏆 <span>Upbit<br><small>39个代币 • 最激进策略</small></span>
                    </button>
                    <button class="exchange-btn binance" onclick="selectExchangeHistory('binance')" style="padding: 15px; border: none; border-radius: 8px; cursor: pointer; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                        🥈 <span>Binance<br><small>20个代币 • 积极策略</small></span>
                    </button>
                    <button class="exchange-btn bithumb" onclick="selectExchangeHistory('bithumb')" style="padding: 15px; border: none; border-radius: 8px; cursor: pointer; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                        🥉 <span>Bithumb<br><small>32个代币 • 保守策略</small></span>
                    </button>
                    <button class="exchange-btn coinbase" onclick="selectExchangeHistory('coinbase')" style="padding: 15px; border: none; border-radius: 8px; cursor: pointer; background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                        📊 <span>Coinbase<br><small>51个代币 • 最保守策略</small></span>
                    </button>
                </div>
            </div>

            <!-- 选中交易所的历史数据显示区域 -->
            <div id="exchange-history-data" style="display: none;">
                <div class="analysis-card">
                    <h3 id="exchange-history-title">交易所历史数据</h3>
                    <div id="exchange-history-table">
                        <!-- 历史数据表格将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function loadAnalysis() {
            try {
                const grid = document.getElementById('analysis-grid');
                grid.innerHTML = '<div class="loading">正在加载历史分析数据...</div>';

                const response = await fetch('/api/historical-analysis');
                const analysisData = await response.json();

                grid.innerHTML = '';

                const exchangeNames = {
                    'upbit': '🏆 Upbit',
                    'binance': '🥈 Binance',
                    'bithumb': '🥉 Bithumb',
                    'coinbase': '📊 Coinbase'
                };

                for (const [exchange, data] of Object.entries(analysisData)) {
                    if (!data || Object.keys(data).length === 0) continue;

                    const card = document.createElement('div');
                    card.className = 'analysis-card';

                    const change1h = data.avg_change_1h || 0;
                    const change4h = data.avg_change_4h || 0;
                    const change1d = data.avg_change_1d || 0;

                    card.innerHTML = `
                        <div class="analysis-title">${exchangeNames[exchange] || exchange}</div>
                        <div class="metric">
                            <span class="metric-label">上币数量:</span>
                            <span class="metric-value">${data.total_listings || 0}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">平均1小时涨幅:</span>
                            <span class="metric-value ${change1h >= 0 ? 'positive' : 'negative'}">${change1h.toFixed(2)}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">平均4小时涨幅:</span>
                            <span class="metric-value ${change4h >= 0 ? 'positive' : 'negative'}">${change4h.toFixed(2)}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">平均1天涨幅:</span>
                            <span class="metric-value ${change1d >= 0 ? 'positive' : 'negative'}">${change1d.toFixed(2)}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">最后更新:</span>
                            <span class="metric-value">${data.last_updated ? new Date(data.last_updated).toLocaleString() : '未知'}</span>
                        </div>
                    `;

                    grid.appendChild(card);
                }

                if (grid.children.length === 0) {
                    grid.innerHTML = '<div class="loading">暂无历史分析数据</div>';
                }

            } catch (error) {
                console.error('加载分析数据失败:', error);
                document.getElementById('analysis-grid').innerHTML = '<div class="loading">加载失败，请重试</div>';
            }
        }

        async function refreshAnalysis() {
            try {
                const grid = document.getElementById('analysis-grid');
                grid.innerHTML = '<div class="loading">正在重新分析历史数据，请稍候...</div>';

                const response = await fetch('/api/historical-analysis/refresh', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    setTimeout(loadAnalysis, 2000); // 2秒后刷新数据
                } else {
                    grid.innerHTML = '<div class="loading">分析失败，请重试</div>';
                }

            } catch (error) {
                console.error('刷新分析失败:', error);
                document.getElementById('analysis-grid').innerHTML = '<div class="loading">刷新失败，请重试</div>';
            }
        }

        // 页面加载时初始化
        console.log('交易所历史数据分析页面已加载');

        // 刷新上币数据
        async function refreshListingData() {
            const button = event.target;
            const originalText = button.innerHTML;

            try {
                // 显示加载状态
                button.innerHTML = '🔄 正在刷新...';
                button.disabled = true;
                button.style.background = '#95a5a6';

                // 调用刷新API
                const response = await fetch('/api/refresh-listing-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    // 刷新成功
                    const exchangeInfo = Object.entries(result.exchanges)
                        .map(([ex, count]) => `${ex.toUpperCase()}: ${count}`)
                        .join(', ');

                    alert(`✅ 数据强制刷新成功！\\n\\n📊 本次收集: ${result.total_listings}个代币\\n🏢 交易所分布: ${exchangeInfo}\\n💾 已强制覆盖: ${result.saved_to_db || result.total_listings}条到数据库\\n⏰ 数据已去重并强制更新，确保为最新数据`);

                    // 重置上币数据缓存，强制重新加载
                    listingData = null;

                    // 如果当前有显示的交易所数据，刷新它
                    const historyData = document.getElementById('exchange-history-data');
                    if (historyData.style.display !== 'none') {
                        // 找到当前选中的交易所并重新加载
                        const title = document.getElementById('exchange-history-title').textContent;
                        const exchange = title.split(' ')[0].toLowerCase();
                        if (exchange) {
                            await selectExchangeHistory(exchange);
                        }
                    }
                } else {
                    // 刷新失败
                    alert(`❌ 数据刷新失败！\\n\\n错误信息: ${result.message}`);
                }

            } catch (error) {
                console.error('刷新数据失败:', error);
                alert(`❌ 网络错误！\\n\\n错误信息: ${error.message}`);
            } finally {
                // 恢复按钮状态
                button.innerHTML = originalText;
                button.disabled = false;
                button.style.background = '#27ae60';
            }
        }

        // 全局变量存储上币数据
        let listingData = null;

        // 选择交易所查看历史数据
        async function selectExchangeHistory(exchange) {
            console.log('选择交易所历史数据:', exchange);

            // 如果还没有加载上币数据，先加载
            if (!listingData) {
                try {
                    const response = await fetch('/api/listing-data');
                    const data = await response.json();
                    if (data.success) {
                        listingData = data;
                    } else {
                        alert('加载数据失败: ' + data.message);
                        return;
                    }
                } catch (error) {
                    alert('网络错误: ' + error.message);
                    return;
                }
            }

            // 显示历史数据区域
            document.getElementById('exchange-history-data').style.display = 'block';

            // 更新标题
            document.getElementById('exchange-history-title').textContent =
                `${exchange.toUpperCase()} 最近3个月上币历史数据`;

            // 获取该交易所的代币数据
            const exchangeTokens = listingData.exchanges[exchange] || [];

            if (exchangeTokens.length === 0) {
                document.getElementById('exchange-history-table').innerHTML =
                    '<div style="text-align: center; padding: 40px; color: #666;">该交易所暂无数据</div>';
                return;
            }

            // 生成表格
            let tableHtml = `
                <table class="listing-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>代币</th>
                            <th>公告时间</th>
                            <th>市值</th>
                            <th>1分钟</th>
                            <th>5分钟</th>
                            <th>15分钟</th>
                            <th>1小时</th>
                            <th>4小时</th>
                            <th>1天</th>
                            <th>3天</th>
                            <th>达峰时长</th>
                            <th>最大涨幅</th>
                            <th>峰值回落</th>
                            <th>波动率</th>
                            <th>1小时涨幅</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            exchangeTokens.forEach((token, index) => {
                const marketcap = token.marketcap ? `$${token.marketcap}` : '-';
                const announcementTime = token.announcement_time ?
                    token.announcement_time.substring(0, 19) : '-';

                // 格式化价格分析数据
                const formatGain = (value) => {
                    if (value === null || value === undefined) return '-';
                    const num = parseFloat(value);
                    const color = num > 0 ? '#27ae60' : num < 0 ? '#e74c3c' : '#666';
                    return `<span style="color: ${color}">${num.toFixed(2)}%</span>`;
                };

                const formatTime = (minutes) => {
                    if (!minutes) return '-';
                    if (minutes < 60) return `${minutes}分`;
                    const hours = Math.floor(minutes / 60);
                    const mins = minutes % 60;
                    return mins > 0 ? `${hours}时${mins}分` : `${hours}时`;
                };

                tableHtml += `
                    <tr>
                        <td>${index + 1}</td>
                        <td><strong>${token.symbol}</strong></td>
                        <td>${announcementTime}</td>
                        <td>${marketcap}</td>
                        <td>${formatGain(token.gain_1m)}</td>
                        <td>${formatGain(token.gain_5m)}</td>
                        <td>${formatGain(token.gain_15m)}</td>
                        <td>${formatGain(token.gain_1h)}</td>
                        <td>${formatGain(token.gain_4h)}</td>
                        <td>${formatGain(token.gain_1d)}</td>
                        <td>${formatGain(token.gain_3d)}</td>
                        <td>${formatTime(token.time_to_peak)}</td>
                        <td>${formatGain(token.max_gain)}</td>
                        <td>${formatGain(token.peak_pullback)}</td>
                        <td>${token.volatility ? token.volatility.toFixed(2) + '%' : '-'}</td>
                    </tr>
                `;
            });

            tableHtml += `
                    </tbody>
                </table>

            `;

            document.getElementById('exchange-history-table').innerHTML = tableHtml;
        }

        // 分析价格表现
        async function analyzePrices(event) {
            // 阻止默认行为
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            const button = event ? event.target : document.querySelector('button[onclick="analyzePrices()"]');
            const originalText = button.innerHTML;
            const statusSpan = document.getElementById('analysis-status');

            try {
                // 更新按钮状态
                button.innerHTML = '🔄 重新分析中...';
                button.disabled = true;
                button.style.background = '#95a5a6';
                statusSpan.textContent = '正在重新分析所有代币的合约价格表现，这可能需要几分钟时间...';
                statusSpan.style.color = '#e67e22';

                // 发送分析请求
                console.log('开始发送价格分析请求...');
                const response = await fetch('/api/analyze-prices', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                console.log('收到响应:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('分析结果:', result);

                if (result.success) {
                    statusSpan.textContent = `✅ ${result.message}`;
                    statusSpan.style.color = '#27ae60';

                    // 添加刷新按钮
                    const refreshBtn = document.createElement('button');
                    refreshBtn.textContent = '🔄 刷新查看结果';
                    refreshBtn.style.cssText = 'margin-left: 10px; padding: 5px 10px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;';
                    refreshBtn.onclick = () => {
                        // 强制刷新，清除缓存
                        window.location.reload(true);
                    };

                    statusSpan.appendChild(document.createElement('br'));
                    statusSpan.appendChild(refreshBtn);
                } else {
                    statusSpan.textContent = `❌ ${result.message}`;
                    statusSpan.style.color = '#e74c3c';
                }

            } catch (error) {
                console.error('分析价格失败:', error);
                statusSpan.textContent = `❌ 网络错误: ${error.message}`;
                statusSpan.style.color = '#e74c3c';
            } finally {
                // 恢复按钮状态
                button.innerHTML = originalText;
                button.disabled = false;
                button.style.background = '#e67e22';
            }
        }

        // 显示分析统计
        async function showAnalysisStats() {
            const statsDiv = document.getElementById('analysis-stats');
            const contentDiv = document.getElementById('stats-content');

            try {
                // 显示统计区域
                statsDiv.style.display = 'block';
                contentDiv.innerHTML = '⏳ 加载统计数据...';

                // 获取统计数据
                const response = await fetch('/api/analysis-stats');
                const result = await response.json();

                if (result.success) {
                    const stats = result.stats;

                    let html = `
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px;">
                            <div style="text-align: center; padding: 10px; background: white; border-radius: 4px;">
                                <div style="font-size: 24px; font-weight: bold; color: #3498db;">${stats.total_tokens}</div>
                                <div style="color: #666;">总代币数</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: white; border-radius: 4px;">
                                <div style="font-size: 24px; font-weight: bold; color: #27ae60;">${stats.analyzed_tokens}</div>
                                <div style="color: #666;">已分析</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: white; border-radius: 4px;">
                                <div style="font-size: 24px; font-weight: bold; color: #e74c3c;">${stats.failed_tokens}</div>
                                <div style="color: #666;">分析失败</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: white; border-radius: 4px;">
                                <div style="font-size: 24px; font-weight: bold; color: #f39c12;">${stats.success_rate}%</div>
                                <div style="color: #666;">成功率</div>
                            </div>
                        </div>
                    `;

                    if (stats.top_performers && stats.top_performers.length > 0) {
                        html += `
                            <h5 style="margin: 15px 0 10px 0; color: #9b59b6;">🏆 表现最佳的代币</h5>
                            <div style="background: white; border-radius: 4px; overflow: hidden;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 8px; text-align: left; border-bottom: 1px solid #dee2e6;">代币</th>
                                            <th style="padding: 8px; text-align: left; border-bottom: 1px solid #dee2e6;">交易所</th>
                                            <th style="padding: 8px; text-align: right; border-bottom: 1px solid #dee2e6;">最大涨幅</th>
                                            <th style="padding: 8px; text-align: right; border-bottom: 1px solid #dee2e6;">达峰时长</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                        `;

                        stats.top_performers.forEach((token, index) => {
                            const gainColor = token.max_gain > 0 ? '#27ae60' : '#e74c3c';
                            const timeText = token.time_to_peak ? `${Math.floor(token.time_to_peak / 60)}时${token.time_to_peak % 60}分` : '-';

                            html += `
                                <tr style="border-bottom: 1px solid #f8f9fa;">
                                    <td style="padding: 8px; font-weight: bold;">${token.symbol}</td>
                                    <td style="padding: 8px;">${token.exchange.toUpperCase()}</td>
                                    <td style="padding: 8px; text-align: right; color: ${gainColor}; font-weight: bold;">+${token.max_gain}%</td>
                                    <td style="padding: 8px; text-align: right;">${timeText}</td>
                                </tr>
                            `;
                        });

                        html += `
                                    </tbody>
                                </table>
                            </div>
                        `;
                    }

                    html += `
                        <div style="margin-top: 15px; text-align: center;">
                            <button onclick="document.getElementById('analysis-stats').style.display='none'"
                                    style="background: #95a5a6; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                                关闭统计
                            </button>
                        </div>
                    `;

                    contentDiv.innerHTML = html;
                } else {
                    contentDiv.innerHTML = `❌ 获取统计失败: ${result.message}`;
                }

            } catch (error) {
                console.error('获取分析统计失败:', error);
                contentDiv.innerHTML = `❌ 网络错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
        """

    async def _get_listing_data(self) -> Dict[str, Any]:
        """从数据库获取上币数据"""
        try:
            # 导入数据库管理器
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(__file__)))
            from database import exchange_db

            # 获取价格分析数据
            performance_data = {}
            if price_analyzer:
                try:
                    performances = price_analyzer.get_performance_data()
                    for perf in performances:
                        key = f"{perf.symbol}_{perf.exchange}"
                        performance_data[key] = perf
                except Exception as e:
                    logger.error(f"获取价格分析数据失败: {e}")

            # 获取所有交易所的上币事件
            all_listings = []
            exchanges_data = {}

            for exchange in ['binance', 'coinbase', 'upbit', 'bithumb']:
                try:
                    # 获取该交易所的上币事件
                    events = exchange_db.get_listing_events(exchange)

                    exchange_listings = []
                    for event in events:
                        # 基础数据
                        listing_record = {
                            'symbol': event.symbol,
                            'exchange': event.exchange,
                            'announcement_time': event.announcement_time.isoformat() if event.announcement_time else '',
                            'marketcap': event.marketcap or '',
                            'source': event.source,
                            'processed': event.processed
                        }

                        # 合并价格分析数据
                        perf_key = f"{event.symbol}_{event.exchange}"
                        if perf_key in performance_data:
                            perf = performance_data[perf_key]
                            listing_record.update({
                                'gain_1m': perf.gain_1m,
                                'gain_5m': perf.gain_5m,
                                'gain_15m': perf.gain_15m,
                                'gain_1h': perf.gain_1h,
                                'gain_4h': perf.gain_4h,
                                'gain_1d': perf.gain_1d,
                                'gain_3d': perf.gain_3d,
                                'time_to_peak': perf.time_to_peak,
                                'max_gain': perf.max_gain,
                                'peak_pullback': perf.peak_pullback,
                                'volatility': perf.volatility,
                                'analyzed': perf.analyzed
                            })
                        else:
                            # 默认值
                            listing_record.update({
                                'gain_1m': None, 'gain_5m': None, 'gain_15m': None,
                                'gain_1h': None, 'gain_4h': None, 'gain_1d': None,
                                'gain_3d': None, 'time_to_peak': None, 'max_gain': None,
                                'peak_pullback': None, 'volatility': None, 'analyzed': False
                            })

                        exchange_listings.append(listing_record)
                        all_listings.append(listing_record)

                    # 按时间排序
                    exchange_listings.sort(key=lambda x: x['announcement_time'], reverse=True)
                    exchanges_data[exchange] = exchange_listings

                    logger.info(f"从数据库获取 {exchange} 上币数据: {len(exchange_listings)} 条")

                except Exception as e:
                    logger.warning(f"获取 {exchange} 数据失败: {e}")
                    exchanges_data[exchange] = []

            # 统计信息
            stats = {
                'total_listings': len(all_listings),
                'exchanges': {
                    exchange: len(tokens)
                    for exchange, tokens in exchanges_data.items()
                },
                'latest_update': datetime.now().isoformat(),
                'data_source': 'database'
            }

            logger.info(f"从数据库获取上币数据完成: 总计 {len(all_listings)} 条")

            return {
                "success": True,
                "stats": stats,
                "exchanges": exchanges_data,
                "data": all_listings
            }

        except Exception as e:
            logger.error(f"从数据库读取上币数据失败: {e}")
            # 如果数据库读取失败，回退到文件读取
            return await self._get_listing_data_from_file()

    async def _get_listing_data_from_file(self) -> Dict[str, Any]:
        """从文件获取上币数据（备用方案）"""
        try:
            # 查找最新的上币数据文件
            listing_file = self._find_latest_listing_file()

            if not listing_file:
                return {
                    "success": False,
                    "message": "未找到上币数据文件",
                    "data": []
                }

            # 读取数据
            with open(listing_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

                # 检查是否是数据库格式
                if isinstance(data, dict) and 'listings' in data:
                    listings = data['listings']  # 数据库格式
                else:
                    listings = data  # 简单格式

            # 按交易所分组
            exchanges = {}
            for listing in listings:
                exchange = listing['exchange']
                if exchange not in exchanges:
                    exchanges[exchange] = []
                exchanges[exchange].append(listing)

            # 按时间排序
            for exchange in exchanges:
                exchanges[exchange].sort(key=lambda x: x['announcement_time'], reverse=True)

            # 统计信息
            stats = {
                'total_listings': len(listings),
                'exchanges': {
                    exchange: len(tokens)
                    for exchange, tokens in exchanges.items()
                },
                'latest_update': datetime.now().isoformat(),
                'data_source': 'file'
            }

            return {
                "success": True,
                "stats": stats,
                "exchanges": exchanges,
                "data": listings
            }

        except Exception as e:
            logger.error(f"从文件读取上币数据失败: {e}")
            return {
                "success": False,
                "message": f"读取数据失败: {str(e)}",
                "data": []
            }

    def _find_latest_listing_file(self) -> str:
        """查找最新的上币数据文件"""
        try:
            # 在当前目录和上级目录查找
            search_paths = ['.', '..']

            for path in search_paths:
                if os.path.exists(path):
                    files = os.listdir(path)
                    listing_files = [
                        f for f in files
                        if (f.startswith('simple_listings_') or f.startswith('listings_database_')) and f.endswith('.json')
                    ]

                    if listing_files:
                        # 按文件名排序，取最新的
                        listing_files.sort(reverse=True)
                        latest_file = os.path.join(path, listing_files[0])
                        logger.info(f"找到上币数据文件: {latest_file}")
                        return latest_file

            logger.warning("未找到上币数据文件")
            return ""

        except Exception as e:
            logger.error(f"查找上币数据文件失败: {e}")
            return ""

    async def _refresh_listing_data(self) -> Dict[str, Any]:
        """刷新上币数据 - 通过监控系统自动获取"""
        try:
            logger.info("开始刷新上币数据...")

            # 创建上币数据收集器
            import sys
            import os
            
            # 确保设置正确的工作目录到项目根目录
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))  # 从src/web向上两级
            current_dir = os.getcwd()
            
            # 如果当前不在项目根目录，切换到项目根目录
            if not os.path.exists(os.path.join(current_dir, 'telegram_session.session')):
                if os.path.exists(os.path.join(project_root, 'telegram_session.session')):
                    logger.info(f"🔧 切换工作目录: {current_dir} -> {project_root}")
                    os.chdir(project_root)
                else:
                    logger.warning(f"⚠️ 在以下位置都未找到telegram会话文件:")
                    logger.warning(f"   当前目录: {current_dir}")
                    logger.warning(f"   项目目录: {project_root}")
            
            sys.path.append(os.path.dirname(os.path.dirname(__file__)))
            from monitors.listing_data_collector import ListingDataCollector

            collector = ListingDataCollector()

            # 初始化收集器
            if not await collector.initialize():
                return {
                    "success": False,
                    "message": "监控系统初始化失败",
                    "total_listings": 0,
                    "exchanges": {}
                }

            try:
                # 收集最近3个月的上币数据
                logger.info("正在收集最近3个月的上币数据...")
                listings_data = await collector.collect_listings(days=90)

                if not listings_data or len(listings_data) == 0:
                    return {
                        "success": False,
                        "message": "未收集到任何上币数据",
                        "total_listings": 0,
                        "exchanges": {}
                    }

                # 保存到数据库 - 使用强制覆盖模式确保数据最新
                saved_count = await self._save_listings_to_database(listings_data, force_overwrite=True)

                # 按交易所分组统计
                exchanges = {}
                for listing in listings_data:
                    exchange = listing.get('exchange', 'unknown')
                    if exchange not in exchanges:
                        exchanges[exchange] = 0
                    exchanges[exchange] += 1

                logger.info(f"成功刷新上币数据: {len(listings_data)}条记录，保存到数据库: {saved_count}条")

                return {
                    "success": True,
                    "message": "数据刷新成功",
                    "total_listings": len(listings_data),
                    "exchanges": exchanges,
                    "saved_to_db": saved_count
                }

            finally:
                # 清理资源
                await collector.cleanup()

        except ImportError:
            logger.error("ListingDataCollector模块未找到，使用备用方案")
            return await self._refresh_listing_data_fallback()
        except Exception as e:
            logger.error(f"刷新上币数据失败: {e}")
            return {
                "success": False,
                "message": f"刷新失败: {str(e)}",
                "total_listings": 0,
                "exchanges": {}
            }

    async def _refresh_listing_data_fallback(self) -> Dict[str, Any]:
        """备用方案：直接使用内置收集器"""
        try:
            logger.info("使用备用方案：直接调用内置收集器...")

            # 直接使用内置的收集逻辑
            import sys
            import os
            
            # 确保设置正确的工作目录到项目根目录
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))  # 从src/web向上两级
            current_dir = os.getcwd()
            
            # 如果当前不在项目根目录，切换到项目根目录
            if not os.path.exists(os.path.join(current_dir, 'telegram_session.session')):
                if os.path.exists(os.path.join(project_root, 'telegram_session.session')):
                    logger.info(f"🔧 备用方案切换工作目录: {current_dir} -> {project_root}")
                    os.chdir(project_root)
            
            sys.path.append(os.path.dirname(os.path.dirname(__file__)))
            from monitors.listing_data_collector import ListingDataCollector

            collector = ListingDataCollector()

            # 初始化收集器
            if not await collector.initialize():
                return {
                    "success": False,
                    "message": "内置收集器初始化失败",
                    "total_listings": 0,
                    "exchanges": {}
                }

            try:
                # 收集数据
                listings_data = await collector.collect_listings(days=90)

                if not listings_data or len(listings_data) == 0:
                    return {
                        "success": False,
                        "message": "未收集到任何上币数据",
                        "total_listings": 0,
                        "exchanges": {}
                    }

                # 保存数据
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"simple_listings_{timestamp}.json"

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(listings_data, f, indent=2, ensure_ascii=False)

                logger.info(f"数据已保存到: {filename}")

                # 统计信息
                exchanges = {}
                for listing in listings_data:
                    exchange = listing.get('exchange', 'unknown')
                    if exchange not in exchanges:
                        exchanges[exchange] = 0
                    exchanges[exchange] += 1

                return {
                    "success": True,
                    "message": "通过内置收集器刷新成功",
                    "total_listings": len(listings_data),
                    "exchanges": exchanges
                }

            finally:
                await collector.cleanup()

        except ImportError as e:
            logger.error(f"导入收集器失败: {e}")
            return {
                "success": False,
                "message": f"导入收集器失败: {str(e)}",
                "total_listings": 0,
                "exchanges": {}
            }
        except Exception as e:
            logger.error(f"备用方案执行失败: {e}")
            return {
                "success": False,
                "message": f"备用方案失败: {str(e)}",
                "total_listings": 0,
                "exchanges": {}
            }

    async def _save_listings_to_database(self, listings_data: list, force_overwrite: bool = False) -> int:
        """保存上币数据到数据库
        
        Args:
            listings_data: 上币数据列表
            force_overwrite: 是否强制覆盖已存在的记录（刷新时使用）
        """
        try:
            # 导入数据库管理器
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(__file__)))
            from database import exchange_db, ListingEvent

            # 如果是强制覆盖模式，先清空整个表
            if force_overwrite:
                logger.info("🗑️ 强制覆盖模式：清空现有的上币数据...")
                import sqlite3
                db_path = exchange_db.db_path
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute('DELETE FROM listing_events')
                conn.commit()
                conn.close()
                logger.info("✅ 已清空所有现有上币数据")

            saved_count = 0

            for listing in listings_data:
                try:
                    # 创建上币事件对象
                    event = ListingEvent(
                        exchange=listing['exchange'],
                        symbol=listing['symbol'],
                        announcement_time=datetime.fromisoformat(listing['announcement_time'].replace(' ', 'T')),
                        announcement_content=listing.get('message_text', ''),
                        marketcap=listing.get('marketcap', ''),
                        source='telegram_monitor'
                    )

                    # 保存到数据库 - 现在不需要force_overwrite因为已经清空了表
                    exchange_db.save_listing_event(event, force_overwrite=False)
                    saved_count += 1

                except Exception as e:
                    logger.warning(f"保存上币事件失败 {listing['exchange']}-{listing['symbol']}: {e}")
                    continue

            logger.info(f"成功保存 {saved_count}/{len(listings_data)} 条上币数据到数据库")

            # 同时保存一份到文件作为备份
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"listings_backup_{timestamp}.json"

            backup_data = {
                'metadata': {
                    'created_at': datetime.now().isoformat(),
                    'total_records': len(listings_data),
                    'collection_method': 'monitoring_system',
                    'data_period_days': 90,
                    'saved_to_db': saved_count
                },
                'listings': listings_data
            }

            with open(backup_filename, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)

            logger.info(f"备份文件已保存: {backup_filename}")

            return saved_count

        except Exception as e:
            logger.error(f"保存到数据库失败: {e}")
            # 如果数据库保存失败，至少保存到文件
            try:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                fallback_filename = f"listings_fallback_{timestamp}.json"

                with open(fallback_filename, 'w', encoding='utf-8') as f:
                    json.dump(listings_data, f, indent=2, ensure_ascii=False)

                logger.info(f"数据库保存失败，已保存到备用文件: {fallback_filename}")
                return len(listings_data)

            except Exception as e2:
                logger.error(f"备用文件保存也失败: {e2}")
                return 0

if __name__ == "__main__":
    """直接运行web服务器"""
    import uvicorn

    print("🚀 启动交易机器人Web管理界面...")
    print("📊 访问 http://localhost:8000 查看界面")

    # 创建web服务器实例
    web_server = WebServer()

    # 启动服务器
    uvicorn.run(web_server.app, host="0.0.0.0", port=8000)

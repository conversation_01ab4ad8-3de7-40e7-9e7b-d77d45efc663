#!/usr/bin/env python3
"""
Binance历史数据获取器 (简化版)
专门从Binance API获取精确的历史行情数据
只使用Binance，删除其他数据源
"""
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from loguru import logger
import json


class BinanceHistoricalFetcher:
    """Binance历史数据获取器"""
    
    def __init__(self):
        self.base_url = "https://api.binance.com"
        self.request_delay = 0.1  # 请求间隔，避免限制
        
    async def get_symbol_info(self, symbol: str) -> Optional[str]:
        """获取交易对信息，确定正确的交易对名称"""
        try:
            url = f"{self.base_url}/api/v3/exchangeInfo"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # 查找匹配的交易对
                        possible_pairs = [
                            f"{symbol.upper()}USDT",
                            f"{symbol.upper()}BUSD", 
                            f"{symbol.upper()}BTC",
                            f"{symbol.upper()}ETH"
                        ]
                        
                        for symbol_info in data['symbols']:
                            if symbol_info['symbol'] in possible_pairs and symbol_info['status'] == 'TRADING':
                                logger.info(f"✅ 找到交易对: {symbol_info['symbol']}")
                                return symbol_info['symbol']
                                
                        logger.warning(f"⚠️ 未找到 {symbol} 的有效交易对")
                        return None
                    else:
                        logger.error(f"❌ 获取交易对信息失败: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"❌ 获取交易对信息异常: {e}")
            return None
            
    async def get_historical_klines(self, trading_pair: str, start_time: datetime, days: int = 3) -> Optional[List[Dict]]:
        """
        获取历史K线数据
        
        Args:
            trading_pair: 交易对名称 (如: BTCUSDT)
            start_time: 开始时间 (公告时间)
            days: 获取天数 (默认3天)
        """
        try:
            logger.info(f"📊 获取 {trading_pair} 历史K线数据...")
            
            # 计算时间范围
            start_timestamp = int(start_time.timestamp() * 1000)
            end_timestamp = int((start_time + timedelta(days=days)).timestamp() * 1000)
            
            url = f"{self.base_url}/api/v3/klines"
            
            all_klines = []
            current_start = start_timestamp
            
            # 分批获取数据 (Binance限制每次最多1500条)
            while current_start < end_timestamp:
                params = {
                    'symbol': trading_pair,
                    'interval': '1m',  # 1分钟K线
                    'startTime': current_start,
                    'endTime': min(current_start + (1500 * 60 * 1000), end_timestamp),  # 最多1500分钟
                    'limit': 1500
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            klines = await response.json()
                            
                            if not klines:
                                break
                                
                            # 转换为标准格式
                            for kline in klines:
                                kline_data = {
                                    'timestamp': datetime.fromtimestamp(kline[0] / 1000),
                                    'open': float(kline[1]),
                                    'high': float(kline[2]),
                                    'low': float(kline[3]),
                                    'close': float(kline[4]),
                                    'volume': float(kline[5]),
                                    'close_time': datetime.fromtimestamp(kline[6] / 1000),
                                    'quote_volume': float(kline[7]),
                                    'trades_count': int(kline[8])
                                }
                                all_klines.append(kline_data)
                                
                            # 更新下次请求的开始时间
                            current_start = klines[-1][6] + 1  # 下一分钟
                            
                            logger.info(f"📈 已获取 {len(all_klines)} 条K线数据...")
                            
                            # 避免请求限制
                            await asyncio.sleep(self.request_delay)
                            
                        elif response.status == 429:
                            logger.warning("⚠️ Binance API请求限制，等待重试...")
                            await asyncio.sleep(1)
                            continue
                            
                        else:
                            logger.error(f"❌ Binance API错误: {response.status}")
                            break
                            
            logger.info(f"✅ {trading_pair}: 总共获取 {len(all_klines)} 条历史数据")
            return all_klines if all_klines else None
            
        except Exception as e:
            logger.error(f"❌ 获取历史K线数据异常: {e}")
            return None
            
    def analyze_price_changes_from_announcement(self, klines: List[Dict], announcement_time: datetime) -> Dict[str, Any]:
        """基于公告时间分析价格变化"""
        if not klines:
            return {}
            
        try:
            # 找到公告时间最接近的K线数据
            announcement_kline = None
            min_time_diff = float('inf')
            
            for kline in klines:
                time_diff = abs((kline['timestamp'] - announcement_time).total_seconds())
                if time_diff < min_time_diff:
                    min_time_diff = time_diff
                    announcement_kline = kline
                    
            if not announcement_kline:
                logger.error("❌ 未找到公告时间对应的K线数据")
                return {}
                
            announcement_price = announcement_kline['close']
            logger.info(f"💰 公告时价格: ${announcement_price:.8f}")
            
            # 计算各时间点的价格变化
            time_points = [
                (1, '1min'), (5, '5min'), (15, '15min'), (30, '30min'),
                (60, '1hour'), (240, '4hour'), (720, '12hour'), 
                (1440, '1day'), (4320, '3day')
            ]
            
            changes = {}
            max_gain = 0
            max_loss = 0
            peak_time_minutes = 0
            peak_price = announcement_price
            max_drawdown_from_peak = 0
            
            # 记录价格时间线
            price_timeline = []
            
            for minutes, label in time_points:
                target_time = announcement_time + timedelta(minutes=minutes)
                
                # 找到最接近目标时间的K线
                closest_kline = None
                min_diff = float('inf')
                
                for kline in klines:
                    time_diff = abs((kline['timestamp'] - target_time).total_seconds())
                    if time_diff < min_diff:
                        min_diff = time_diff
                        closest_kline = kline
                        
                if closest_kline:
                    current_price = closest_kline['close']
                    change_percent = ((current_price - announcement_price) / announcement_price) * 100
                    changes[f'change_{label}'] = round(change_percent, 2)
                    
                    # 更新最大涨跌幅和达峰时间
                    if change_percent > max_gain:
                        max_gain = change_percent
                        peak_time_minutes = minutes
                        peak_price = current_price
                        
                    if change_percent < max_loss:
                        max_loss = change_percent
                        
                    # 记录时间线
                    price_timeline.append({
                        'time': label,
                        'timestamp': closest_kline['timestamp'].isoformat(),
                        'price': current_price,
                        'change': change_percent,
                        'volume': closest_kline['volume'],
                        'high': closest_kline['high'],
                        'low': closest_kline['low']
                    })
                    
                    logger.info(f"📈 {label}: ${current_price:.8f} ({change_percent:+.2f}%)")
                    
            # 计算从峰值的最大回撤
            peak_reached = False
            for point in price_timeline:
                if point['change'] == max_gain:
                    peak_reached = True
                elif peak_reached:
                    drawdown = point['change'] - max_gain
                    if drawdown < max_drawdown_from_peak:
                        max_drawdown_from_peak = drawdown
                        
            # 计算3天波动率
            all_changes = [v for v in changes.values() if v is not None]
            volatility_3day = max(all_changes) - min(all_changes) if all_changes else 0
            
            analysis_result = {
                'announcement_time': announcement_time.isoformat(),
                'announcement_price': announcement_price,
                'changes': changes,
                'max_gain': round(max_gain, 2),
                'max_loss': round(max_loss, 2),
                'peak_time_hours': round(peak_time_minutes / 60, 2),
                'peak_price': peak_price,
                'max_drawdown_from_peak': round(max_drawdown_from_peak, 2),
                'volatility_3day': round(volatility_3day, 2),
                'price_timeline': price_timeline
            }
            
            logger.info(f"🎯 分析完成:")
            logger.info(f"  最大涨幅: {max_gain:.2f}%")
            logger.info(f"  达峰时间: {peak_time_minutes/60:.2f}小时")
            logger.info(f"  最大回撤: {max_drawdown_from_peak:.2f}%")
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"❌ 分析价格变化异常: {e}")
            return {}
            
    async def get_announcement_analysis(self, symbol: str, announcement_time: datetime) -> Optional[Dict]:
        """获取基于公告时间的完整分析"""
        logger.info(f"🔍 开始分析 {symbol} 的公告后价格变化...")
        logger.info(f"📅 公告时间: {announcement_time}")
        
        try:
            # 1. 获取交易对信息
            trading_pair = await self.get_symbol_info(symbol)
            if not trading_pair:
                logger.error(f"❌ 无法找到 {symbol} 的交易对")
                return None
                
            # 2. 获取历史K线数据
            klines = await self.get_historical_klines(trading_pair, announcement_time, days=3)
            if not klines:
                logger.error(f"❌ 无法获取 {symbol} 的历史数据")
                return None
                
            # 3. 分析价格变化
            analysis = self.analyze_price_changes_from_announcement(klines, announcement_time)
            if not analysis:
                logger.error(f"❌ 无法分析 {symbol} 的价格变化")
                return None
                
            # 4. 返回完整结果
            result = {
                'symbol': symbol,
                'trading_pair': trading_pair,
                'announcement_time': announcement_time.isoformat(),
                'data_source': 'binance',
                'analysis': analysis,
                'raw_klines_count': len(klines)
            }
            
            logger.info(f"✅ {symbol} 分析完成!")
            return result
            
        except Exception as e:
            logger.error(f"❌ 获取公告分析异常: {e}")
            return None


async def main():
    """测试Binance历史数据获取"""
    logger.info("🧪 测试Binance历史数据获取器")
    logger.info("=" * 60)
    
    fetcher = BinanceHistoricalFetcher()
    
    # 测试案例 - 使用一些知名代币的历史时间点
    test_cases = [
        {
            'symbol': 'BTC',
            'announcement_time': datetime.now() - timedelta(days=7),  # 7天前
            'description': 'BTC 7天前的数据'
        },
        {
            'symbol': 'ETH', 
            'announcement_time': datetime.now() - timedelta(days=3),  # 3天前
            'description': 'ETH 3天前的数据'
        }
    ]
    
    for test_case in test_cases:
        logger.info(f"\n🔍 测试: {test_case['description']}")
        logger.info(f"代币: {test_case['symbol']}")
        logger.info(f"时间: {test_case['announcement_time']}")
        
        result = await fetcher.get_announcement_analysis(
            test_case['symbol'], 
            test_case['announcement_time']
        )
        
        if result:
            analysis = result['analysis']
            logger.info(f"✅ 分析成功:")
            logger.info(f"  交易对: {result['trading_pair']}")
            logger.info(f"  公告价格: ${analysis['announcement_price']:.8f}")
            logger.info(f"  1小时涨幅: {analysis['changes'].get('change_1hour', 0)}%")
            logger.info(f"  最大涨幅: {analysis['max_gain']}%")
            logger.info(f"  达峰时间: {analysis['peak_time_hours']}小时")
            logger.info(f"  数据点数: {result['raw_klines_count']}")
        else:
            logger.warning(f"⚠️ 分析失败")
            
    logger.info("\n🎉 测试完成!")
    logger.info("💡 现在可以用这个系统分析任何代币在任何时间点的价格变化!")


if __name__ == "__main__":
    asyncio.run(main())

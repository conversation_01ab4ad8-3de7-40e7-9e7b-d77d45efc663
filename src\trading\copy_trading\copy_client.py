"""
币安跟单API客户端
"""
import hashlib
import hmac
import time
import aiohttp
import json
from typing import Dict, Any, Optional
from loguru import logger
from urllib.parse import urlencode


class BinanceCopyTradingClient:
    """币安跟单API客户端"""
    
    def __init__(self, api_key: str, api_secret: str, testnet: bool = False):
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet
        
        # API端点
        if testnet:
            self.base_url = "https://testnet.binancefuture.com"
        else:
            self.base_url = "https://fapi.binance.com"
        
        # 跟单API端点 (假设的端点，需要根据实际API文档调整)
        self.copy_trading_base = "https://api.binance.com/sapi/v1/copyTrading"
        
        logger.info(f"币安跟单客户端初始化完成 - 测试网: {testnet}")
    
    def _generate_signature(self, params: Dict[str, Any]) -> str:
        """生成API签名"""
        query_string = urlencode(params)
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/json'
        }
    
    async def _request(self, method: str, endpoint: str, params: Dict[str, Any] = None, 
                      signed: bool = True) -> Optional[Dict[str, Any]]:
        """发送API请求"""
        try:
            if params is None:
                params = {}
            
            # 添加时间戳
            if signed:
                params['timestamp'] = int(time.time() * 1000)
                params['signature'] = self._generate_signature(params)
            
            url = f"{self.base_url}{endpoint}"
            headers = self._get_headers()
            
            async with aiohttp.ClientSession() as session:
                if method.upper() == 'GET':
                    async with session.get(url, params=params, headers=headers) as response:
                        if response.status == 200:
                            return await response.json()
                        else:
                            error_text = await response.text()
                            logger.error(f"API请求失败: {response.status} - {error_text}")
                            return None
                
                elif method.upper() == 'POST':
                    async with session.post(url, json=params, headers=headers) as response:
                        if response.status == 200:
                            return await response.json()
                        else:
                            error_text = await response.text()
                            logger.error(f"API请求失败: {response.status} - {error_text}")
                            return None
                            
        except Exception as e:
            logger.error(f"API请求异常: {e}")
            return None
    
    async def get_symbol_ticker(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取交易对价格"""
        try:
            endpoint = "/fapi/v1/ticker/price"
            params = {'symbol': symbol}
            return await self._request('GET', endpoint, params, signed=False)
        except Exception as e:
            logger.error(f"获取价格异常: {e}")
            return None
    
    async def get_position_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取仓位信息"""
        try:
            endpoint = "/fapi/v2/positionRisk"
            params = {'symbol': symbol}
            result = await self._request('GET', endpoint, params)
            
            if result and isinstance(result, list) and len(result) > 0:
                return result[0]
            return None
            
        except Exception as e:
            logger.error(f"获取仓位信息异常: {e}")
            return None
    
    async def get_account_balance(self) -> Dict[str, Any]:
        """获取账户余额"""
        try:
            endpoint = "/fapi/v2/balance"
            result = await self._request('GET', endpoint)
            return result if result else {}
        except Exception as e:
            logger.error(f"获取账户余额异常: {e}")
            return {}
    
    async def change_leverage(self, symbol: str, leverage: int) -> bool:
        """设置杠杆"""
        try:
            endpoint = "/fapi/v1/leverage"
            params = {
                'symbol': symbol,
                'leverage': leverage
            }
            result = await self._request('POST', endpoint, params)
            return result is not None
        except Exception as e:
            logger.error(f"设置杠杆异常: {e}")
            return False
    
    async def change_margin_type(self, symbol: str, margin_type: str) -> bool:
        """设置保证金模式"""
        try:
            endpoint = "/fapi/v1/marginType"
            params = {
                'symbol': symbol,
                'marginType': margin_type
            }
            result = await self._request('POST', endpoint, params)
            return result is not None
        except Exception as e:
            logger.debug(f"设置保证金模式异常 (可能已设置): {e}")
            return False
    
    async def create_copy_trading_order(self, symbol: str, side: str, type: str, 
                                      quantity: float, copy_trading: bool = True,
                                      leader_config: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        创建跟单交易订单
        
        注意: 这是假设的API端点，实际实现需要根据币安官方跟单API文档调整
        """
        try:
            # 如果跟单功能启用，使用跟单API端点
            if copy_trading:
                endpoint = "/sapi/v1/copyTrading/futures/order"
                base_url = "https://api.binance.com"
            else:
                endpoint = "/fapi/v1/order"
                base_url = self.base_url
            
            params = {
                'symbol': symbol,
                'side': side,
                'type': type,
                'quantity': quantity,
                'timeInForce': 'IOC'  # 立即成交或取消
            }
            
            # 添加跟单配置
            if copy_trading and leader_config:
                params.update({
                    'copyTrading': True,
                    'leaderConfig': leader_config
                })
            
            # 临时使用普通期货API (实际应该使用跟单API)
            url = f"{base_url}{endpoint}"
            headers = self._get_headers()
            
            # 添加签名
            params['timestamp'] = int(time.time() * 1000)
            params['signature'] = self._generate_signature(params)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=params, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"跟单订单创建成功: {result.get('orderId')}")
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"跟单订单创建失败: {response.status} - {error_text}")
                        return None
                        
        except Exception as e:
            logger.error(f"创建跟单订单异常: {e}")
            return None
    
    async def get_copy_trading_status(self) -> Dict[str, Any]:
        """获取跟单状态"""
        try:
            # 假设的API端点
            endpoint = "/sapi/v1/copyTrading/futures/leadStatus"
            result = await self._request('GET', endpoint)
            return result if result else {}
        except Exception as e:
            logger.error(f"获取跟单状态异常: {e}")
            return {}
    
    async def get_followers_list(self) -> list:
        """获取跟单者列表"""
        try:
            # 假设的API端点
            endpoint = "/sapi/v1/copyTrading/futures/leadPosition"
            result = await self._request('GET', endpoint)
            return result if result else []
        except Exception as e:
            logger.error(f"获取跟单者列表异常: {e}")
            return []
    
    async def set_leader_config(self, config: Dict[str, Any]) -> bool:
        """设置带单者配置"""
        try:
            # 假设的API端点
            endpoint = "/sapi/v1/copyTrading/futures/leadConfig"
            result = await self._request('POST', endpoint, config)
            return result is not None
        except Exception as e:
            logger.error(f"设置带单者配置异常: {e}")
            return False

"""
交易器接口定义
"""
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from datetime import datetime
from ...models import TradingSignal


class TraderInterface(ABC):
    """交易器接口 - 定义统一的交易接口"""
    
    @abstractmethod
    async def execute_trade(self, signal: TradingSignal, **kwargs) -> Dict[str, Any]:
        """
        执行交易
        
        Args:
            signal: 交易信号
            **kwargs: 额外参数
            
        Returns:
            Dict: 交易结果
            {
                'success': bool,
                'order_id': str,
                'price': float,
                'quantity': float,
                'mode': str,  # 'normal' or 'copy_trading'
                'message': str
            }
        """
        pass
    
    @abstractmethod
    async def close_position(self, symbol: str, percentage: float = 100.0) -> Dict[str, Any]:
        """
        平仓
        
        Args:
            symbol: 交易对符号
            percentage: 平仓百分比
            
        Returns:
            Dict: 平仓结果
        """
        pass
    
    @abstractmethod
    async def get_position_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        获取仓位信息
        
        Args:
            symbol: 交易对符号
            
        Returns:
            Dict: 仓位信息
        """
        pass
    
    @abstractmethod
    async def get_account_balance(self) -> Dict[str, Any]:
        """
        获取账户余额
        
        Returns:
            Dict: 账户余额信息
        """
        pass
    
    @abstractmethod
    def get_trader_type(self) -> str:
        """
        获取交易器类型
        
        Returns:
            str: 'normal' or 'copy_trading'
        """
        pass


class TradingResult:
    """交易结果数据类"""
    
    def __init__(self, success: bool, order_id: str = None, price: float = 0.0, 
                 quantity: float = 0.0, mode: str = 'normal', message: str = ''):
        self.success = success
        self.order_id = order_id
        self.price = price
        self.quantity = quantity
        self.mode = mode
        self.message = message
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'success': self.success,
            'order_id': self.order_id,
            'price': self.price,
            'quantity': self.quantity,
            'mode': self.mode,
            'message': self.message,
            'timestamp': self.timestamp.isoformat()
        }

# 📋 配置管理指南

## 🎯 概述

本系统支持**配置热加载**，您可以在运行时修改配置而无需重启程序。配置管理采用多层架构：

- **主配置文件**: `config.yaml` - 主要配置
- **环境变量**: `.env` - 敏感信息和环境特定配置  
- **示例配置**: `configs/config_with_feishu.yaml` - 配置模板

## 🔄 热加载功能

### ✅ 支持热加载的配置
- 交易参数（金额、杠杆等）
- 通知设置（飞书、邮件等）
- 监控关键词
- Web界面设置
- 日志级别

### ⚠️ 需要重启的配置
- API密钥（Binance、Telegram）
- 数据库连接
- 核心系统架构

## 📁 配置文件结构

```
├── config.yaml              # 主配置文件
├── .env                     # 环境变量（敏感信息）
├── .env.example            # 环境变量示例
├── .env.template           # 环境变量模板
└── configs/
    └── config_with_feishu.yaml  # 完整配置示例
```

## 🛠️ 配置管理工具

### 同步配置文件
```bash
# 检查配置一致性
python scripts/sync_configs.py --check

# 同步所有配置文件
python scripts/sync_configs.py --sync

# 自动检查并同步
python scripts/sync_configs.py
```

### 配置验证
系统会自动验证配置的合法性：
- 数值范围检查
- 必填字段验证
- 格式正确性验证

## 📝 配置修改方法

### 方法1: 直接修改配置文件
```bash
# 编辑主配置
nano config.yaml

# 系统会自动检测变化并应用
```

### 方法2: 通过Web界面
访问 `http://localhost:8080` 在线修改配置

### 方法3: 环境变量
```bash
# 修改 .env 文件
echo "TRADING_AMOUNT=200" >> .env

# 重启程序应用环境变量变化
```

## 🔧 常用配置项

### 交易配置
```yaml
fixed_strategy:
  amount: 100          # 交易金额
  leverage: 10         # 杠杆倍数
  first_close_time: 56 # 首次平仓时间(秒)
  first_close_percentage: 80 # 首次平仓比例
```

### 监控配置
```yaml
monitoring:
  telegram:
    api_id: "your_api_id"
    api_hash: "your_api_hash"
    phone: "your_phone"
    channels:
      - "@BWEnews"
    keywords:
      - "Binance Will List"
```

### 通知配置
```yaml
notifications:
  feishu:
    enabled: true
    webhook_url: "your_webhook_url"
```

## 🔐 敏感信息管理

### 环境变量 (.env)
```bash
# Binance API
BINANCE_API_KEY=your_api_key
BINANCE_API_SECRET=your_api_secret
BINANCE_TESTNET=false

# Telegram
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash
TELEGRAM_PHONE=your_phone

# 飞书
FEISHU_WEBHOOK_URL=your_webhook_url
```

### 安全建议
- ✅ 将敏感信息放在 `.env` 文件中
- ✅ 不要将 `.env` 文件提交到版本控制
- ✅ 使用 `.env.example` 作为模板
- ✅ 定期轮换API密钥

## 📊 配置优先级

配置加载优先级（高到低）：
1. **环境变量** - 最高优先级
2. **config.yaml** - 主配置文件
3. **默认值** - 系统默认值

## 🔄 配置变化监控

系统会自动监控配置文件变化：
```
2025-08-02 22:00:00 | INFO | 检测到配置文件变化: config.yaml
2025-08-02 22:00:01 | INFO | 配置更新: trading.amount = 100 → 200
2025-08-02 22:00:01 | INFO | ✅ 配置变化应用完成
```

## 🚨 故障排除

### 配置不生效
1. 检查配置语法是否正确
2. 查看日志中的配置验证信息
3. 确认配置项是否支持热加载

### 配置文件不一致
```bash
# 运行同步工具
python scripts/sync_configs.py --sync
```

### 配置验证失败
```bash
# 检查配置文件语法
python -c "import yaml; yaml.safe_load(open('config.yaml'))"
```

## 📋 配置检查清单

### 首次部署
- [ ] 复制 `.env.example` 为 `.env`
- [ ] 填写必要的API密钥
- [ ] 运行配置同步工具
- [ ] 验证配置一致性

### 日常维护
- [ ] 定期检查配置一致性
- [ ] 备份重要配置文件
- [ ] 监控配置变化日志
- [ ] 测试热加载功能

## 🔗 相关文档

- [系统控制指南](./guides/README_SYSTEM_CONTROL.md)
- [部署指南](./QUICK_DEPLOY_GUIDE.md)
- [故障排除](./README.md)

## 💡 最佳实践

1. **配置分离**: 敏感信息使用环境变量，业务配置使用YAML
2. **版本控制**: 配置文件变化要有记录和备份
3. **测试验证**: 配置修改后要测试功能是否正常
4. **文档更新**: 配置变化要更新相关文档
5. **权限控制**: 限制配置文件的访问权限

---

🎯 **记住**: 配置热加载让您可以快速调整策略参数，无需重启系统，提高运维效率！

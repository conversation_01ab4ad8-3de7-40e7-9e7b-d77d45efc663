"""
统一数据库管理器
管理所有交易所的历史数据存储和查询
支持: Binance, Coinbase, Upbit, Bithumb
统一存储，通过exchange字段区分交易所
"""
import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from loguru import logger
from dataclasses import dataclass, asdict


@dataclass
class ExchangeData:
    """交易所数据结构"""
    exchange: str
    symbol: str
    timestamp: datetime
    price: float
    volume: float
    market_cap: Optional[float] = None
    change_24h: Optional[float] = None
    listing_type: Optional[str] = None  # 上币类型
    announcement_time: Optional[datetime] = None  # 公告时间


@dataclass
class ListingEvent:
    """上币事件数据结构"""
    exchange: str
    symbol: str
    announcement_time: datetime
    announcement_content: Optional[str] = None
    marketcap: Optional[str] = None
    source: Optional[str] = None
    processed: bool = False


@dataclass
class TradingSignalRecord:
    """交易信号记录数据结构"""
    source: str
    symbol: str
    timestamp: datetime
    content: str
    exchange: str
    confidence: float = 1.0
    priority: int = 1
    metadata: Optional[Dict] = None
    processed: bool = False
    id: Optional[int] = None


@dataclass
class TradingOrderRecord:
    """交易订单记录数据结构"""
    order_id: str
    symbol: str
    side: str
    amount: float
    leverage: int
    stop_loss_percent: Optional[float]
    status: str
    filled_price: Optional[float]
    created_at: datetime
    filled_at: Optional[datetime] = None
    error_message: Optional[str] = None
    signal_id: Optional[int] = None
    id: Optional[int] = None


@dataclass
class TradingHistoryRecord:
    """交易历史记录数据结构"""
    order_id: str
    symbol: str
    side: str
    entry_price: float
    exit_price: Optional[float]
    amount: float
    leverage: int
    profit: float = 0.0
    profit_percent: float = 0.0
    status: str = 'open'
    entry_time: datetime = None
    exit_time: Optional[datetime] = None
    duration_minutes: Optional[int] = None
    exchange_source: str = 'unknown'
    signal_source: str = 'unknown'
    close_reason: Optional[str] = None
    id: Optional[int] = None


class UnifiedDatabaseManager:
    """统一数据库管理器 - 管理所有交易所数据"""

    def __init__(self, db_path: str = "trading_data.db"):
        self.db_path = db_path
        self.init_database()
        self.migrate_database()

    def init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 创建交易所数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS exchange_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    exchange TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    price REAL NOT NULL,
                    volume REAL NOT NULL,
                    market_cap REAL,
                    change_24h REAL,
                    listing_type TEXT,
                    announcement_time TEXT,
                    raw_data TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,

                    UNIQUE(exchange, symbol, timestamp)
                )
            ''')

            # 创建上币事件表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS listing_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    exchange TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    announcement_time TEXT NOT NULL,
                    listing_time TEXT,
                    announcement_content TEXT,
                    marketcap TEXT,
                    source TEXT,
                    processed BOOLEAN DEFAULT FALSE,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,

                    UNIQUE(exchange, symbol, announcement_time)
                )
            ''')

            # 创建交易信号表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trading_signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    source TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    content TEXT NOT NULL,
                    exchange TEXT NOT NULL,
                    confidence REAL DEFAULT 1.0,
                    priority INTEGER DEFAULT 1,
                    metadata TEXT,
                    processed BOOLEAN DEFAULT FALSE,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,

                    UNIQUE(source, symbol, timestamp)
                )
            ''')

            # 创建交易订单表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trading_orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_id TEXT UNIQUE,
                    symbol TEXT NOT NULL,
                    side TEXT NOT NULL,
                    amount REAL NOT NULL,
                    leverage INTEGER NOT NULL,
                    stop_loss_percent REAL,
                    status TEXT NOT NULL,
                    filled_price REAL,
                    created_at TEXT NOT NULL,
                    filled_at TEXT,
                    error_message TEXT,
                    signal_id INTEGER,

                    FOREIGN KEY (signal_id) REFERENCES trading_signals (id)
                )
            ''')

            # 创建交易历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trading_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_id TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    side TEXT NOT NULL,
                    entry_price REAL NOT NULL,
                    exit_price REAL,
                    amount REAL NOT NULL,
                    leverage INTEGER NOT NULL,
                    profit REAL DEFAULT 0,
                    profit_percent REAL DEFAULT 0,
                    status TEXT NOT NULL,
                    entry_time TEXT NOT NULL,
                    exit_time TEXT,
                    duration_minutes INTEGER,
                    exchange_source TEXT,
                    signal_source TEXT,
                    close_reason TEXT,

                    UNIQUE(order_id)
                )
            ''')

            # price_performance表由price_performance_analyzer.py管理

            # 创建索引 - 分别处理每个索引，避免单个错误影响整体
            try:
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_exchange_symbol_time
                    ON exchange_data(exchange, symbol, timestamp)
                ''')
                logger.debug("创建exchange_data索引成功")
            except Exception as e:
                logger.warning(f"创建exchange_data索引失败: {e}")

            try:
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_timestamp
                    ON exchange_data(timestamp)
                ''')
                logger.debug("创建timestamp索引成功")
            except Exception as e:
                logger.warning(f"创建timestamp索引失败: {e}")

            try:
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_listing_events
                    ON listing_events(exchange, symbol, announcement_time)
                ''')
                logger.debug("创建listing_events索引成功")
            except Exception as e:
                logger.warning(f"创建listing_events索引失败: {e}")

            try:
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_trading_signals
                    ON trading_signals(source, symbol, timestamp)
                ''')
                logger.debug("创建trading_signals索引成功")
            except Exception as e:
                logger.warning(f"创建trading_signals索引失败: {e}")

            try:
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_trading_orders
                    ON trading_orders(symbol, status, created_at)
                ''')
                logger.debug("创建trading_orders索引成功")
            except Exception as e:
                logger.warning(f"创建trading_orders索引失败: {e}")

            try:
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_trading_history
                    ON trading_history(symbol, entry_time, status)
                ''')
                logger.debug("创建trading_history索引成功")
            except Exception as e:
                logger.warning(f"创建trading_history索引失败: {e}")

            # price_performance索引由price_performance_analyzer.py管理

            conn.commit()
            conn.close()

            logger.info("交易所数据库初始化完成")

        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            # 尝试关闭连接
            try:
                if 'conn' in locals():
                    conn.close()
            except:
                pass

    def migrate_database(self):
        """数据库迁移 - 处理重复数据和约束"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 检查是否需要迁移
            cursor.execute("PRAGMA table_info(listing_events)")
            columns = [row[1] for row in cursor.fetchall()]

            # 检查是否已有UNIQUE约束
            cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='listing_events'")
            table_sql = cursor.fetchone()

            if table_sql and 'UNIQUE(exchange, symbol, announcement_time)' not in table_sql[0]:
                logger.info("检测到旧版数据库，开始迁移...")

                # 创建临时表
                cursor.execute('''
                    CREATE TABLE listing_events_new (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        exchange TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        announcement_time TEXT NOT NULL,
                        listing_time TEXT,
                        announcement_content TEXT,
                        marketcap TEXT,
                        source TEXT,
                        processed BOOLEAN DEFAULT FALSE,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(exchange, symbol, announcement_time)
                    )
                ''')

                # 复制去重后的数据，添加marketcap字段
                cursor.execute('''
                    INSERT OR IGNORE INTO listing_events_new
                    (exchange, symbol, announcement_time, listing_time,
                     announcement_content, marketcap, source, processed, created_at)
                    SELECT DISTINCT exchange, symbol, announcement_time, listing_time,
                           announcement_content,
                           CASE WHEN EXISTS(SELECT 1 FROM pragma_table_info('listing_events') WHERE name='marketcap')
                                THEN marketcap ELSE NULL END as marketcap,
                           source, processed, created_at
                    FROM listing_events
                    ORDER BY created_at DESC
                ''')

                # 删除旧表
                cursor.execute('DROP TABLE listing_events')

                # 重命名新表
                cursor.execute('ALTER TABLE listing_events_new RENAME TO listing_events')

                # 重建索引
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_listing_events
                    ON listing_events(exchange, symbol, announcement_time)
                ''')

                conn.commit()
                logger.info("数据库迁移完成，重复数据已清理")

            # 清理超过90天的旧数据（可选）
            self._cleanup_old_listing_data(cursor, days=90)

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"数据库迁移失败: {e}")

    def _cleanup_old_listing_data(self, cursor, days: int = 90):
        """清理超过指定天数的旧上币数据"""
        try:
            from datetime import datetime, timedelta

            cutoff_date = datetime.now() - timedelta(days=days)
            cutoff_str = cutoff_date.isoformat()

            # 删除超过90天的上币事件
            cursor.execute('''
                DELETE FROM listing_events
                WHERE announcement_time < ?
            ''', (cutoff_str,))

            deleted_count = cursor.rowcount
            if deleted_count > 0:
                logger.info(f"清理了 {deleted_count} 条超过{days}天的旧数据")

        except Exception as e:
            logger.warning(f"清理旧数据失败: {e}")
    
    def save_exchange_data(self, data: ExchangeData, raw_data: Optional[Dict] = None):
        """保存交易所数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO exchange_data
                (exchange, symbol, timestamp, price, volume, market_cap, change_24h,
                 listing_type, announcement_time, raw_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data.exchange,
                data.symbol,
                data.timestamp.isoformat(),
                data.price,
                data.volume,
                data.market_cap,
                data.change_24h,
                data.listing_type,
                data.announcement_time.isoformat() if data.announcement_time else None,
                json.dumps(raw_data) if raw_data else None
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"保存交易所数据失败: {e}")

    def save_listing_event(self, event: ListingEvent, force_overwrite: bool = False):
        """保存上币事件
        
        Args:
            event: 上币事件对象
            force_overwrite: 是否强制覆盖已存在的记录
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if force_overwrite:
                # 强制覆盖模式：先删除现有记录，再插入新记录
                cursor.execute('''
                    DELETE FROM listing_events 
                    WHERE exchange = ? AND symbol = ? AND announcement_time = ?
                ''', (
                    event.exchange,
                    event.symbol,
                    event.announcement_time.isoformat()
                ))
                
                cursor.execute('''
                    INSERT INTO listing_events
                    (exchange, symbol, announcement_time,
                     announcement_content, marketcap, source, processed)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    event.exchange,
                    event.symbol,
                    event.announcement_time.isoformat(),
                    event.announcement_content,
                    getattr(event, 'marketcap', None),
                    event.source,
                    event.processed
                ))
                
                logger.debug(f"强制覆盖上币事件: {event.exchange}-{event.symbol}")
            else:
                # 默认模式：忽略重复记录
                cursor.execute('''
                    INSERT OR IGNORE INTO listing_events
                    (exchange, symbol, announcement_time,
                     announcement_content, marketcap, source, processed)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    event.exchange,
                    event.symbol,
                    event.announcement_time.isoformat(),
                    event.announcement_content,
                    getattr(event, 'marketcap', None),
                    event.source,
                    event.processed
                ))

                # 检查是否插入成功
                if cursor.rowcount > 0:
                    logger.debug(f"保存新上币事件: {event.exchange}-{event.symbol}")
                else:
                    logger.debug(f"上币事件已存在，跳过: {event.exchange}-{event.symbol}")

            conn.commit()
            conn.close()

            logger.info(f"保存上币事件: {event.exchange} - {event.symbol}")

        except Exception as e:
            logger.error(f"保存上币事件失败: {e}")

    def save_trading_signal(self, signal_record: TradingSignalRecord) -> Optional[int]:
        """保存交易信号"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR IGNORE INTO trading_signals
                (source, symbol, timestamp, content, exchange, confidence, priority, metadata, processed)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                signal_record.source,
                signal_record.symbol,
                signal_record.timestamp.isoformat(),
                signal_record.content,
                signal_record.exchange,
                signal_record.confidence,
                signal_record.priority,
                json.dumps(signal_record.metadata) if signal_record.metadata else None,
                signal_record.processed
            ))

            signal_id = cursor.lastrowid if cursor.rowcount > 0 else None

            if signal_id:
                logger.info(f"保存交易信号: {signal_record.source}-{signal_record.symbol} (ID: {signal_id})")
            else:
                logger.debug(f"交易信号已存在，跳过: {signal_record.source}-{signal_record.symbol}")

            conn.commit()
            conn.close()
            return signal_id

        except Exception as e:
            logger.error(f"保存交易信号失败: {e}")
            return None

    def save_trading_order(self, order_record: TradingOrderRecord) -> Optional[int]:
        """保存交易订单"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO trading_orders
                (order_id, symbol, side, amount, leverage, stop_loss_percent, status,
                 filled_price, created_at, filled_at, error_message, signal_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_record.order_id,
                order_record.symbol,
                order_record.side,
                order_record.amount,
                order_record.leverage,
                order_record.stop_loss_percent,
                order_record.status,
                order_record.filled_price,
                order_record.created_at.isoformat(),
                order_record.filled_at.isoformat() if order_record.filled_at else None,
                order_record.error_message,
                order_record.signal_id
            ))

            order_id = cursor.lastrowid
            conn.commit()
            conn.close()

            logger.info(f"保存交易订单: {order_record.symbol} - {order_record.order_id}")
            return order_id

        except Exception as e:
            logger.error(f"保存交易订单失败: {e}")
            return None

    def save_trading_history(self, history_record: TradingHistoryRecord) -> Optional[int]:
        """保存交易历史"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO trading_history
                (order_id, symbol, side, entry_price, exit_price, amount, leverage,
                 profit, profit_percent, status, entry_time, exit_time, duration_minutes,
                 exchange_source, signal_source, close_reason)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                history_record.order_id,
                history_record.symbol,
                history_record.side,
                history_record.entry_price,
                history_record.exit_price,
                history_record.amount,
                history_record.leverage,
                history_record.profit,
                history_record.profit_percent,
                history_record.status,
                history_record.entry_time.isoformat() if history_record.entry_time else None,
                history_record.exit_time.isoformat() if history_record.exit_time else None,
                history_record.duration_minutes,
                history_record.exchange_source,
                history_record.signal_source,
                history_record.close_reason
            ))

            history_id = cursor.lastrowid
            conn.commit()
            conn.close()

            logger.info(f"保存交易历史: {history_record.symbol} - {history_record.order_id}")
            return history_id

        except Exception as e:
            logger.error(f"保存交易历史失败: {e}")
            return None

    def get_trading_signals(self, limit: int = 100, processed: Optional[bool] = None) -> List[TradingSignalRecord]:
        """获取交易信号"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            query = '''
                SELECT id, source, symbol, timestamp, content, exchange, confidence, priority, metadata, processed
                FROM trading_signals
            '''
            params = []

            if processed is not None:
                query += " WHERE processed = ?"
                params.append(processed)

            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)

            cursor.execute(query, params)
            rows = cursor.fetchall()
            conn.close()

            signals = []
            for row in rows:
                metadata = json.loads(row[8]) if row[8] else None
                signal = TradingSignalRecord(
                    id=row[0],
                    source=row[1],
                    symbol=row[2],
                    timestamp=datetime.fromisoformat(row[3]),
                    content=row[4],
                    exchange=row[5],
                    confidence=row[6],
                    priority=row[7],
                    metadata=metadata,
                    processed=bool(row[9])
                )
                signals.append(signal)

            return signals

        except Exception as e:
            logger.error(f"获取交易信号失败: {e}")
            return []

    def get_trading_history(self, limit: int = 100, symbol: Optional[str] = None) -> List[TradingHistoryRecord]:
        """获取交易历史"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            query = '''
                SELECT id, order_id, symbol, side, entry_price, exit_price, amount, leverage,
                       profit, profit_percent, status, entry_time, exit_time, duration_minutes,
                       exchange_source, signal_source, close_reason
                FROM trading_history
            '''
            params = []

            if symbol:
                query += " WHERE symbol = ?"
                params.append(symbol)

            query += " ORDER BY entry_time DESC LIMIT ?"
            params.append(limit)

            cursor.execute(query, params)
            rows = cursor.fetchall()
            conn.close()

            history = []
            for row in rows:
                record = TradingHistoryRecord(
                    id=row[0],
                    order_id=row[1],
                    symbol=row[2],
                    side=row[3],
                    entry_price=row[4],
                    exit_price=row[5],
                    amount=row[6],
                    leverage=row[7],
                    profit=row[8],
                    profit_percent=row[9],
                    status=row[10],
                    entry_time=datetime.fromisoformat(row[11]) if row[11] else None,
                    exit_time=datetime.fromisoformat(row[12]) if row[12] else None,
                    duration_minutes=row[13],
                    exchange_source=row[14],
                    signal_source=row[15],
                    close_reason=row[16]
                )
                history.append(record)

            return history

        except Exception as e:
            logger.error(f"获取交易历史失败: {e}")
            return []

    def get_trading_statistics(self) -> Dict[str, Any]:
        """获取交易统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 基础统计
            cursor.execute('''
                SELECT
                    COUNT(*) as total_trades,
                    COUNT(CASE WHEN status = 'closed' AND profit > 0 THEN 1 END) as successful_trades,
                    COUNT(CASE WHEN status = 'closed' AND profit <= 0 THEN 1 END) as failed_trades,
                    SUM(profit) as total_profit,
                    AVG(profit) as avg_profit,
                    MAX(profit) as max_profit,
                    MIN(profit) as max_loss
                FROM trading_history
                WHERE status = 'closed'
            ''')

            row = cursor.fetchone()

            if row and row[0] > 0:
                total_trades = row[0]
                successful_trades = row[1] or 0
                failed_trades = row[2] or 0
                win_rate = (successful_trades / total_trades * 100) if total_trades > 0 else 0

                stats = {
                    'total_trades': total_trades,
                    'successful_trades': successful_trades,
                    'failed_trades': failed_trades,
                    'win_rate': round(win_rate, 2),
                    'total_profit': round(row[3] or 0, 2),
                    'avg_profit': round(row[4] or 0, 2),
                    'max_profit': round(row[5] or 0, 2),
                    'max_loss': round(row[6] or 0, 2)
                }
            else:
                stats = {
                    'total_trades': 0,
                    'successful_trades': 0,
                    'failed_trades': 0,
                    'win_rate': 0,
                    'total_profit': 0,
                    'avg_profit': 0,
                    'max_profit': 0,
                    'max_loss': 0
                }

            conn.close()
            return stats

        except Exception as e:
            logger.error(f"获取交易统计失败: {e}")
            return {}

    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 总记录数
            cursor.execute("SELECT COUNT(*) FROM listing_events")
            total_count = cursor.fetchone()[0]

            # 按交易所统计
            cursor.execute('''
                SELECT exchange, COUNT(*)
                FROM listing_events
                GROUP BY exchange
            ''')
            exchange_stats = dict(cursor.fetchall())

            # 最早和最新记录时间
            cursor.execute('''
                SELECT MIN(announcement_time), MAX(announcement_time)
                FROM listing_events
            ''')
            time_range = cursor.fetchone()

            # 最近30天的记录数
            from datetime import datetime, timedelta
            cutoff_30d = (datetime.now() - timedelta(days=30)).isoformat()
            cursor.execute('''
                SELECT COUNT(*) FROM listing_events
                WHERE announcement_time >= ?
            ''', (cutoff_30d,))
            recent_count = cursor.fetchone()[0]

            conn.close()

            return {
                'total_listings': total_count,
                'exchanges': exchange_stats,
                'time_range': {
                    'earliest': time_range[0],
                    'latest': time_range[1]
                },
                'recent_30d': recent_count
            }

        except Exception as e:
            logger.error(f"获取数据库统计失败: {e}")
            return {
                'total_listings': 0,
                'exchanges': {},
                'time_range': {'earliest': None, 'latest': None},
                'recent_30d': 0
            }

    def get_listing_events(self, exchange: Optional[str] = None,
                          processed: Optional[bool] = None) -> List[ListingEvent]:
        """获取上币事件"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            query = "SELECT exchange, symbol, announcement_time, announcement_content, marketcap, source, processed FROM listing_events"
            params = []
            conditions = []

            if exchange:
                conditions.append("exchange = ?")
                params.append(exchange)

            if processed is not None:
                conditions.append("processed = ?")
                params.append(processed)

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            query += " ORDER BY announcement_time DESC"

            cursor.execute(query, params)
            rows = cursor.fetchall()
            conn.close()

            return [
                ListingEvent(
                    exchange=row[0],
                    symbol=row[1],
                    announcement_time=datetime.fromisoformat(row[2]),
                    announcement_content=row[3],
                    marketcap=row[4],
                    source=row[5],
                    processed=bool(row[6])
                )
                for row in rows
            ]

        except Exception as e:
            logger.error(f"获取上币事件失败: {e}")
            return []
    
    def get_latest_data(self, exchange: str, symbol: str) -> Optional[ExchangeData]:
        """获取最新数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT exchange, symbol, timestamp, price, volume, market_cap, change_24h
                FROM exchange_data 
                WHERE exchange = ? AND symbol = ?
                ORDER BY timestamp DESC 
                LIMIT 1
            ''', (exchange, symbol))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                return ExchangeData(
                    exchange=row[0],
                    symbol=row[1],
                    timestamp=datetime.fromisoformat(row[2]),
                    price=row[3],
                    volume=row[4],
                    market_cap=row[5],
                    change_24h=row[6]
                )
            
            return None
            
        except Exception as e:
            logger.error(f"获取最新数据失败: {e}")
            return None
    
    def get_historical_data(self, exchange: str, symbol: str, 
                          start_time: datetime, end_time: datetime) -> List[ExchangeData]:
        """获取历史数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT exchange, symbol, timestamp, price, volume, market_cap, change_24h
                FROM exchange_data 
                WHERE exchange = ? AND symbol = ? 
                AND timestamp BETWEEN ? AND ?
                ORDER BY timestamp ASC
            ''', (exchange, symbol, start_time.isoformat(), end_time.isoformat()))
            
            rows = cursor.fetchall()
            conn.close()
            
            return [
                ExchangeData(
                    exchange=row[0],
                    symbol=row[1],
                    timestamp=datetime.fromisoformat(row[2]),
                    price=row[3],
                    volume=row[4],
                    market_cap=row[5],
                    change_24h=row[6]
                )
                for row in rows
            ]
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return []
    
    def get_exchange_symbols(self, exchange: str) -> List[str]:
        """获取交易所的所有交易对"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT DISTINCT symbol 
                FROM exchange_data 
                WHERE exchange = ?
                ORDER BY symbol
            ''', (exchange,))
            
            rows = cursor.fetchall()
            conn.close()
            
            return [row[0] for row in rows]
            
        except Exception as e:
            logger.error(f"获取交易对列表失败: {e}")
            return []
    
    def cleanup_old_data(self, days: int = 30):
        """清理旧数据"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                DELETE FROM exchange_data 
                WHERE timestamp < ?
            ''', (cutoff_time.isoformat(),))
            
            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            logger.info(f"清理了 {deleted_count} 条 {days} 天前的数据")
            
        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
    
    def get_exchange_data(self, exchange: str, limit: int = 100) -> List[ExchangeData]:
        """获取交易所数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT exchange, symbol, timestamp, price, volume, market_cap, change_24h,
                       listing_type, announcement_time
                FROM exchange_data
                WHERE exchange = ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (exchange, limit))

            rows = cursor.fetchall()
            conn.close()

            return [
                ExchangeData(
                    exchange=row[0],
                    symbol=row[1],
                    timestamp=datetime.fromisoformat(row[2]),
                    price=row[3],
                    volume=row[4],
                    market_cap=row[5],
                    change_24h=row[6],
                    listing_type=row[7],
                    announcement_time=datetime.fromisoformat(row[8]) if row[8] else None
                )
                for row in rows
            ]

        except Exception as e:
            logger.error(f"获取交易所数据失败: {e}")
            return []

    def get_all_exchanges(self) -> List[str]:
        """获取所有交易所列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT DISTINCT exchange
                FROM exchange_data
                ORDER BY exchange
            ''')

            rows = cursor.fetchall()
            conn.close()

            return [row[0] for row in rows]

        except Exception as e:
            logger.error(f"获取交易所列表失败: {e}")
            return []

    def get_recent_listings(self, exchange: Optional[str] = None, days: int = 7) -> List[ListingEvent]:
        """获取最近的上币事件"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cutoff_time = datetime.now() - timedelta(days=days)

            if exchange:
                cursor.execute('''
                    SELECT exchange, symbol, announcement_time,
                           announcement_content, marketcap, source, processed
                    FROM listing_events
                    WHERE exchange = ? AND announcement_time >= ?
                    ORDER BY announcement_time DESC
                ''', (exchange, cutoff_time.isoformat()))
            else:
                cursor.execute('''
                    SELECT exchange, symbol, announcement_time,
                           announcement_content, marketcap, source, processed
                    FROM listing_events
                    WHERE announcement_time >= ?
                    ORDER BY announcement_time DESC
                ''', (cutoff_time.isoformat(),))

            rows = cursor.fetchall()
            conn.close()

            return [
                ListingEvent(
                    exchange=row[0],
                    symbol=row[1],
                    announcement_time=datetime.fromisoformat(row[2]),
                    announcement_content=row[3],
                    marketcap=row[4],
                    source=row[5],
                    processed=bool(row[6])
                )
                for row in rows
            ]

        except Exception as e:
            logger.error(f"获取最近上币事件失败: {e}")
            return []

    def get_statistics(self, exchange: str) -> Dict[str, Any]:
        """获取交易所统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 基础统计
            cursor.execute('''
                SELECT
                    COUNT(*) as total_records,
                    COUNT(DISTINCT symbol) as unique_symbols,
                    MIN(timestamp) as earliest_data,
                    MAX(timestamp) as latest_data
                FROM exchange_data
                WHERE exchange = ?
            ''', (exchange,))

            row = cursor.fetchone()

            if row and row[0] > 0:
                stats = {
                    'exchange': exchange,
                    'total_records': row[0],
                    'unique_symbols': row[1],
                    'earliest_data': row[2],
                    'latest_data': row[3],
                    'data_span_days': 0
                }

                # 计算数据跨度
                if row[2] and row[3]:
                    earliest = datetime.fromisoformat(row[2])
                    latest = datetime.fromisoformat(row[3])
                    stats['data_span_days'] = (latest - earliest).days

                conn.close()
                return stats
            else:
                conn.close()
                return {
                    'exchange': exchange,
                    'total_records': 0,
                    'unique_symbols': 0,
                    'message': '暂无数据'
                }

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {'exchange': exchange, 'error': str(e)}

    # ==================== 各交易所便捷方法 ====================

    def save_binance_listing(self, symbol: str, announcement_time: datetime,
                           announcement_content: str, source: str = "binance_api"):
        """保存Binance上币公告"""
        event = ListingEvent(
            exchange="binance",
            symbol=symbol,
            announcement_time=announcement_time,
            announcement_content=announcement_content,
            source=source
        )
        self.save_listing_event(event)

    def save_coinbase_listing(self, symbol: str, announcement_time: datetime,
                            announcement_content: str, source: str = "coinbase_api"):
        """保存Coinbase上币公告"""
        event = ListingEvent(
            exchange="coinbase",
            symbol=symbol,
            announcement_time=announcement_time,
            announcement_content=announcement_content,
            source=source
        )
        self.save_listing_event(event)

    def save_upbit_listing(self, symbol: str, announcement_time: datetime,
                          announcement_content: str, source: str = "upbit_api"):
        """保存Upbit上币公告"""
        event = ListingEvent(
            exchange="upbit",
            symbol=symbol,
            announcement_time=announcement_time,
            announcement_content=announcement_content,
            source=source
        )
        self.save_listing_event(event)

    def save_bithumb_listing(self, symbol: str, announcement_time: datetime,
                           announcement_content: str, source: str = "bithumb_api"):
        """保存Bithumb上币公告"""
        event = ListingEvent(
            exchange="bithumb",
            symbol=symbol,
            announcement_time=announcement_time,
            announcement_content=announcement_content,
            source=source
        )
        self.save_listing_event(event)

    def get_binance_listings(self, processed: Optional[bool] = None) -> List[ListingEvent]:
        """获取Binance上币事件"""
        return self.get_listing_events("binance", processed)

    def get_coinbase_listings(self, processed: Optional[bool] = None) -> List[ListingEvent]:
        """获取Coinbase上币事件"""
        return self.get_listing_events("coinbase", processed)

    def get_upbit_listings(self, processed: Optional[bool] = None) -> List[ListingEvent]:
        """获取Upbit上币事件"""
        return self.get_listing_events("upbit", processed)

    def get_bithumb_listings(self, processed: Optional[bool] = None) -> List[ListingEvent]:
        """获取Bithumb上币事件"""
        return self.get_listing_events("bithumb", processed)


# 为了兼容性，保留旧类名的别名
ExchangeDatabaseManager = UnifiedDatabaseManager

# 全局实例 - 使用新的统一数据库文件
unified_db = UnifiedDatabaseManager()

# 为了兼容性，保留旧的全局变量名
exchange_db = unified_db

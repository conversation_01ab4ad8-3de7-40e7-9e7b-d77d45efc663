services:
  goldbot:
    build: .
    container_name: goldbot-trading
    restart: unless-stopped
    ports:
      - "8080:8081"
      - "8000:8081"
    volumes:
      # 挂载配置和代码
      - .:/app
      # 挂载Telegram会话文件（如果存在）- 需要读写权限
      - ./telegram_session.session:/app/telegram_session.session
      # 数据持久化目录
      - goldbot-data:/app/data
      - goldbot-logs:/app/logs
      - goldbot-cache:/app/cache
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - TZ=Asia/Shanghai
    networks:
      - goldbot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis缓存 (可选)
  redis:
    image: redis:7-alpine
    container_name: goldbot-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - goldbot-redis:/data
    networks:
      - goldbot-network
    command: redis-server --appendonly yes

volumes:
  goldbot-data:
    driver: local
  goldbot-logs:
    driver: local
  goldbot-cache:
    driver: local
  goldbot-redis:
    driver: local

networks:
  goldbot-network:
    driver: bridge

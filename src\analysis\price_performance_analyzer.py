#!/usr/bin/env python3
"""
价格表现分析器
分析代币上币公告后的价格表现
"""
import requests
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from loguru import logger
import statistics
import time

@dataclass
class PricePerformanceData:
    """价格表现数据结构"""
    symbol: str
    exchange: str
    announcement_time: datetime
    
    # 时间段涨幅
    gain_1m: Optional[float] = None      # 1分钟涨幅
    gain_5m: Optional[float] = None      # 5分钟涨幅
    gain_15m: Optional[float] = None     # 15分钟涨幅
    gain_1h: Optional[float] = None      # 1小时涨幅
    gain_4h: Optional[float] = None      # 4小时涨幅
    gain_1d: Optional[float] = None      # 1天涨幅
    gain_3d: Optional[float] = None      # 3天涨幅
    
    # 峰值分析
    time_to_peak: Optional[int] = None   # 达峰时长(分钟)
    max_gain: Optional[float] = None     # 最大涨幅
    peak_pullback: Optional[float] = None # 峰值回落幅度
    volatility: Optional[float] = None   # 波动率
    
    # 基础数据
    base_price: Optional[float] = None   # 基准价格
    peak_price: Optional[float] = None   # 峰值价格
    current_price: Optional[float] = None # 当前价格
    
    # 分析状态
    analyzed: bool = False
    analysis_time: Optional[datetime] = None
    error_message: Optional[str] = None

class PricePerformanceAnalyzer:
    """价格表现分析器"""
    
    def __init__(self, db_path: str = "exchange_data.db"):
        self.db_path = db_path
        self.binance_base_url = "https://fapi.binance.com"  # 合约API
        self.spot_base_url = "https://api.binance.com"      # 现货API (用于获取交易对列表)
        self.available_symbols = set()  # 缓存可用的合约交易对
        self._init_performance_table()
        self._load_available_symbols()
    
    def _init_performance_table(self):
        """初始化价格表现表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS price_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    exchange TEXT NOT NULL,
                    announcement_time TEXT NOT NULL,
                    
                    -- 时间段涨幅
                    gain_1m REAL,
                    gain_5m REAL,
                    gain_15m REAL,
                    gain_1h REAL,
                    gain_4h REAL,
                    gain_1d REAL,
                    gain_3d REAL,
                    
                    -- 峰值分析
                    time_to_peak INTEGER,
                    max_gain REAL,
                    peak_pullback REAL,
                    volatility REAL,
                    
                    -- 基础数据
                    base_price REAL,
                    peak_price REAL,
                    current_price REAL,
                    
                    -- 分析状态
                    analyzed BOOLEAN DEFAULT FALSE,
                    analysis_time TEXT,
                    error_message TEXT,
                    
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, exchange, announcement_time)
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("价格表现表初始化完成")

        except Exception as e:
            logger.error(f"初始化价格表现表失败: {e}")

    def _load_available_symbols(self):
        """加载可用的合约交易对"""
        try:
            # 获取合约交易对信息
            url = f"{self.binance_base_url}/fapi/v1/exchangeInfo"
            response = requests.get(url, timeout=30)

            if response.status_code == 200:
                data = response.json()
                symbols = data.get('symbols', [])

                # 只保留USDT永续合约且状态为TRADING的交易对
                for symbol_info in symbols:
                    if (symbol_info.get('status') == 'TRADING' and
                        symbol_info.get('contractType') == 'PERPETUAL' and
                        symbol_info.get('quoteAsset') == 'USDT'):
                        self.available_symbols.add(symbol_info['symbol'])

                logger.info(f"✅ 加载了 {len(self.available_symbols)} 个可用的USDT永续合约")

                # 显示一些示例
                sample_symbols = list(self.available_symbols)[:10]
                logger.info(f"示例合约: {', '.join(sample_symbols)}")

            else:
                logger.error(f"获取合约交易对失败: {response.status_code}")

        except Exception as e:
            logger.error(f"加载可用交易对失败: {e}")

    def is_symbol_available(self, symbol: str) -> bool:
        """快速检查代币是否有合约交易对"""
        trading_symbol = f"{symbol}USDT" if not symbol.endswith('USDT') else symbol
        return trading_symbol in self.available_symbols

    def refresh_available_symbols(self):
        """刷新可用交易对列表"""
        logger.info("🔄 刷新合约交易对列表...")
        self.available_symbols.clear()
        self._load_available_symbols()
    
    def get_binance_klines(self, symbol: str, start_time: datetime, end_time: datetime, interval: str = "1m") -> List[List]:
        """获取Binance合约K线数据"""
        try:
            # 构建交易对符号 (如 TREE -> TREEUSDT)
            trading_symbol = f"{symbol}USDT" if not symbol.endswith('USDT') else symbol

            # 使用合约API获取K线数据
            url = f"{self.binance_base_url}/fapi/v1/klines"
            params = {
                'symbol': trading_symbol,
                'interval': interval,
                'startTime': int(start_time.timestamp() * 1000),
                'endTime': int(end_time.timestamp() * 1000),
                'limit': 1000
            }

            response = requests.get(url, params=params, timeout=30)

            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"获取{trading_symbol}合约K线数据失败: {response.status_code}")
                return []

        except Exception as e:
            logger.error(f"获取合约K线数据异常: {e}")
            return []
    
    def calculate_price_performance(self, symbol: str, exchange: str, announcement_time: datetime) -> PricePerformanceData:
        """计算价格表现"""
        try:
            logger.info(f"开始分析 {symbol} ({exchange}) 的价格表现")
            
            # 获取分析时间范围 (公告前30分钟到公告后3天)
            start_time = announcement_time - timedelta(minutes=30)
            end_time = announcement_time + timedelta(days=3)
            
            # 获取1分钟K线数据
            klines = self.get_binance_klines(symbol, start_time, end_time, "1m")
            
            if not klines:
                return PricePerformanceData(
                    symbol=symbol,
                    exchange=exchange,
                    announcement_time=announcement_time,
                    error_message="无法获取K线数据"
                )
            
            # 解析K线数据
            price_data = []
            for kline in klines:
                timestamp = datetime.fromtimestamp(kline[0] / 1000)
                open_price = float(kline[1])
                high_price = float(kline[2])
                low_price = float(kline[3])
                close_price = float(kline[4])
                volume = float(kline[5])
                
                price_data.append({
                    'timestamp': timestamp,
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'volume': volume
                })
            
            # 找到公告时间对应的基准价格
            base_price = self._find_base_price(price_data, announcement_time)
            if not base_price:
                return PricePerformanceData(
                    symbol=symbol,
                    exchange=exchange,
                    announcement_time=announcement_time,
                    error_message="无法确定基准价格"
                )
            
            # 计算各时间段涨幅
            performance = PricePerformanceData(
                symbol=symbol,
                exchange=exchange,
                announcement_time=announcement_time,
                base_price=base_price
            )
            
            # 计算时间段涨幅
            logger.info(f"计算各时间段涨幅 (基准价格: ${base_price})")
            performance.gain_1m = self._calculate_gain_at_time(price_data, announcement_time, timedelta(minutes=1), base_price)
            performance.gain_5m = self._calculate_gain_at_time(price_data, announcement_time, timedelta(minutes=5), base_price)
            performance.gain_15m = self._calculate_gain_at_time(price_data, announcement_time, timedelta(minutes=15), base_price)
            performance.gain_1h = self._calculate_gain_at_time(price_data, announcement_time, timedelta(hours=1), base_price)
            performance.gain_4h = self._calculate_gain_at_time(price_data, announcement_time, timedelta(hours=4), base_price)
            performance.gain_1d = self._calculate_gain_at_time(price_data, announcement_time, timedelta(days=1), base_price)
            performance.gain_3d = self._calculate_gain_at_time(price_data, announcement_time, timedelta(days=3), base_price)

            logger.info(f"时间段涨幅: 1m={performance.gain_1m}%, 1h={performance.gain_1h}%, 1d={performance.gain_1d}%")
            
            # 计算峰值分析
            peak_analysis = self._calculate_peak_analysis(price_data, announcement_time, base_price)
            performance.time_to_peak = peak_analysis['time_to_peak']
            performance.max_gain = peak_analysis['max_gain']
            performance.peak_price = peak_analysis['peak_price']
            performance.peak_pullback = peak_analysis['peak_pullback']
            performance.current_price = peak_analysis['current_price']
            
            # 计算波动率
            performance.volatility = self._calculate_volatility(price_data, announcement_time)
            
            performance.analyzed = True
            performance.analysis_time = datetime.now()
            
            logger.info(f"✅ {symbol} 价格表现分析完成")
            return performance
            
        except Exception as e:
            logger.error(f"计算价格表现失败: {e}")
            return PricePerformanceData(
                symbol=symbol,
                exchange=exchange,
                announcement_time=announcement_time,
                error_message=str(e)
            )
    
    def _find_base_price(self, price_data: List[Dict], announcement_time: datetime) -> Optional[float]:
        """找到公告那一分钟的开盘价作为基准"""
        # 正确逻辑：使用公告那一分钟的开盘价作为基准
        # 这样才能准确反映公告发布后的真实涨幅

        if not price_data:
            return None

        # 找到公告时间对应的K线数据
        # 策略：找到公告时间所在的那一分钟的K线（而不是最近的）
        closest_data = None

        for data in price_data:
            # 检查这个K线是否包含公告时间
            kline_start = data['timestamp']
            kline_end = kline_start + timedelta(minutes=1)

            # 如果公告时间在这个K线的时间范围内
            if kline_start <= announcement_time < kline_end:
                closest_data = data
                break

        # 如果没找到精确匹配，则找最近的
        if closest_data is None:
            min_diff = float('inf')
            for data in price_data:
                diff = abs((data['timestamp'] - announcement_time).total_seconds())
                if diff < min_diff:
                    min_diff = diff
                    closest_data = data

        if closest_data:
            # 使用公告那一分钟的开盘价作为基准
            base_price = closest_data['open']

            logger.info(f"基准价格: ${base_price} (公告时间 {announcement_time} 对应的开盘价)")
            logger.info(f"对应K线时间: {closest_data['timestamp']}")

            return base_price

        return None
    
    def _calculate_gain_at_time(self, price_data: List[Dict], announcement_time: datetime,
                               time_delta: timedelta, base_price: float) -> Optional[float]:
        """计算指定时间点的涨幅"""
        target_time = announcement_time + time_delta

        # 特殊处理1分钟涨幅：计算公告那一分钟内的涨幅
        if time_delta.total_seconds() == 60:  # 1分钟
            # 找到包含公告时间的K线
            for data in price_data:
                kline_start = data['timestamp']
                kline_end = kline_start + timedelta(minutes=1)

                if kline_start <= announcement_time < kline_end:
                    # 使用这一分钟的收盘价计算涨幅
                    gain = ((data['close'] - base_price) / base_price) * 100
                    logger.debug(f"时间段{time_delta}: 价格${data['close']}, 涨幅{gain:.2f}% (公告那一分钟内)")
                    return gain

            # 如果没找到，返回None
            return None

        # 对于其他时间段，只考虑公告后的数据
        after_announcement = [d for d in price_data if d['timestamp'] > announcement_time]

        if not after_announcement:
            return None

        # 找到目标时间最近的价格
        closest_data = None
        min_diff = float('inf')

        for data in after_announcement:
            diff = abs((data['timestamp'] - target_time).total_seconds())
            if diff < min_diff:
                min_diff = diff
                closest_data = data

        # 放宽时间容忍度，特别是对于较长时间段
        max_tolerance = min(3600, time_delta.total_seconds() * 0.1)  # 最多1小时或时间段的10%

        if closest_data and min_diff <= max_tolerance:
            gain = ((closest_data['close'] - base_price) / base_price) * 100
            logger.debug(f"时间段{time_delta}: 价格${closest_data['close']}, 涨幅{gain:.2f}%")
            return gain

        return None
    
    def _calculate_peak_analysis(self, price_data: List[Dict], announcement_time: datetime,
                                base_price: float) -> Dict[str, Any]:
        """计算峰值分析"""
        # 只考虑公告后的数据
        post_announcement_data = [d for d in price_data if d['timestamp'] > announcement_time]

        if not post_announcement_data:
            return {
                'time_to_peak': None,
                'max_gain': None,
                'peak_price': None,
                'peak_pullback': None,
                'current_price': None
            }

        # 找到最高价格
        peak_data = max(post_announcement_data, key=lambda x: x['high'])
        peak_price = peak_data['high']
        max_gain = ((peak_price - base_price) / base_price) * 100

        # 计算达峰时长（从公告时间开始）
        time_to_peak = int((peak_data['timestamp'] - announcement_time).total_seconds() / 60)

        # 计算峰值回落幅度（从峰值到数据结束时的价格）
        current_price = post_announcement_data[-1]['close']
        peak_pullback = ((peak_price - current_price) / peak_price) * 100 if peak_price > current_price else 0

        logger.info(f"峰值分析: 最高${peak_price}, 最大涨幅{max_gain:.2f}%, 达峰{time_to_peak}分钟")

        return {
            'time_to_peak': time_to_peak,
            'max_gain': max_gain,
            'peak_price': peak_price,
            'peak_pullback': peak_pullback,
            'current_price': current_price
        }
    
    def _calculate_volatility(self, price_data: List[Dict], announcement_time: datetime) -> Optional[float]:
        """计算波动率"""
        post_data = [d for d in price_data if d['timestamp'] >= announcement_time]
        
        if len(post_data) < 2:
            return None
        
        # 计算价格变化率的标准差
        prices = [d['close'] for d in post_data]
        returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
        
        if returns:
            return statistics.stdev(returns) * 100  # 转换为百分比
        
        return None

    def save_performance_data(self, performance: PricePerformanceData):
        """保存价格表现数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO price_performance
                (symbol, exchange, announcement_time, gain_1m, gain_5m, gain_15m,
                 gain_1h, gain_4h, gain_1d, gain_3d, time_to_peak, max_gain,
                 peak_pullback, volatility, base_price, peak_price, current_price,
                 analyzed, analysis_time, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                performance.symbol,
                performance.exchange,
                performance.announcement_time.isoformat(),
                performance.gain_1m,
                performance.gain_5m,
                performance.gain_15m,
                performance.gain_1h,
                performance.gain_4h,
                performance.gain_1d,
                performance.gain_3d,
                performance.time_to_peak,
                performance.max_gain,
                performance.peak_pullback,
                performance.volatility,
                performance.base_price,
                performance.peak_price,
                performance.current_price,
                performance.analyzed,
                performance.analysis_time.isoformat() if performance.analysis_time else None,
                performance.error_message
            ))

            conn.commit()
            conn.close()
            logger.info(f"✅ {performance.symbol} 价格表现数据已保存")

        except Exception as e:
            logger.error(f"保存价格表现数据失败: {e}")

    def analyze_all_listings(self, limit: int = None, force_reanalyze: bool = False):
        """分析所有上币事件的合约价格表现"""
        try:
            # 获取上币事件
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if force_reanalyze:
                # 强制重新分析：获取所有代币
                logger.info("🔄 强制重新分析模式：将重新分析所有代币")
                if limit is None:
                    cursor.execute('''
                        SELECT DISTINCT symbol, exchange, announcement_time
                        FROM listing_events
                        ORDER BY announcement_time DESC
                    ''')
                else:
                    cursor.execute('''
                        SELECT DISTINCT symbol, exchange, announcement_time
                        FROM listing_events
                        ORDER BY announcement_time DESC
                        LIMIT ?
                    ''', (limit,))
            else:
                # 正常模式：只分析未分析的代币
                logger.info("📊 正常分析模式：只分析未分析的代币")
                if limit is None:
                    cursor.execute('''
                        SELECT DISTINCT symbol, exchange, announcement_time
                        FROM listing_events
                        WHERE symbol NOT IN (
                            SELECT symbol FROM price_performance
                            WHERE analyzed = 1
                        )
                        ORDER BY announcement_time DESC
                    ''')
                else:
                    cursor.execute('''
                        SELECT DISTINCT symbol, exchange, announcement_time
                        FROM listing_events
                        WHERE symbol NOT IN (
                            SELECT symbol FROM price_performance
                            WHERE analyzed = 1
                        )
                        ORDER BY announcement_time DESC
                        LIMIT ?
                    ''', (limit,))

            events = cursor.fetchall()
            conn.close()

            logger.info(f"找到 {len(events)} 个待分析的上币事件")
            logger.info(f"可用合约交易对: {len(self.available_symbols)} 个")

            analyzed_count = 0
            skipped_count = 0

            for i, (symbol, exchange, announcement_time_str) in enumerate(events, 1):
                try:
                    announcement_time = datetime.fromisoformat(announcement_time_str)

                    logger.info(f"[{i}/{len(events)}] 检查 {symbol} ({exchange})")

                    # 快速检查是否有合约交易对（本地缓存，无需API调用）
                    if not self.is_symbol_available(symbol):
                        logger.info(f"   ⏭️ {symbol} 没有USDT永续合约，跳过")
                        skipped_count += 1
                        continue

                    logger.info(f"   🔄 开始分析 {symbol} 合约价格表现...")

                    # 计算价格表现
                    performance = self.calculate_price_performance(symbol, exchange, announcement_time)

                    # 保存结果
                    self.save_performance_data(performance)

                    if performance.analyzed:
                        analyzed_count += 1
                        logger.info(f"   ✅ {symbol} 分析成功 ({analyzed_count}个已完成)")
                    else:
                        logger.info(f"   ❌ {symbol} 分析失败: {performance.error_message}")

                    # 避免API限制，稍作延迟
                    time.sleep(0.5)  # 减少延迟，因为不需要额外的交易对检查

                except Exception as e:
                    logger.error(f"分析 {symbol} 失败: {e}")
                    continue

            logger.info(f"✅ 批量分析完成！")
            logger.info(f"   📊 成功分析: {analyzed_count} 个代币")
            logger.info(f"   ⏭️ 跳过无合约: {skipped_count} 个代币")
            logger.info(f"   📈 分析的都是USDT永续合约数据")

        except Exception as e:
            logger.error(f"批量分析失败: {e}")

    def get_performance_data(self, symbol: str = None, exchange: str = None) -> List[PricePerformanceData]:
        """获取价格表现数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            query = "SELECT * FROM price_performance"
            params = []

            conditions = []
            if symbol:
                conditions.append("symbol = ?")
                params.append(symbol)
            if exchange:
                conditions.append("exchange = ?")
                params.append(exchange)

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            query += " ORDER BY announcement_time DESC"

            cursor.execute(query, params)
            rows = cursor.fetchall()
            conn.close()

            # 转换为PricePerformanceData对象
            performances = []
            for row in rows:
                performance = PricePerformanceData(
                    symbol=row[1],
                    exchange=row[2],
                    announcement_time=datetime.fromisoformat(row[3]),
                    gain_1m=row[4],
                    gain_5m=row[5],
                    gain_15m=row[6],
                    gain_1h=row[7],
                    gain_4h=row[8],
                    gain_1d=row[9],
                    gain_3d=row[10],
                    time_to_peak=row[11],
                    max_gain=row[12],
                    peak_pullback=row[13],
                    volatility=row[14],
                    base_price=row[15],
                    peak_price=row[16],
                    current_price=row[17],
                    analyzed=bool(row[18]),
                    analysis_time=datetime.fromisoformat(row[19]) if row[19] else None,
                    error_message=row[20]
                )
                performances.append(performance)

            return performances

        except Exception as e:
            logger.error(f"获取价格表现数据失败: {e}")
            return []


# 全局实例
price_analyzer = PricePerformanceAnalyzer()

# 优化后的依赖项 - 仅包含实际使用的包

# 核心依赖
loguru>=0.7.0                    # 日志系统
pyyaml>=6.0                      # YAML配置文件解析

# 交易相关
ccxt>=4.0.0                      # 加密货币交易所API
python-binance>=1.0.19           # 币安API客户端

# Telegram相关
telethon>=1.29.0                 # Telegram客户端

# Web界面
fastapi>=0.104.0                 # Web框架
uvicorn>=0.24.0                  # ASGI服务器

# 系统相关
aiohttp>=3.8.0                   # 异步HTTP客户端
psutil>=5.9.0                    # 系统资源监控
requests>=2.31.0                 # HTTP请求
watchdog>=3.0.0                  # 文件监控（配置文件变化监控）
schedule>=1.2.0                  # 任务调度（日志清理）

# 任务调度
schedule>=1.2.0                  # 任务调度（日志清理和配置备份）

# 未使用的依赖项（已注释）
# UNUSED: aiofiles>=23.0.0                 # 异步文件操作
# UNUSED: websockets>=12.0                 # WebSocket支持
# UNUSED: pandas>=2.0.0                    # 数据分析
# UNUSED: numpy>=1.24.0                    # 数值计算

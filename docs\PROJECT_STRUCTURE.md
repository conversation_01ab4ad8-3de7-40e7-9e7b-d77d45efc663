# 📁 Goldbot 项目结构

## 🏗️ 项目概览

```
goldbot/
├── 📄 配置文件
│   ├── config.yaml                 # 主配置文件
│   └── requirements.txt            # Python依赖
│
├── 🐳 Docker部署
│   ├── Dockerfile                  # Docker镜像定义
│   ├── docker-compose.yml          # Docker编排配置
│   ├── deploy.sh                   # Linux部署脚本
│   ├── deploy.bat                  # Windows部署脚本
│   └── docker-build.bat            # Docker构建脚本
│
├── 📱 Telegram配置
│   ├── setup_telegram_session.py   # 单文件会话生成器（推荐）
│   ├── setup_telegram_sessions.py  # 兼容的会话生成器
│   └── telegram_session.session    # Telegram会话文件
│
├── 📚 文档
│   ├── DOCKER_DEPLOYMENT.md        # Docker部署指南
│   ├── TELEGRAM_SETUP.md           # Telegram配置指南
│   ├── README.md                   # 项目说明
│   └── docs/                       # 详细文档目录
│       ├── CONFIG_MANAGEMENT.md
│       ├── FIXED_STRATEGY_GUIDE.md
│       ├── QUICK_DEPLOY_GUIDE.md
│       └── guides/
│
├── 🤖 核心代码
│   ├── main.py                     # 主程序入口
│   └── src/                        # 源代码目录
│       ├── core/                   # 核心模块
│       ├── trading/                # 交易模块
│       ├── monitors/               # 监控模块
│       ├── database/               # 数据库模块
│       ├── web/                    # Web界面
│       ├── notifications/          # 通知模块
│       └── analysis/               # 分析模块
│
├── 🛠️ 工具脚本
│   └── scripts/                    # 工具脚本目录
│       ├── simple_api_setup.py
│       ├── sync_configs.py
│       ├── system_monitor.py
│       └── verify_binance_api.py
│
├── 🗄️ 数据文件
│   ├── trading_data.db             # 统一交易数据库
│   ├── exchange_data.db            # 价格分析数据库
│   ├── binance_contracts.json      # 币安合约信息
│   ├── token_blacklist.json        # 代币黑名单
│   └── system_state.json           # 系统状态
│
├── 📊 日志和备份
│   ├── logs/                       # 日志目录
│   ├── config_backups/             # 配置备份（最新3个）
│   └── backup_configs/             # 旧配置备份
│
└── 🧪 测试
    └── tests/                      # 测试目录
        ├── unit/                   # 单元测试
        └── integration/            # 集成测试
```

## 🚀 快速开始

### 本地开发
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置文件
cp config.yaml.example config.yaml
# 编辑 config.yaml

# 3. 启动系统
python main.py
```

### Docker部署
```bash
# 1. 生成Telegram会话（可选）
python setup_telegram_session.py

# 2. 一键部署
./deploy.sh          # Linux/macOS
deploy.bat           # Windows
```

## 📋 核心文件说明

### 🔧 配置文件
- **config.yaml**: 主配置文件，包含所有系统配置
- **requirements.txt**: Python依赖包列表

### 🐳 Docker文件
- **Dockerfile**: Docker镜像构建定义
- **docker-compose.yml**: 多容器编排配置
- **deploy.sh/deploy.bat**: 一键部署脚本

### 📱 Telegram文件
- **setup_telegram_session.py**: 推荐使用的会话生成器
- **telegram_session.session**: 统一的Telegram会话文件

### 🗄️ 数据库文件
- **trading_data.db**: 统一数据库（交易所数据、上币事件、交易记录等）
- **exchange_data.db**: 价格分析专用数据库

## 🧹 已清理的文件

以下文件已被清理，不再需要：

### ❌ 重复的脚本
- `generate_goldbot_session.py` → 已合并到 `setup_telegram_session.py`
- `generate_telegram_session.py` → 已合并到 `setup_telegram_session.py`
- `deploy_ubuntu.sh` → 功能已合并到 `deploy.sh`
- `docker-start.sh` → 功能已合并到 `deploy.sh`
- `start_bot.py` → 功能已合并到 `main.py`

### ❌ 重复的文档
- `README-Docker.md` → 已替换为 `DOCKER_DEPLOYMENT.md`

### ❌ 不需要的文件
- `goldbot_session.session` → 已统一使用 `telegram_session.session`
- `get-docker.sh` → Docker官方脚本，不需要在项目中
- `__pycache__/` → Python缓存文件

### ❌ 旧的备份
- 旧的配置备份文件（保留最新3个）

## 🎯 项目优势

### ✅ 简化的结构
- 统一的Telegram会话管理
- 清晰的文件组织
- 减少重复文件

### ✅ 完整的部署方案
- Docker一键部署
- 详细的文档指南
- 多平台支持

### ✅ 模块化设计
- 核心功能分离
- 易于维护和扩展
- 清晰的依赖关系

---

**🎉 项目结构已优化完成！现在更加简洁和易于管理。**

#!/usr/bin/env python3
"""
简化API密钥配置
直接输入API密钥信息更新配置
"""
import yaml
from loguru import logger

def setup_api_keys():
    """设置API密钥"""
    print("🔑 Binance测试网API密钥配置")
    print("=" * 50)
    print()
    print("请从你的测试网页面复制API密钥信息:")
    print("1. API Key: 长字符串 (类似 abcd1234efgh...)")
    print("2. Secret Key: 长字符串 (类似 xyz9876abc...)")
    print()
    
    # 获取API密钥
    api_key = input("请输入API Key: ").strip()
    if not api_key:
        print("❌ API Key不能为空")
        return False
        
    api_secret = input("请输入Secret Key: ").strip()
    if not api_secret:
        print("❌ Secret Key不能为空")
        return False
    
    try:
        # 读取现有配置
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 更新Binance配置
        if 'trading' not in config:
            config['trading'] = {}
        if 'binance' not in config['trading']:
            config['trading']['binance'] = {}
            
        config['trading']['binance']['api_key'] = api_key
        config['trading']['binance']['api_secret'] = api_secret
        config['trading']['binance']['testnet'] = True
        
        # 保存配置
        with open('config.yaml', 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
        
        # 创建.env文件
        env_content = f"""# Binance测试网API配置
BINANCE_API_KEY={api_key}
BINANCE_API_SECRET={api_secret}
BINANCE_TESTNET=true

# 其他配置
TELEGRAM_API_ID=26145597
TELEGRAM_API_HASH=859206f58db62ec957089a7e9ff11d38
TELEGRAM_PHONE=+8613375386798
"""
        
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print()
        print("✅ API密钥配置成功!")
        print(f"  API Key: {api_key[:10]}...{api_key[-10:]}")
        print(f"  Secret: {api_secret[:10]}...{api_secret[-10:]}")
        print("  模式: 测试网")
        print()
        print("🚀 现在可以启动程序了:")
        print("  python start_bot.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置失败: {e}")
        return False

if __name__ == "__main__":
    setup_api_keys()

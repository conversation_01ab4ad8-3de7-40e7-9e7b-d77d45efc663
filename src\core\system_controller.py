"""
系统控制管理器
实现完整的系统启动/停止控制功能，包括优雅关闭、状态管理、错误恢复
"""
import asyncio
import signal
import json
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from enum import Enum
from dataclasses import dataclass, asdict
from loguru import logger

from ..utils import load_config, save_config
from ..models import TradingSignal
from .runtime_config_validator import RuntimeConfigValidator


class SystemStatus(Enum):
    """系统状态"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"
    MAINTENANCE = "maintenance"


@dataclass
class SystemHealth:
    """系统健康状态"""
    status: SystemStatus
    uptime_seconds: float
    cpu_percent: float
    memory_percent: float
    active_monitors: int
    active_positions: int
    total_signals: int
    total_trades: int
    last_signal_time: Optional[datetime]
    last_trade_time: Optional[datetime]
    error_count: int
    api_connections: Dict[str, bool]
    
    
@dataclass
class SystemMetrics:
    """系统指标"""
    start_time: datetime
    signals_processed: int
    trades_executed: int
    errors_occurred: int
    uptime_seconds: float
    performance_stats: Dict[str, Any]


class SystemController:
    """系统控制管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.status = SystemStatus.STOPPED
        self.start_time: Optional[datetime] = None
        self.shutdown_event = asyncio.Event()
        
        # 系统组件
        self.components: Dict[str, Any] = {}
        self.tasks: List[asyncio.Task] = []
        
        # 系统指标
        self.metrics = SystemMetrics(
            start_time=datetime.now(),
            signals_processed=0,
            trades_executed=0,
            errors_occurred=0,
            uptime_seconds=0,
            performance_stats={}
        )
        
        # 健康检查
        self.health_check_interval = 30  # 30秒检查一次
        self.last_health_check = datetime.now()
        
        # 状态持久化文件
        self.state_file = "system_state.json"
        self.metrics_file = "system_metrics.json"
        
        # 错误处理
        self.max_errors = 10
        self.error_window = 300  # 5分钟
        self.recent_errors: List[datetime] = []
        
        # 回调函数
        self.status_callbacks: List[Callable] = []

        # 运行时配置验证器
        self.runtime_validator: Optional[RuntimeConfigValidator] = None

        logger.info("系统控制管理器初始化完成")
        
    def register_component(self, name: str, component: Any):
        """注册系统组件"""
        self.components[name] = component
        logger.info(f"注册系统组件: {name}")
        
    def add_status_callback(self, callback: Callable):
        """添加状态变化回调"""
        self.status_callbacks.append(callback)
        
    async def start_system(self) -> bool:
        """启动系统"""
        try:
            if self.status != SystemStatus.STOPPED:
                logger.warning(f"系统已在运行中，当前状态: {self.status.value}")
                return False
                
            logger.info("🚀 开始启动交易机器人系统...")
            self.status = SystemStatus.STARTING
            await self._notify_status_change()
            
            # 1. 加载系统状态
            await self._load_system_state()
            
            # 2. 初始化运行时配置验证器
            logger.info("🔍 初始化运行时配置验证器...")
            self.runtime_validator = RuntimeConfigValidator(self.config)

            # 3. 初始化系统组件
            await self._initialize_components()

            # 4. 启动监控任务
            await self._start_monitoring_tasks()

            # 4. 启动健康检查
            await self._start_health_check()

            # 6. 启动运行时配置验证
            await self._start_runtime_validation()
            
            # 5. 设置信号处理
            self._setup_signal_handlers()
            
            # 6. 更新系统状态
            self.status = SystemStatus.RUNNING
            self.start_time = datetime.now()
            self.metrics.start_time = self.start_time
            
            await self._notify_status_change()
            await self._save_system_state()
            
            logger.info("✅ 交易机器人系统启动成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统启动失败: {e}")
            self.status = SystemStatus.ERROR
            await self._notify_status_change()
            return False
            
    async def stop_system(self, reason: str = "用户请求") -> bool:
        """停止系统"""
        try:
            if self.status == SystemStatus.STOPPED:
                logger.info("系统已经停止")
                return True
                
            logger.info(f"🛑 开始停止交易机器人系统 - 原因: {reason}")
            self.status = SystemStatus.STOPPING
            await self._notify_status_change()
            
            # 1. 设置关闭事件
            self.shutdown_event.set()
            
            # 2. 停止所有监控任务
            await self._stop_monitoring_tasks()
            
            # 3. 优雅关闭系统组件
            await self._shutdown_components()
            
            # 4. 保存系统状态
            await self._save_system_state()
            await self._save_metrics()
            
            # 5. 更新系统状态
            self.status = SystemStatus.STOPPED
            await self._notify_status_change()
            
            logger.info("✅ 交易机器人系统已安全停止")
            return True

        except Exception as e:
            logger.error(f"❌ 系统停止异常: {e}")
            self.status = SystemStatus.ERROR
            await self._notify_status_change()
            return False

    async def stop_trading_components(self, reason: str = "管理员请求") -> bool:
        """只停止交易相关组件，保持监控和Web界面运行"""
        try:
            logger.info(f"🛑 停止交易组件 - 原因: {reason}")

            # 停止交易相关组件但保持监控运行
            # 这样Web界面仍然可以访问，可以重新启动交易

            # 停止固定策略
            if hasattr(self, 'fixed_strategy') and self.fixed_strategy:
                try:
                    # 如果有停止方法就调用
                    if hasattr(self.fixed_strategy, 'stop'):
                        await self.fixed_strategy.stop()
                    logger.info("✅ 固定策略已停止")
                except Exception as e:
                    logger.error(f"❌ 停止固定策略失败: {e}")

            # 停止跟单交易器
            if hasattr(self, 'copy_trader') and self.copy_trader:
                try:
                    if hasattr(self.copy_trader, 'stop'):
                        await self.copy_trader.stop()
                    logger.info("✅ 跟单交易器已停止")
                except Exception as e:
                    logger.error(f"❌ 停止跟单交易器失败: {e}")

            logger.info("✅ 交易组件已停止，监控和Web界面继续运行")
            return True

        except Exception as e:
            logger.error(f"❌ 停止交易组件失败: {e}")
            return False

    async def restart_system(self, reason: str = "系统重启") -> bool:
        """重启系统"""
        logger.info(f"🔄 重启系统 - 原因: {reason}")
        
        # 先停止系统
        if not await self.stop_system(f"重启前停止 - {reason}"):
            return False
            
        # 等待一段时间
        await asyncio.sleep(2)
        
        # 重新启动
        return await self.start_system()
        
    async def _initialize_components(self):
        """初始化系统组件"""
        try:
            # 这里会在主程序中注入具体的组件
            logger.info("初始化系统组件...")
            
            for name, component in self.components.items():
                if hasattr(component, 'initialize'):
                    await component.initialize()
                logger.debug(f"组件初始化完成: {name}")
                
        except Exception as e:
            logger.error(f"初始化组件异常: {e}")
            raise
            
    async def _start_monitoring_tasks(self):
        """启动监控任务"""
        try:
            logger.info("启动监控任务...")
            
            # 从组件中获取监控任务
            if 'signal_processor' in self.components:
                processor = self.components['signal_processor']

                # 启动清理任务（固定信号处理器）
                if hasattr(processor, 'start_cleanup_task'):
                    cleanup_task = asyncio.create_task(processor.start_cleanup_task())
                    self.tasks.append(cleanup_task)

                # 启动其他任务（如果存在）
                if hasattr(processor, 'start_monitoring_task'):
                    monitoring_task = asyncio.create_task(processor.start_monitoring_task())
                    self.tasks.append(monitoring_task)

                if hasattr(processor, 'start_daily_report_task'):
                    report_task = asyncio.create_task(processor.start_daily_report_task())
                    self.tasks.append(report_task)
                
            # 启动Telegram监控
            if 'telegram_monitor' in self.components:
                telegram_task = asyncio.create_task(
                    self._run_telegram_monitor()
                )
                self.tasks.append(telegram_task)
                
            logger.info(f"监控任务启动完成，共 {len(self.tasks)} 个任务")
            
        except Exception as e:
            logger.error(f"启动监控任务异常: {e}")
            raise
            

    async def _run_telegram_monitor(self):
        """运行Telegram监控"""
        try:
            telegram_monitor = self.components['telegram_monitor']
            signal_processor = self.components.get('signal_processor')

            # 设置飞书通知器
            if signal_processor and hasattr(signal_processor, 'feishu_notifier'):
                telegram_monitor.set_feishu_notifier(signal_processor.feishu_notifier)
                logger.info("✅ Telegram监控器已关联飞书通知")

            async with telegram_monitor:
                # 使用固定策略信号处理器的方法
                signal_callback = None
                if signal_processor:
                    if hasattr(signal_processor, 'process_listing_signal'):
                        signal_callback = signal_processor.process_listing_signal
                    elif hasattr(signal_processor, 'process_signal'):
                        signal_callback = signal_processor.process_signal

                await telegram_monitor.start_monitoring(signal_callback)
        except Exception as e:
            error_msg = str(e)
            if "database disk image is malformed" in error_msg:
                logger.error(f"Telegram监控异常: 会话文件损坏 - {e}")
                logger.info("🔧 建议运行 python fix_telegram_session.py 修复会话文件")
            else:
                logger.error(f"Telegram监控异常: {e}")
            await self._handle_component_error('telegram_monitor', e)
            
    async def _stop_monitoring_tasks(self):
        """停止监控任务"""
        try:
            logger.info("停止监控任务...")
            
            # 停止组件监控
            for name, component in self.components.items():
                if hasattr(component, 'stop_monitoring'):
                    component.stop_monitoring()
                    
            # 取消所有任务
            for task in self.tasks:
                if not task.done():
                    task.cancel()
                    
            # 等待任务完成
            if self.tasks:
                await asyncio.gather(*self.tasks, return_exceptions=True)
                
            self.tasks.clear()
            logger.info("监控任务停止完成")
            
        except Exception as e:
            logger.error(f"停止监控任务异常: {e}")
            
    async def _shutdown_components(self):
        """关闭系统组件"""
        try:
            logger.info("关闭系统组件...")
            
            for name, component in self.components.items():
                try:
                    if hasattr(component, 'shutdown'):
                        await component.shutdown()
                    elif hasattr(component, 'close'):
                        await component.close()
                    logger.debug(f"组件关闭完成: {name}")
                except Exception as e:
                    logger.error(f"关闭组件{name}异常: {e}")
                    
        except Exception as e:
            logger.error(f"关闭组件异常: {e}")
            
    def _setup_signal_handlers(self):
        """设置信号处理"""
        try:
            def signal_handler(signum, frame):
                logger.info(f"收到系统信号 {signum}")
                asyncio.create_task(self.stop_system(f"系统信号 {signum}"))
                
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            
        except Exception as e:
            logger.error(f"设置信号处理异常: {e}")
            
    async def _start_health_check(self):
        """启动健康检查"""
        try:
            health_task = asyncio.create_task(self._health_check_loop())
            self.tasks.append(health_task)
            logger.info("健康检查任务已启动")
        except Exception as e:
            logger.error(f"启动健康检查异常: {e}")

    async def _start_runtime_validation(self):
        """启动运行时配置验证"""
        try:
            if self.runtime_validator:
                # 立即执行一次验证
                logger.info("🔍 执行初始运行时配置验证...")
                validation_result = await self.runtime_validator.validate_all_runtime_configs()

                # 记录验证结果
                overall_status = validation_result.get('overall_status', 'unknown')
                if overall_status == 'error':
                    logger.error("❌ 运行时配置验证发现严重问题")
                    for name, result in validation_result.get('validations', {}).items():
                        if result.get('status') == 'error':
                            logger.error(f"  • {name}: {result.get('message', '未知错误')}")
                elif overall_status == 'warning':
                    logger.warning("⚠️ 运行时配置验证发现警告")
                    for name, result in validation_result.get('validations', {}).items():
                        if result.get('status') == 'warning':
                            logger.warning(f"  • {name}: {result.get('message', '未知警告')}")
                else:
                    logger.info("✅ 运行时配置验证通过")

                # 启动持续验证任务
                validation_task = asyncio.create_task(self.runtime_validator.continuous_validation())
                self.tasks.append(validation_task)
                logger.info("运行时配置验证任务已启动")
            else:
                logger.warning("运行时配置验证器未初始化")
        except Exception as e:
            logger.error(f"启动运行时配置验证异常: {e}")
            
    async def _health_check_loop(self):
        """健康检查循环"""
        while not self.shutdown_event.is_set():
            try:
                await self._perform_health_check()
                await asyncio.sleep(self.health_check_interval)
            except Exception as e:
                logger.error(f"健康检查异常: {e}")
                await asyncio.sleep(self.health_check_interval)

    async def _perform_health_check(self):
        """执行健康检查"""
        try:
            self.last_health_check = datetime.now()

            # 检查系统资源
            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent

            # 检查API连接状态
            api_connections = await self._check_api_connections()

            # 检查组件状态
            component_status = self._check_component_status()

            # 更新指标
            self.metrics.uptime_seconds = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0

            # 检查错误率
            error_rate = self._calculate_error_rate()

            # 判断系统健康状态
            if cpu_percent > 90 or memory_percent > 90:
                logger.warning(f"系统资源使用率过高: CPU {cpu_percent}%, 内存 {memory_percent}%")

            if error_rate > 0.1:  # 错误率超过10%
                logger.warning(f"系统错误率过高: {error_rate:.1%}")

            if not any(api_connections.values()):
                logger.error("所有API连接都失败")
                await self._handle_critical_error("API连接全部失败")

        except Exception as e:
            logger.error(f"健康检查异常: {e}")

    async def _check_api_connections(self) -> Dict[str, bool]:
        """检查API连接状态"""
        connections = {
            'binance': False,
            'upbit': False,
            'telegram': False,
            'feishu': False
        }

        try:
            # 检查币安连接
            if 'signal_processor' in self.components:
                processor = self.components['signal_processor']
                if hasattr(processor, 'trader'):
                    try:
                        account = await processor.trader.get_account_balance()
                        connections['binance'] = account is not None
                    except:
                        connections['binance'] = False



            # 检查Telegram连接
            if 'telegram_monitor' in self.components:
                connections['telegram'] = True  # 简化检查

            # 检查飞书连接
            if 'signal_processor' in self.components:
                processor = self.components['signal_processor']
                if hasattr(processor, 'feishu_notifier'):
                    connections['feishu'] = processor.feishu_notifier.enabled

        except Exception as e:
            logger.error(f"检查API连接异常: {e}")

        return connections

    def _check_component_status(self) -> Dict[str, bool]:
        """检查组件状态"""
        status = {}
        for name, component in self.components.items():
            try:
                # 简化的组件状态检查
                status[name] = component is not None
            except:
                status[name] = False
        return status

    def _calculate_error_rate(self) -> float:
        """计算错误率"""
        try:
            now = datetime.now()
            window_start = now - timedelta(seconds=self.error_window)

            # 清理过期错误
            self.recent_errors = [err_time for err_time in self.recent_errors if err_time > window_start]

            # 计算错误率
            total_operations = max(self.metrics.signals_processed + self.metrics.trades_executed, 1)
            return len(self.recent_errors) / total_operations

        except Exception as e:
            logger.error(f"计算错误率异常: {e}")
            return 0.0

    async def _handle_component_error(self, component_name: str, error: Exception):
        """处理组件错误"""
        try:
            self.recent_errors.append(datetime.now())
            self.metrics.errors_occurred += 1

            logger.error(f"组件{component_name}发生错误: {error}")

            # 检查是否需要重启组件
            error_count = len([err for err in self.recent_errors if (datetime.now() - err).total_seconds() < 300])

            if error_count >= 5:  # 5分钟内5个错误
                logger.warning(f"组件{component_name}错误频繁，考虑重启")
                # 这里可以添加组件重启逻辑

        except Exception as e:
            logger.error(f"处理组件错误异常: {e}")

    async def _handle_critical_error(self, reason: str):
        """处理严重错误"""
        try:
            logger.critical(f"系统严重错误: {reason}")

            # 发送紧急通知
            if 'signal_processor' in self.components:
                processor = self.components['signal_processor']
                if hasattr(processor, 'feishu_notifier'):
                    await processor.feishu_notifier.send_statistics_notification({
                        'total_trades': 0,
                        'successful_trades': 0,
                        'failed_trades': 0,
                        'total_profit': 0,
                        'win_rate': 0,
                        'error_message': f"系统严重错误: {reason}"
                    })

            # 考虑是否需要停止系统
            if len(self.recent_errors) >= self.max_errors:
                await self.stop_system(f"错误过多: {reason}")

        except Exception as e:
            logger.error(f"处理严重错误异常: {e}")

    async def _notify_status_change(self):
        """通知状态变化"""
        try:
            for callback in self.status_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(self.status)
                    else:
                        callback(self.status)
                except Exception as e:
                    logger.error(f"状态回调异常: {e}")

        except Exception as e:
            logger.error(f"通知状态变化异常: {e}")

    async def _load_system_state(self):
        """加载系统状态"""
        try:
            with open(self.state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)

            # 恢复指标数据
            if 'metrics' in state_data:
                metrics_data = state_data['metrics']
                self.metrics.signals_processed = metrics_data.get('signals_processed', 0)
                self.metrics.trades_executed = metrics_data.get('trades_executed', 0)
                self.metrics.errors_occurred = metrics_data.get('errors_occurred', 0)

            logger.info("系统状态加载完成")

        except FileNotFoundError:
            logger.info("未找到系统状态文件，使用默认状态")
        except Exception as e:
            logger.error(f"加载系统状态异常: {e}")

    async def _save_system_state(self):
        """保存系统状态"""
        try:
            state_data = {
                'status': self.status.value,
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'metrics': asdict(self.metrics),
                'last_save_time': datetime.now().isoformat()
            }

            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2, default=str)

            logger.debug("系统状态保存完成")

        except Exception as e:
            logger.error(f"保存系统状态异常: {e}")

    async def _save_metrics(self):
        """保存系统指标"""
        try:
            metrics_data = asdict(self.metrics)
            metrics_data['save_time'] = datetime.now().isoformat()

            with open(self.metrics_file, 'w', encoding='utf-8') as f:
                json.dump(metrics_data, f, ensure_ascii=False, indent=2, default=str)

        except Exception as e:
            logger.error(f"保存系统指标异常: {e}")

    def get_system_health(self) -> SystemHealth:
        """获取系统健康状态"""
        try:
            # 获取系统资源信息
            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent

            # 获取组件状态
            active_monitors = len([name for name in self.components.keys() if 'monitor' in name])

            # 获取持仓信息
            active_positions = 0
            if 'signal_processor' in self.components:
                processor = self.components['signal_processor']
                if hasattr(processor, 'position_manager'):
                    active_positions = len(processor.position_manager.active_positions)

            return SystemHealth(
                status=self.status,
                uptime_seconds=self.metrics.uptime_seconds,
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                active_monitors=active_monitors,
                active_positions=active_positions,
                total_signals=self.metrics.signals_processed,
                total_trades=self.metrics.trades_executed,
                last_signal_time=None,  # 需要从组件获取
                last_trade_time=None,   # 需要从组件获取
                error_count=len(self.recent_errors),
                api_connections={}  # 需要异步获取
            )

        except Exception as e:
            logger.error(f"获取系统健康状态异常: {e}")
            return SystemHealth(
                status=SystemStatus.ERROR,
                uptime_seconds=0,
                cpu_percent=0,
                memory_percent=0,
                active_monitors=0,
                active_positions=0,
                total_signals=0,
                total_trades=0,
                last_signal_time=None,
                last_trade_time=None,
                error_count=0,
                api_connections={}
            )

    def update_metrics(self, metric_type: str, value: int = 1):
        """更新系统指标"""
        try:
            if metric_type == 'signal_processed':
                self.metrics.signals_processed += value
            elif metric_type == 'trade_executed':
                self.metrics.trades_executed += value
            elif metric_type == 'error_occurred':
                self.metrics.errors_occurred += value
                self.recent_errors.append(datetime.now())

        except Exception as e:
            logger.error(f"更新系统指标异常: {e}")

    async def emergency_shutdown(self, reason: str):
        """紧急关闭"""
        logger.critical(f"🚨 紧急关闭系统: {reason}")
        self.status = SystemStatus.ERROR
        await self.stop_system(f"紧急关闭 - {reason}")

    def is_running(self) -> bool:
        """检查系统是否运行中"""
        return self.status == SystemStatus.RUNNING

    async def get_runtime_validation_results(self) -> Optional[Dict[str, Any]]:
        """获取运行时配置验证结果"""
        try:
            if self.runtime_validator:
                return self.runtime_validator.get_last_validation_results()
            return None
        except Exception as e:
            logger.error(f"获取运行时验证结果异常: {e}")
            return None

    async def validate_runtime_config(self, config_type: str = None) -> Dict[str, Any]:
        """手动触发运行时配置验证"""
        try:
            if not self.runtime_validator:
                return {
                    'status': 'error',
                    'message': '运行时配置验证器未初始化',
                    'details': {}
                }

            if config_type:
                # 验证特定类型的配置
                return await self.runtime_validator.validate_specific_config(config_type)
            else:
                # 验证所有配置
                return await self.runtime_validator.validate_all_runtime_configs()

        except Exception as e:
            logger.error(f"手动验证运行时配置异常: {e}")
            return {
                'status': 'error',
                'message': f'验证异常: {str(e)}',
                'details': {}
            }

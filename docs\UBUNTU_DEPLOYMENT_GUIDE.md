# 🚀 Ubuntu服务器部署完整教程

## 📋 目录
1. [服务器准备](#1-服务器准备)
2. [连接服务器](#2-连接服务器)
3. [系统环境配置](#3-系统环境配置)
4. [项目部署](#4-项目部署)
5. [配置文件设置](#5-配置文件设置)
6. [启动服务](#6-启动服务)
7. [日志查看](#7-日志查看)
8. [系统监控](#8-系统监控)
9. [配置修改](#9-配置修改)
10. [一键部署脚本](#10-一键部署脚本)
11. [常见问题](#11-常见问题)

---

## 1. 服务器准备

### 1.1 购买Ubuntu服务器
**推荐配置**:
- **系统**: Ubuntu 20.04 LTS 或 22.04 LTS
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **硬盘**: 40GB以上
- **带宽**: 5Mbps以上

**推荐服务商**:
- 阿里云ECS
- 腾讯云CVM
- 华为云ECS
- Vultr
- DigitalOcean

### 1.2 获取服务器信息
购买后你会得到:
```
服务器IP地址: 例如 123.456.789.123
用户名: root 或 ubuntu
密码: 你设置的密码
SSH端口: 通常是22
```

---

## 2. 连接服务器

### 2.1 Windows用户连接方法

#### 方法1: 使用PuTTY (推荐新手)
1. **下载PuTTY**: https://www.putty.org/
2. **安装并打开PuTTY**
3. **配置连接**:
   ```
   Host Name: 你的服务器IP地址
   Port: 22
   Connection type: SSH
   ```
4. **点击Open连接**
5. **输入用户名**: `root` 或 `ubuntu`
6. **输入密码**: 你的服务器密码

#### 方法2: 使用Windows Terminal
1. **打开Windows Terminal或PowerShell**
2. **输入连接命令**:
   ```bash
   ssh root@你的服务器IP地址
   # 例如: ssh root@123.456.789.123
   ```
3. **输入密码**

### 2.2 连接成功标志
看到类似这样的提示说明连接成功:
```bash
root@ubuntu-server:~#
```

---

## 3. 系统环境配置

### 3.1 更新系统包
**作用**: 确保系统是最新的，避免安全漏洞
```bash
# 更新包列表
sudo apt update

# 升级已安装的包
sudo apt upgrade -y
```

### 3.2 安装基础工具
**作用**: 安装后续需要用到的基础工具
```bash
# 安装基础工具包
sudo apt install -y curl wget git vim htop screen unzip

# 解释每个工具的作用:
# curl: 下载文件和API请求
# wget: 下载文件
# git: 代码版本控制
# vim: 文本编辑器
# htop: 系统监控工具
# screen: 后台运行程序
# unzip: 解压缩工具
```

### 3.3 安装Python 3.9+
**作用**: 我们的交易机器人需要Python环境
```bash
# 检查Python版本
python3 --version

# 如果版本低于3.9，安装新版本
sudo apt install -y python3.9 python3.9-venv python3.9-dev python3-pip

# 设置Python3.9为默认python3
sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.9 1
```

### 3.4 安装Node.js (可选，用于某些功能)
**作用**: 某些监控功能可能需要Node.js
```bash
# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

---

## 4. 项目部署

### 4.1 创建项目目录
**作用**: 为项目创建专门的目录
```bash
# 创建项目目录
mkdir -p /opt/goldbot
cd /opt/goldbot

# 解释:
# /opt/ 是Linux系统中存放第三方软件的标准目录
# goldbot 是我们的项目名称
```

### 4.2 上传项目文件

#### 方法1: 使用Git (推荐)
```bash
# 如果你的代码在Git仓库中
git clone https://github.com/你的用户名/goldbot.git .

# 如果没有Git仓库，跳到方法2
```

#### 方法2: 使用SCP上传 (Windows用户)
在你的**本地电脑**上打开PowerShell:
```bash
# 压缩本地项目
# 在goldbot目录下执行
tar -czf goldbot.tar.gz *

# 上传到服务器
scp goldbot.tar.gz root@你的服务器IP:/opt/goldbot/

# 然后在服务器上解压
cd /opt/goldbot
tar -xzf goldbot.tar.gz
rm goldbot.tar.gz
```

#### 方法3: 使用WinSCP (图形界面，推荐新手)
1. **下载WinSCP**: https://winscp.net/
2. **配置连接信息**:
   ```
   文件协议: SFTP
   主机名: 你的服务器IP
   端口号: 22
   用户名: root
   密码: 你的密码
   ```
3. **连接后直接拖拽文件到 `/opt/goldbot/` 目录**

### 4.3 设置项目权限
**作用**: 确保项目文件有正确的权限
```bash
# 设置目录所有者
sudo chown -R root:root /opt/goldbot

# 设置文件权限
sudo chmod -R 755 /opt/goldbot

# 设置脚本执行权限
sudo chmod +x /opt/goldbot/scripts/*.sh
sudo chmod +x /opt/goldbot/start_bot.py
```

---

## 5. 配置文件设置

### 5.1 创建Python虚拟环境
**作用**: 隔离项目依赖，避免与系统Python冲突
```bash
cd /opt/goldbot

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 你会看到命令提示符变成这样:
# (venv) root@ubuntu-server:/opt/goldbot#
```

### 5.2 安装项目依赖
**作用**: 安装项目需要的所有Python包
```bash
# 确保在虚拟环境中
source venv/bin/activate

# 升级pip
pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt

# 这个过程可能需要5-10分钟，请耐心等待
```

### 5.3 配置环境变量
**作用**: 设置项目运行需要的环境变量
```bash
# 编辑.env文件
vim .env

# 如果不会用vim，可以用nano (更简单)
nano .env
```

**vim编辑器使用方法**:
```bash
# 进入编辑模式: 按 i
# 编辑完成后: 按 Esc
# 保存并退出: 输入 :wq 然后按回车
# 不保存退出: 输入 :q! 然后按回车
```

**nano编辑器使用方法**:
```bash
# 直接编辑，编辑完成后:
# 保存: Ctrl + O，然后按回车
# 退出: Ctrl + X
```

### 5.4 配置config.yaml
```bash
# 编辑主配置文件
nano config.yaml

# 重点检查这些配置:
# 1. Binance API密钥是否正确
# 2. Telegram手机号是否填写
# 3. 飞书Webhook是否配置
# 4. 交易参数是否合适
```

---

## 6. 启动服务

### 6.1 测试启动
**作用**: 先测试程序是否能正常启动
```bash
cd /opt/goldbot

# 激活虚拟环境
source venv/bin/activate

# 测试启动 (前台运行，可以看到日志)
python start_bot.py

# 如果看到类似这样的输出说明启动成功:
# 2024-07-31 10:00:00 | INFO | 系统启动成功
# 2024-07-31 10:00:01 | INFO | Web服务器启动在端口 8080
```

**如果启动失败**:
1. 检查错误信息
2. 确认配置文件是否正确
3. 确认依赖是否安装完整

### 6.2 后台运行 (使用screen)
**作用**: 让程序在后台持续运行，即使断开SSH连接也不会停止
```bash
# 创建一个名为goldbot的screen会话
screen -S goldbot

# 在screen中启动程序
cd /opt/goldbot
source venv/bin/activate
python start_bot.py

# 程序启动后，按 Ctrl+A 然后按 D 来分离screen
# 这样程序就在后台运行了
```

**screen常用命令**:
```bash
# 查看所有screen会话
screen -ls

# 重新连接到goldbot会话
screen -r goldbot

# 在screen中:
# Ctrl+A + D: 分离会话 (程序继续运行)
# Ctrl+A + K: 杀死当前会话
# exit: 退出会话
```

### 6.3 使用systemd服务 (推荐生产环境)
**作用**: 让系统自动管理程序，开机自启动，崩溃自动重启

创建服务文件:
```bash
sudo nano /etc/systemd/system/goldbot.service
```

服务文件内容:
```ini
[Unit]
Description=GoldBot Trading System
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/goldbot
Environment=PATH=/opt/goldbot/venv/bin
ExecStart=/opt/goldbot/venv/bin/python /opt/goldbot/start_bot.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务:
```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用服务 (开机自启动)
sudo systemctl enable goldbot

# 启动服务
sudo systemctl start goldbot

# 查看服务状态
sudo systemctl status goldbot
```

---

## 7. 日志查看

### 7.1 实时查看日志
**作用**: 监控程序运行状态，及时发现问题
```bash
# 方法1: 查看程序日志文件
tail -f /opt/goldbot/trading_bot.log

# 方法2: 如果使用systemd服务
sudo journalctl -u goldbot -f

# 方法3: 如果使用screen
screen -r goldbot  # 重新连接到screen会话
```

### 7.2 查看历史日志
```bash
# 查看最近100行日志
tail -n 100 /opt/goldbot/trading_bot.log

# 查看今天的日志
sudo journalctl -u goldbot --since today

# 查看最近1小时的日志
sudo journalctl -u goldbot --since "1 hour ago"

# 搜索包含"ERROR"的日志
grep "ERROR" /opt/goldbot/trading_bot.log
```

### 7.3 日志文件位置
```bash
# 主要日志文件
/opt/goldbot/trading_bot.log          # 程序主日志
/opt/goldbot/system_state.json        # 系统状态
/opt/goldbot/trading_stats.json       # 交易统计
```

---

## 8. 系统监控

### 8.1 查看系统资源使用
```bash
# 查看CPU和内存使用情况 (实时)
htop

# 查看磁盘使用情况
df -h

# 查看内存使用情况
free -h

# 查看网络连接
netstat -tulpn | grep :8080  # 查看8080端口是否在监听
```

### 8.2 查看程序状态
```bash
# 查看goldbot服务状态
sudo systemctl status goldbot

# 查看程序进程
ps aux | grep python

# 查看端口占用
lsof -i :8080
```

### 8.3 Web界面访问
在浏览器中访问:
```
http://你的服务器IP:8080
```

如果无法访问，检查防火墙:
```bash
# Ubuntu防火墙设置
sudo ufw allow 8080
sudo ufw enable

# 查看防火墙状态
sudo ufw status
```

---

## 9. 配置修改

### 9.1 修改配置文件
```bash
# 停止服务
sudo systemctl stop goldbot

# 修改配置
nano /opt/goldbot/config.yaml

# 修改环境变量
nano /opt/goldbot/.env

# 重新启动服务
sudo systemctl start goldbot

# 查看启动状态
sudo systemctl status goldbot
```

### 9.2 更新代码
```bash
# 停止服务
sudo systemctl stop goldbot

# 备份当前配置
cp /opt/goldbot/config.yaml /opt/goldbot/config.yaml.backup
cp /opt/goldbot/.env /opt/goldbot/.env.backup

# 更新代码 (如果使用Git)
cd /opt/goldbot
git pull

# 或者重新上传文件 (使用WinSCP)

# 恢复配置文件
cp /opt/goldbot/config.yaml.backup /opt/goldbot/config.yaml
cp /opt/goldbot/.env.backup /opt/goldbot/.env

# 更新依赖 (如果requirements.txt有变化)
source venv/bin/activate
pip install -r requirements.txt

# 重新启动服务
sudo systemctl start goldbot
```

### 9.3 修改交易参数
```bash
# 编辑配置文件
nano /opt/goldbot/config.yaml

# 找到trading部分，修改参数:
# amount: 开单金额
# leverage: 杠杆倍数
# stop_loss_percent: 止损百分比
# max_positions: 最大持仓数

# 保存后重启服务
sudo systemctl restart goldbot
```

---

## 10. 一键部署脚本

创建自动部署脚本，让部署过程更简单。

### 10.1 创建部署脚本
```bash
nano /opt/deploy_goldbot.sh
```

脚本内容:
```bash
#!/bin/bash
# GoldBot一键部署脚本

set -e  # 遇到错误立即退出

echo "🚀 开始部署GoldBot交易系统..."

# 1. 更新系统
echo "📦 更新系统包..."
apt update && apt upgrade -y

# 2. 安装基础工具
echo "🔧 安装基础工具..."
apt install -y curl wget git vim htop screen unzip python3.9 python3.9-venv python3.9-dev python3-pip

# 3. 创建项目目录
echo "📁 创建项目目录..."
mkdir -p /opt/goldbot
cd /opt/goldbot

# 4. 创建虚拟环境
echo "🐍 创建Python虚拟环境..."
python3.9 -m venv venv
source venv/bin/activate

# 5. 安装依赖
echo "📚 安装项目依赖..."
pip install --upgrade pip
pip install -r requirements.txt

# 6. 设置权限
echo "🔐 设置文件权限..."
chown -R root:root /opt/goldbot
chmod -R 755 /opt/goldbot
chmod +x /opt/goldbot/start_bot.py

# 7. 创建systemd服务
echo "⚙️ 创建系统服务..."
cat > /etc/systemd/system/goldbot.service << EOF
[Unit]
Description=GoldBot Trading System
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/goldbot
Environment=PATH=/opt/goldbot/venv/bin
ExecStart=/opt/goldbot/venv/bin/python /opt/goldbot/start_bot.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 8. 启用服务
echo "🔄 启用系统服务..."
systemctl daemon-reload
systemctl enable goldbot

# 9. 配置防火墙
echo "🔥 配置防火墙..."
ufw allow 8080
ufw --force enable

echo "✅ 部署完成！"
echo ""
echo "📋 下一步操作:"
echo "1. 编辑配置文件: nano /opt/goldbot/config.yaml"
echo "2. 编辑环境变量: nano /opt/goldbot/.env"
echo "3. 启动服务: systemctl start goldbot"
echo "4. 查看状态: systemctl status goldbot"
echo "5. 查看日志: journalctl -u goldbot -f"
echo "6. 访问Web界面: http://$(curl -s ifconfig.me):8080"
```

### 10.2 使用一键部署脚本
```bash
# 1. 上传项目文件到服务器 /opt/goldbot/ 目录

# 2. 给脚本执行权限
chmod +x /opt/deploy_goldbot.sh

# 3. 运行部署脚本
./opt/deploy_goldbot.sh

# 4. 根据提示完成配置
```

---

## 11. 常见问题

### 11.1 连接问题

**问题**: SSH连接失败
```bash
# 解决方案:
# 1. 检查IP地址是否正确
# 2. 检查端口是否正确 (通常是22)
# 3. 检查用户名和密码
# 4. 检查服务器防火墙设置
```

**问题**: Web界面无法访问
```bash
# 解决方案:
# 1. 检查程序是否启动
sudo systemctl status goldbot

# 2. 检查端口是否监听
netstat -tulpn | grep :8080

# 3. 检查防火墙
sudo ufw status
sudo ufw allow 8080

# 4. 检查云服务器安全组设置 (在云服务商控制台)
```

### 11.2 程序问题

**问题**: 程序启动失败
```bash
# 查看详细错误信息
sudo journalctl -u goldbot -n 50

# 常见原因:
# 1. 配置文件格式错误
# 2. API密钥配置错误
# 3. 依赖包安装不完整
# 4. Python版本不兼容
```

**问题**: Telegram连接失败
```bash
# 解决方案:
# 1. 检查手机号格式: +8613375386798
# 2. 确保网络可以访问Telegram
# 3. 首次运行需要输入验证码
# 4. 检查API ID和Hash是否正确
```

### 11.3 性能问题

**问题**: 内存使用过高
```bash
# 查看内存使用
free -h
htop

# 解决方案:
# 1. 增加服务器内存
# 2. 优化配置参数
# 3. 减少监控频率
```

**问题**: CPU使用过高
```bash
# 查看CPU使用
htop
top

# 解决方案:
# 1. 检查是否有死循环
# 2. 优化监控间隔
# 3. 升级服务器配置
```

---

## 12. 安全建议

### 12.1 服务器安全
```bash
# 1. 修改SSH端口 (可选)
nano /etc/ssh/sshd_config
# 修改 Port 22 为其他端口，如 Port 2222
systemctl restart ssh

# 2. 禁用root密码登录，使用密钥登录 (推荐)
# 3. 定期更新系统
apt update && apt upgrade

# 4. 配置fail2ban防止暴力破解
apt install fail2ban
```

### 12.2 API安全
```bash
# 1. 定期更换API密钥
# 2. 设置API权限最小化
# 3. 监控API使用情况
# 4. 备份重要配置文件
```

---

## 13. 备份和恢复

### 13.1 备份重要文件
```bash
# 创建备份脚本
cat > /opt/backup_goldbot.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/goldbot_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

# 备份配置文件
cp /opt/goldbot/config.yaml $BACKUP_DIR/
cp /opt/goldbot/.env $BACKUP_DIR/

# 备份数据文件
cp /opt/goldbot/*.json $BACKUP_DIR/
cp /opt/goldbot/*.db $BACKUP_DIR/

# 备份日志
cp /opt/goldbot/*.log $BACKUP_DIR/

echo "备份完成: $BACKUP_DIR"
EOF

chmod +x /opt/backup_goldbot.sh

# 运行备份
/opt/backup_goldbot.sh
```

### 13.2 定期自动备份
```bash
# 添加到定时任务
crontab -e

# 添加这一行 (每天凌晨2点备份)
0 2 * * * /opt/backup_goldbot.sh
```

---

## 14. 监控和告警

### 14.1 系统监控脚本
```bash
cat > /opt/monitor_goldbot.sh << 'EOF'
#!/bin/bash
# 检查服务状态
if ! systemctl is-active --quiet goldbot; then
    echo "⚠️ GoldBot服务已停止，正在重启..."
    systemctl start goldbot
fi

# 检查内存使用
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.1f"), $3/$2 * 100.0}')
if (( $(echo "$MEMORY_USAGE > 90" | bc -l) )); then
    echo "⚠️ 内存使用率过高: ${MEMORY_USAGE}%"
fi

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 90 ]; then
    echo "⚠️ 磁盘空间不足: ${DISK_USAGE}%"
fi
EOF

chmod +x /opt/monitor_goldbot.sh

# 添加到定时任务 (每5分钟检查一次)
crontab -e
# 添加: */5 * * * * /opt/monitor_goldbot.sh
```

---

## 15. 总结

### 15.1 部署检查清单
- [ ] 服务器购买并获得访问权限
- [ ] SSH连接成功
- [ ] 系统环境配置完成
- [ ] 项目文件上传完成
- [ ] Python环境和依赖安装完成
- [ ] 配置文件设置完成
- [ ] 服务启动成功
- [ ] Web界面可以访问
- [ ] 日志查看正常
- [ ] 备份脚本配置完成

### 15.2 日常维护命令
```bash
# 查看服务状态
sudo systemctl status goldbot

# 重启服务
sudo systemctl restart goldbot

# 查看实时日志
sudo journalctl -u goldbot -f

# 查看系统资源
htop

# 备份数据
/opt/backup_goldbot.sh

# 更新系统
sudo apt update && sudo apt upgrade
```

### 15.3 紧急处理
如果系统出现问题:
1. **立即停止交易**: `sudo systemctl stop goldbot`
2. **查看错误日志**: `sudo journalctl -u goldbot -n 100`
3. **检查配置文件**: `nano /opt/goldbot/config.yaml`
4. **联系技术支持**: 提供错误日志和配置信息

---

**🎉 恭喜！你已经完成了Ubuntu服务器的完整部署！**

现在你的交易机器人已经在服务器上运行，可以通过Web界面监控系统状态，通过SSH查看日志和修改配置。记住定期备份重要数据，监控系统运行状态。

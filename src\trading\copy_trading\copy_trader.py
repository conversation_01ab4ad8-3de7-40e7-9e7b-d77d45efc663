"""
跟单交易器实现
"""
import asyncio
from typing import Dict, Any, Optional
from loguru import logger
from datetime import datetime

from ..interfaces.trader_interface import TraderInterface
from ...models import TradingSignal
from .copy_client import BinanceCopyTradingClient


class CopyTrader(TraderInterface):
    """跟单交易器 - 实现跟单API交易"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.copy_config = config.get('copy_trading', {})
        self.trading_config = config.get('trading', {})
        
        # 交易参数
        self.fixed_amount = self.copy_config.get('amount', 100.0)
        self.leverage = self.copy_config.get('leverage', 10)
        
        # 跟单参数
        self.leader_config = self.copy_config.get('leader_config', {})
        self.min_follow_amount = self.leader_config.get('min_follow_amount', 10)
        self.max_follow_amount = self.leader_config.get('max_follow_amount', 1000)
        self.max_followers = self.leader_config.get('max_followers', 100)
        self.commission_rate = self.leader_config.get('commission_rate', 0.1)
        
        # 初始化客户端
        self.client = self._init_client()
        
        logger.info(f"跟单交易器初始化完成 - 金额: {self.fixed_amount}, 杠杆: {self.leverage}x")
    
    def _init_client(self) -> BinanceCopyTradingClient:
        """初始化跟单客户端"""
        return BinanceCopyTradingClient(
            api_key=self.copy_config.get('api_key'),
            api_secret=self.copy_config.get('api_secret'),
            testnet=self.copy_config.get('testnet', False)
        )
    
    async def execute_trade(self, signal: TradingSignal, **kwargs) -> Dict[str, Any]:
        """
        执行跟单交易
        
        Args:
            signal: 交易信号
            **kwargs: 额外参数
            
        Returns:
            Dict: 交易结果
        """
        try:
            symbol = f"{signal.symbol}USDT"
            
            # 1. 获取当前价格
            current_price = await self._get_current_price(symbol)
            if not current_price:
                return {
                    'success': False,
                    'message': f'无法获取 {symbol} 当前价格'
                }
            
            # 2. 计算交易数量
            quantity = (self.fixed_amount * self.leverage) / current_price
            
            # 3. 设置杠杆和保证金模式
            await self._setup_trading_params(symbol)
            
            # 4. 执行跟单交易
            order_result = await self._create_copy_trading_order(
                symbol=symbol,
                side='BUY',
                quantity=quantity,
                current_price=current_price
            )
            
            if order_result['success']:
                logger.info(f"✅ 跟单交易成功: {symbol}, 价格: {current_price}, 数量: {quantity}")
                return {
                    'success': True,
                    'order_id': order_result['order_id'],
                    'price': current_price,
                    'quantity': quantity,
                    'mode': 'copy_trading',
                    'message': '跟单交易执行成功'
                }
            else:
                return {
                    'success': False,
                    'message': f"跟单交易失败: {order_result['message']}"
                }
                
        except Exception as e:
            logger.error(f"❌ 跟单交易异常: {e}")
            return {
                'success': False,
                'message': f'跟单交易异常: {str(e)}'
            }
    
    async def close_position(self, symbol: str, percentage: float = 100.0) -> Dict[str, Any]:
        """
        平仓
        
        Args:
            symbol: 交易对符号
            percentage: 平仓百分比
            
        Returns:
            Dict: 平仓结果
        """
        try:
            if not symbol.endswith('USDT'):
                symbol = f"{symbol}USDT"
            
            # 1. 获取当前仓位
            position = await self.get_position_info(symbol)
            if not position or position.get('size', 0) == 0:
                return {
                    'success': False,
                    'message': f'{symbol} 无持仓'
                }
            
            # 2. 计算平仓数量
            current_size = abs(float(position['size']))
            close_quantity = current_size * (percentage / 100.0)
            
            # 3. 执行平仓
            close_result = await self._create_copy_trading_order(
                symbol=symbol,
                side='SELL',
                quantity=close_quantity,
                current_price=float(position['markPrice'])
            )
            
            if close_result['success']:
                logger.info(f"✅ 跟单平仓成功: {symbol}, 比例: {percentage}%")
                return {
                    'success': True,
                    'order_id': close_result['order_id'],
                    'price': float(position['markPrice']),
                    'quantity': close_quantity,
                    'message': f'平仓成功 {percentage}%'
                }
            else:
                return {
                    'success': False,
                    'message': f"平仓失败: {close_result['message']}"
                }
                
        except Exception as e:
            logger.error(f"❌ 跟单平仓异常: {e}")
            return {
                'success': False,
                'message': f'平仓异常: {str(e)}'
            }
    
    async def get_position_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取仓位信息"""
        try:
            if not symbol.endswith('USDT'):
                symbol = f"{symbol}USDT"
            
            return await self.client.get_position_info(symbol)
            
        except Exception as e:
            logger.error(f"❌ 获取仓位信息异常: {e}")
            return None
    
    async def get_account_balance(self) -> Dict[str, Any]:
        """获取账户余额"""
        try:
            return await self.client.get_account_balance()
        except Exception as e:
            logger.error(f"❌ 获取账户余额异常: {e}")
            return {}
    
    def get_trader_type(self) -> str:
        """获取交易器类型"""
        return 'copy_trading'
    
    async def _get_current_price(self, symbol: str) -> Optional[float]:
        """获取当前价格"""
        try:
            ticker = await self.client.get_symbol_ticker(symbol)
            return float(ticker['price']) if ticker else None
        except Exception as e:
            logger.error(f"❌ 获取价格失败: {e}")
            return None
    
    async def _setup_trading_params(self, symbol: str):
        """设置交易参数"""
        try:
            # 设置杠杆
            await self.client.change_leverage(symbol, self.leverage)
            
            # 设置逐仓模式
            await self.client.change_margin_type(symbol, 'ISOLATED')
            
        except Exception as e:
            logger.debug(f"设置交易参数异常 (可能已设置): {e}")
    
    async def _create_copy_trading_order(self, symbol: str, side: str, quantity: float, 
                                       current_price: float) -> Dict[str, Any]:
        """
        创建跟单交易订单
        
        Args:
            symbol: 交易对
            side: 交易方向 ('BUY' 或 'SELL')
            quantity: 交易数量
            current_price: 当前价格
            
        Returns:
            Dict: 订单结果
        """
        try:
            # 创建跟单订单
            order = await self.client.create_copy_trading_order(
                symbol=symbol,
                side=side,
                type='MARKET',
                quantity=quantity,
                copy_trading=True,
                leader_config={
                    'minFollowAmount': self.min_follow_amount,
                    'maxFollowAmount': self.max_follow_amount,
                    'maxFollowers': self.max_followers,
                    'commissionRate': self.commission_rate
                }
            )
            
            if order and order.get('orderId'):
                return {
                    'success': True,
                    'order_id': order['orderId'],
                    'message': '跟单订单创建成功'
                }
            else:
                return {
                    'success': False,
                    'message': '跟单订单创建失败'
                }
                
        except Exception as e:
            logger.error(f"❌ 创建跟单订单异常: {e}")
            return {
                'success': False,
                'message': f'创建跟单订单异常: {str(e)}'
            }

#!/usr/bin/env python3
"""
Docker部署前检查脚本
确保所有必要文件和配置都准备就绪
"""
import os
import yaml
import glob
from loguru import logger

def check_required_files():
    """检查必需文件"""
    required_files = [
        'config.yaml',
        'docker-compose.yml',
        'Dockerfile',
        'requirements.txt',
        'main.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            logger.info(f"✅ {file}")
    
    if missing_files:
        logger.error(f"❌ 缺少必需文件: {missing_files}")
        return False
    
    return True

def check_telegram_session():
    """检查Telegram会话文件"""
    session_files = glob.glob("telegram_session*")
    
    if not session_files:
        logger.warning("⚠️ 未发现Telegram会话文件")
        logger.info("📝 Docker部署后首次运行需要Telegram认证")
        logger.info("💡 建议：在主机上先运行一次程序完成认证，再部署Docker")
        return True  # 不是致命错误
    
    for session_file in session_files:
        size = os.path.getsize(session_file)
        logger.info(f"✅ {session_file} (大小: {size} bytes)")
    
    logger.info("📱 Telegram会话文件已准备就绪")
    return True

def check_config():
    """检查配置文件"""
    try:
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查关键配置
        web_config = config.get('web', {})
        port = web_config.get('port', 8081)
        
        logger.info(f"✅ Web端口配置: {port}")
        
        # 检查Telegram配置
        telegram_config = config.get('monitoring', {}).get('telegram', {})
        if telegram_config.get('api_id') and telegram_config.get('api_hash'):
            logger.info("✅ Telegram API配置完整")
        else:
            logger.warning("⚠️ Telegram API配置不完整")
        
        # 检查交易配置
        trading_config = config.get('trading', {}).get('binance', {})
        if trading_config.get('api_key') and trading_config.get('api_secret'):
            logger.info("✅ Binance API配置完整")
        else:
            logger.warning("⚠️ Binance API配置不完整")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置文件检查失败: {e}")
        return False

def check_docker_compose():
    """检查Docker Compose配置"""
    try:
        with open('docker-compose.yml', 'r', encoding='utf-8') as f:
            compose_config = yaml.safe_load(f)
        
        services = compose_config.get('services', {})
        if 'goldbot' not in services:
            logger.error("❌ docker-compose.yml中缺少goldbot服务")
            return False
        
        goldbot_service = services['goldbot']
        
        # 检查端口映射
        ports = goldbot_service.get('ports', [])
        logger.info(f"✅ Docker端口映射: {ports}")
        
        # 检查卷挂载
        volumes = goldbot_service.get('volumes', [])
        session_volume_found = False
        for volume in volumes:
            if 'telegram_session.session' in volume:
                session_volume_found = True
                logger.info(f"✅ Telegram会话文件挂载: {volume}")
                break
        
        if not session_volume_found:
            logger.warning("⚠️ 未配置Telegram会话文件挂载")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Docker Compose配置检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🐳 Docker部署前检查")
    print("=" * 50)
    
    checks = [
        ("必需文件", check_required_files),
        ("配置文件", check_config),
        ("Docker Compose", check_docker_compose),
        ("Telegram会话", check_telegram_session),
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        logger.info(f"\n🔍 检查{check_name}...")
        if not check_func():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✅ 所有检查通过，可以部署Docker！")
        print("\n🚀 部署命令:")
        print("docker-compose up -d")
        print("\n📊 查看日志:")
        print("docker-compose logs -f goldbot")
        print("\n🌐 访问地址:")
        print("http://localhost:8080")
    else:
        print("❌ 部分检查未通过，请修复问题后重试")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())

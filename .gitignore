# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Logs
*.log
logs/

# Database
*.db
*.sqlite
*.sqlite3

# Configuration (sensitive)
config_local.yaml
.env
*.env

# Session files
telegram_session.session*
goldbot_session.session*

# Temporary files
*.tmp
*.temp
*.pid

# System files
.DS_Store
Thumbs.db

# Backup files
backup_*/
config_backups/

# Trading data
trading_stats.json
system_state.json

# Docker
.dockerignore

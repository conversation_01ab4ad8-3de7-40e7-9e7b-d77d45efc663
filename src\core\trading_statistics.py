"""
交易统计和盈利跟踪系统
实现详细的交易统计，包括成功/失败次数、累积盈利、胜率等指标
"""
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from loguru import logger

from ..models import TradingOrder, OrderStatus, SignalSource


@dataclass
class TradeRecord:
    """交易记录"""
    order_id: str
    symbol: str
    side: str
    entry_price: float
    exit_price: Optional[float]
    amount: float
    leverage: int
    profit: float
    profit_percent: float
    status: str  # 'open', 'closed', 'stopped'
    entry_time: datetime
    exit_time: Optional[datetime]
    duration_minutes: Optional[int]
    exchange_source: str
    signal_source: str
    
    
class TradingStatistics:
    """交易统计器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.stats_file = config.get('stats_file', 'trading_stats.json')
        
        # 交易记录
        self.trade_records: List[TradeRecord] = []
        self.active_positions: Dict[str, TradeRecord] = {}  # symbol -> TradeRecord
        
        # 统计数据
        self.total_trades = 0
        self.successful_trades = 0
        self.failed_trades = 0
        self.total_profit = 0.0
        self.total_invested = 0.0
        self.max_profit = 0.0
        self.max_loss = 0.0
        self.win_rate = 0.0
        
        # 按交易所统计
        self.exchange_stats: Dict[str, Dict[str, Any]] = {}
        
        # 按代币统计
        self.symbol_stats: Dict[str, Dict[str, Any]] = {}
        
        # 加载历史数据
        self.load_statistics()
        
        logger.info("交易统计器初始化完成")
        
    def load_statistics(self):
        """加载历史统计数据"""
        try:
            with open(self.stats_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # 恢复交易记录
            for record_data in data.get('trade_records', []):
                # 转换时间字符串为datetime对象
                record_data['entry_time'] = datetime.fromisoformat(record_data['entry_time'])
                if record_data['exit_time']:
                    record_data['exit_time'] = datetime.fromisoformat(record_data['exit_time'])
                    
                record = TradeRecord(**record_data)
                self.trade_records.append(record)
                
            # 恢复统计数据
            stats_data = data.get('statistics', {})
            self.total_trades = stats_data.get('total_trades', 0)
            self.successful_trades = stats_data.get('successful_trades', 0)
            self.failed_trades = stats_data.get('failed_trades', 0)
            self.total_profit = stats_data.get('total_profit', 0.0)
            self.total_invested = stats_data.get('total_invested', 0.0)
            self.max_profit = stats_data.get('max_profit', 0.0)
            self.max_loss = stats_data.get('max_loss', 0.0)
            self.win_rate = stats_data.get('win_rate', 0.0)
            
            self.exchange_stats = data.get('exchange_stats', {})
            self.symbol_stats = data.get('symbol_stats', {})
            
            logger.info(f"加载历史统计数据: {len(self.trade_records)}条交易记录")
            
        except FileNotFoundError:
            logger.info("未找到历史统计文件，从零开始")
        except Exception as e:
            logger.error(f"加载统计数据失败: {e}")
            
    def save_statistics(self):
        """保存统计数据"""
        try:
            # 准备数据
            trade_records_data = []
            for record in self.trade_records:
                record_dict = asdict(record)
                # 转换datetime对象为字符串
                record_dict['entry_time'] = record.entry_time.isoformat()
                if record.exit_time:
                    record_dict['exit_time'] = record.exit_time.isoformat()
                else:
                    record_dict['exit_time'] = None
                trade_records_data.append(record_dict)
                
            data = {
                'trade_records': trade_records_data,
                'statistics': {
                    'total_trades': self.total_trades,
                    'successful_trades': self.successful_trades,
                    'failed_trades': self.failed_trades,
                    'total_profit': self.total_profit,
                    'total_invested': self.total_invested,
                    'max_profit': self.max_profit,
                    'max_loss': self.max_loss,
                    'win_rate': self.win_rate
                },
                'exchange_stats': self.exchange_stats,
                'symbol_stats': self.symbol_stats,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.debug("统计数据保存成功")
            
        except Exception as e:
            logger.error(f"保存统计数据失败: {e}")
            
    def record_trade_entry(self, order: TradingOrder, signal_source: str = "unknown") -> TradeRecord:
        """记录开仓交易"""
        try:
            # 创建交易记录
            record = TradeRecord(
                order_id=order.order_id or f"order_{datetime.now().timestamp()}",
                symbol=order.symbol,
                side=order.side.value,
                entry_price=order.filled_price or 0.0,
                exit_price=None,
                amount=order.amount,
                leverage=order.leverage,
                profit=0.0,
                profit_percent=0.0,
                status='open',
                entry_time=order.filled_at or datetime.now(),
                exit_time=None,
                duration_minutes=None,
                exchange_source=order.metadata.get('exchange', 'unknown') if hasattr(order, 'metadata') and order.metadata else 'unknown',
                signal_source=signal_source
            )
            
            # 添加到活跃持仓
            self.active_positions[order.symbol] = record
            
            # 更新统计
            self.total_trades += 1
            self.total_invested += order.amount
            
            # 更新交易所统计
            self.update_exchange_stats(record.exchange_source, 'entry', order.amount)
            
            # 更新代币统计
            self.update_symbol_stats(order.symbol, 'entry', order.amount)
            
            logger.info(f"记录开仓交易: {order.symbol} - ${order.amount}")
            return record
            
        except Exception as e:
            logger.error(f"记录开仓交易异常: {e}")
            return None
            
    def record_trade_exit(self, symbol: str, exit_price: float, exit_type: str = "manual") -> Optional[TradeRecord]:
        """记录平仓交易"""
        try:
            if symbol not in self.active_positions:
                logger.warning(f"未找到活跃持仓: {symbol}")
                return None
                
            record = self.active_positions[symbol]
            
            # 更新交易记录
            record.exit_price = exit_price
            record.exit_time = datetime.now()
            record.duration_minutes = int((record.exit_time - record.entry_time).total_seconds() / 60)
            
            # 计算盈利
            if record.side.upper() == "LONG":
                price_change = exit_price - record.entry_price
            else:  # SHORT
                price_change = record.entry_price - exit_price
                
            record.profit = (price_change / record.entry_price) * record.amount * record.leverage
            record.profit_percent = (price_change / record.entry_price) * 100 * record.leverage
            
            # 设置状态
            if exit_type == "stop_loss":
                record.status = 'stopped'
                self.failed_trades += 1
            else:
                record.status = 'closed'
                if record.profit > 0:
                    self.successful_trades += 1
                else:
                    self.failed_trades += 1
                    
            # 更新总盈利
            self.total_profit += record.profit
            
            # 更新最大盈利/亏损
            if record.profit > self.max_profit:
                self.max_profit = record.profit
            if record.profit < self.max_loss:
                self.max_loss = record.profit
                
            # 更新胜率
            if self.total_trades > 0:
                self.win_rate = self.successful_trades / self.total_trades
                
            # 更新交易所统计
            self.update_exchange_stats(record.exchange_source, 'exit', record.profit)
            
            # 更新代币统计
            self.update_symbol_stats(symbol, 'exit', record.profit)
            
            # 移动到历史记录
            self.trade_records.append(record)
            del self.active_positions[symbol]
            
            # 保存统计数据
            self.save_statistics()
            
            logger.info(f"记录平仓交易: {symbol} - 盈利${record.profit:.2f} ({record.profit_percent:.2f}%)")
            return record
            
        except Exception as e:
            logger.error(f"记录平仓交易异常: {e}")
            return None

    def update_exchange_stats(self, exchange: str, action: str, value: float):
        """更新交易所统计"""
        try:
            if exchange not in self.exchange_stats:
                self.exchange_stats[exchange] = {
                    'total_trades': 0,
                    'successful_trades': 0,
                    'failed_trades': 0,
                    'total_profit': 0.0,
                    'total_invested': 0.0,
                    'win_rate': 0.0
                }

            stats = self.exchange_stats[exchange]

            if action == 'entry':
                stats['total_trades'] += 1
                stats['total_invested'] += value
            elif action == 'exit':
                stats['total_profit'] += value
                if value > 0:
                    stats['successful_trades'] += 1
                else:
                    stats['failed_trades'] += 1

                # 更新胜率
                if stats['total_trades'] > 0:
                    stats['win_rate'] = stats['successful_trades'] / stats['total_trades']

        except Exception as e:
            logger.error(f"更新交易所统计异常: {e}")

    def update_symbol_stats(self, symbol: str, action: str, value: float):
        """更新代币统计"""
        try:
            if symbol not in self.symbol_stats:
                self.symbol_stats[symbol] = {
                    'total_trades': 0,
                    'successful_trades': 0,
                    'failed_trades': 0,
                    'total_profit': 0.0,
                    'total_invested': 0.0,
                    'win_rate': 0.0,
                    'avg_profit': 0.0
                }

            stats = self.symbol_stats[symbol]

            if action == 'entry':
                stats['total_trades'] += 1
                stats['total_invested'] += value
            elif action == 'exit':
                stats['total_profit'] += value
                if value > 0:
                    stats['successful_trades'] += 1
                else:
                    stats['failed_trades'] += 1

                # 更新胜率和平均盈利
                if stats['total_trades'] > 0:
                    stats['win_rate'] = stats['successful_trades'] / stats['total_trades']
                    stats['avg_profit'] = stats['total_profit'] / stats['total_trades']

        except Exception as e:
            logger.error(f"更新代币统计异常: {e}")

    def calculate_current_profit(self, symbol: str, current_price: float) -> Optional[Dict[str, float]]:
        """计算当前持仓盈利"""
        try:
            if symbol not in self.active_positions:
                return None

            record = self.active_positions[symbol]

            # 计算当前盈利
            if record.side.upper() == "LONG":
                price_change = current_price - record.entry_price
            else:  # SHORT
                price_change = record.entry_price - current_price

            current_profit = (price_change / record.entry_price) * record.amount * record.leverage
            current_profit_percent = (price_change / record.entry_price) * 100 * record.leverage

            return {
                'profit': current_profit,
                'profit_percent': current_profit_percent,
                'entry_price': record.entry_price,
                'current_price': current_price,
                'duration_minutes': int((datetime.now() - record.entry_time).total_seconds() / 60)
            }

        except Exception as e:
            logger.error(f"计算当前盈利异常: {e}")
            return None

    def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        try:
            # 基础统计
            roi = (self.total_profit / self.total_invested * 100) if self.total_invested > 0 else 0
            avg_profit_per_trade = self.total_profit / self.total_trades if self.total_trades > 0 else 0

            # 最近24小时统计
            recent_stats = self.get_recent_statistics(hours=24)

            # 按交易所排序
            sorted_exchanges = sorted(
                self.exchange_stats.items(),
                key=lambda x: x[1]['total_profit'],
                reverse=True
            )

            # 按代币排序
            sorted_symbols = sorted(
                self.symbol_stats.items(),
                key=lambda x: x[1]['total_profit'],
                reverse=True
            )

            return {
                'overview': {
                    'total_trades': self.total_trades,
                    'successful_trades': self.successful_trades,
                    'failed_trades': self.failed_trades,
                    'win_rate': self.win_rate,
                    'total_profit': self.total_profit,
                    'total_invested': self.total_invested,
                    'roi_percent': roi,
                    'avg_profit_per_trade': avg_profit_per_trade,
                    'max_profit': self.max_profit,
                    'max_loss': self.max_loss,
                    'active_positions': len(self.active_positions)
                },
                'recent_24h': recent_stats,
                'exchange_performance': dict(sorted_exchanges[:5]),  # 前5个交易所
                'symbol_performance': dict(sorted_symbols[:10]),     # 前10个代币
                'active_positions': {
                    symbol: {
                        'entry_price': record.entry_price,
                        'amount': record.amount,
                        'leverage': record.leverage,
                        'duration_minutes': int((datetime.now() - record.entry_time).total_seconds() / 60),
                        'exchange_source': record.exchange_source
                    }
                    for symbol, record in self.active_positions.items()
                }
            }

        except Exception as e:
            logger.error(f"获取综合统计异常: {e}")
            return {}

    def get_recent_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取最近时间段的统计"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            recent_trades = [
                record for record in self.trade_records
                if record.entry_time > cutoff_time
            ]

            if not recent_trades:
                return {
                    'total_trades': 0,
                    'successful_trades': 0,
                    'failed_trades': 0,
                    'total_profit': 0.0,
                    'win_rate': 0.0
                }

            successful = len([r for r in recent_trades if r.profit > 0])
            failed = len([r for r in recent_trades if r.profit <= 0])
            total_profit = sum(r.profit for r in recent_trades)

            return {
                'total_trades': len(recent_trades),
                'successful_trades': successful,
                'failed_trades': failed,
                'total_profit': total_profit,
                'win_rate': successful / len(recent_trades) if recent_trades else 0
            }

        except Exception as e:
            logger.error(f"获取最近统计异常: {e}")
            return {}

    def cleanup_old_records(self, days: int = 30):
        """清理旧记录"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days)

            # 保留最近的记录
            self.trade_records = [
                record for record in self.trade_records
                if record.entry_time > cutoff_time
            ]

            logger.info(f"清理{days}天前的交易记录，当前记录数: {len(self.trade_records)}")
            self.save_statistics()

        except Exception as e:
            logger.error(f"清理旧记录异常: {e}")

    def add_signal_record(self, signal):
        """添加信号记录（监控模式）"""
        try:
            # 在监控模式下记录信号
            logger.info(f"📊 监控模式记录信号: {signal.symbol} - {signal.source.value}")

            # 可以在这里添加信号统计逻辑
            # 比如记录信号数量、来源分布等

        except Exception as e:
            logger.error(f"记录信号异常: {e}")

    def add_error_record(self, error_message: str):
        """添加错误记录"""
        try:
            logger.error(f"交易错误记录: {error_message}")
            # 可以在这里添加错误统计逻辑

        except Exception as e:
            logger.error(f"记录错误异常: {e}")

#!/usr/bin/env python3
"""
固定交易策略
不再使用差异化策略，所有交易所都使用相同的固定策略
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, List
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

# 延迟导入避免循环依赖
def get_unified_db():
    from ..database import unified_db, TradingHistoryRecord
    return unified_db, TradingHistoryRecord

class PositionStatus(Enum):
    """仓位状态"""
    OPENING = "opening"           # 开仓中
    OPENED = "opened"            # 已开仓
    PARTIAL_CLOSED = "partial_closed"  # 部分平仓
    CLOSED = "closed"            # 已平仓
    MANUAL = "manual"            # 手动管理

@dataclass
class FixedPosition:
    """固定策略仓位"""
    symbol: str
    exchange: str
    announcement_time: datetime
    open_time: datetime
    open_price: float
    quantity: float
    status: PositionStatus
    
    # 仓位管理
    initial_amount: float = 100.0  # 初始金额 100 USDT
    leverage: int = 10             # 10倍杠杆
    remaining_percentage: float = 100.0  # 剩余仓位百分比
    
    # 价格追踪
    peak_price: float = 0.0        # 峰值价格
    current_price: float = 0.0     # 当前价格
    
    # 平仓记录
    first_close_time: Optional[datetime] = None   # 80%平仓时间
    final_close_time: Optional[datetime] = None   # 最终平仓时间
    
    # 多交易所标记
    multiple_exchanges: bool = False  # 是否多个交易所上线

    # 通知会话ID
    session_id: str = ""  # 用于通知的会话ID

class FixedTradingStrategy:
    """固定交易策略"""
    
    def __init__(self, binance_client, contract_manager, config=None):
        self.binance_client = binance_client
        self.contract_manager = contract_manager
        self.config = config or {}
        self.active_positions: Dict[str, FixedPosition] = {}
        self.position_tasks: Dict[str, asyncio.Task] = {}

        # 从配置文件读取策略参数
        strategy_config = self.config.get('fixed_strategy', {})
        self.FIXED_AMOUNT = strategy_config.get('amount', 100.0)
        self.LEVERAGE = strategy_config.get('leverage', 10)
        self.FIRST_CLOSE_TIME = strategy_config.get('first_close_time', 56)
        self.FIRST_CLOSE_PERCENTAGE = strategy_config.get('first_close_percentage', 80)
        self.MONITOR_DURATION = strategy_config.get('monitor_duration', 300)
        self.DRAWDOWN_THRESHOLD = strategy_config.get('drawdown_threshold', 20)

        # 交易路由器 (支持普通交易和跟单交易)
        self.trading_router = None
        self._init_trading_router()

        logger.info("🎯 固定交易策略初始化完成")
        logger.info(f"   固定金额: {self.FIXED_AMOUNT} USDT")
        logger.info(f"   杠杆倍数: {self.LEVERAGE}x")
        logger.info(f"   首次平仓: {self.FIRST_CLOSE_TIME}秒后平仓{self.FIRST_CLOSE_PERCENTAGE}%")
        logger.info(f"   监控时长: {self.MONITOR_DURATION}秒")
        logger.info(f"   回撤阈值: {self.DRAWDOWN_THRESHOLD}%")

    def _init_trading_router(self):
        """初始化交易路由器"""
        try:
            # 延迟导入避免循环依赖
            from .trading_router import TradingRouter
            from .normal_trader import NormalTrader

            # 创建交易路由器
            self.trading_router = TradingRouter(self.config)

            # 创建普通交易器
            normal_trader = NormalTrader(self)

            # 创建跟单交易器 (如果启用)
            copy_trader = None
            if self.config.get('copy_trading', {}).get('enabled', False):
                try:
                    from .copy_trading.copy_trader import CopyTrader
                    copy_trader = CopyTrader(self.config)
                    logger.info("✅ 跟单交易器已启用")
                except Exception as e:
                    logger.warning(f"⚠️ 跟单交易器初始化失败: {e}")

            # 初始化路由器
            asyncio.create_task(self.trading_router.initialize(normal_trader, copy_trader))

            logger.info(f"✅ 交易路由器初始化完成 - 模式: {self.trading_router.current_mode}")

        except Exception as e:
            logger.error(f"❌ 交易路由器初始化失败: {e}")
            self.trading_router = None

    def update_config(self, new_config: dict):
        """更新配置参数"""
        try:
            strategy_config = new_config.get('fixed_strategy', {})

            # 记录参数变化
            old_amount = self.FIXED_AMOUNT
            old_leverage = self.LEVERAGE
            old_close_time = self.FIRST_CLOSE_TIME
            old_close_percentage = self.FIRST_CLOSE_PERCENTAGE
            old_monitor_duration = self.MONITOR_DURATION
            old_drawdown_threshold = self.DRAWDOWN_THRESHOLD

            # 更新参数
            self.FIXED_AMOUNT = strategy_config.get('amount', self.FIXED_AMOUNT)
            self.LEVERAGE = strategy_config.get('leverage', self.LEVERAGE)
            self.FIRST_CLOSE_TIME = strategy_config.get('first_close_time', self.FIRST_CLOSE_TIME)
            self.FIRST_CLOSE_PERCENTAGE = strategy_config.get('first_close_percentage', self.FIRST_CLOSE_PERCENTAGE)
            self.MONITOR_DURATION = strategy_config.get('monitor_duration', self.MONITOR_DURATION)
            self.DRAWDOWN_THRESHOLD = strategy_config.get('drawdown_threshold', self.DRAWDOWN_THRESHOLD)

            # 记录变化
            changes = []
            if old_amount != self.FIXED_AMOUNT:
                changes.append(f"金额: {old_amount} → {self.FIXED_AMOUNT} USDT")
            if old_leverage != self.LEVERAGE:
                changes.append(f"杠杆: {old_leverage} → {self.LEVERAGE}x")
            if old_close_time != self.FIRST_CLOSE_TIME:
                changes.append(f"平仓时间: {old_close_time} → {self.FIRST_CLOSE_TIME}秒")
            if old_close_percentage != self.FIRST_CLOSE_PERCENTAGE:
                changes.append(f"平仓比例: {old_close_percentage} → {self.FIRST_CLOSE_PERCENTAGE}%")
            if old_monitor_duration != self.MONITOR_DURATION:
                changes.append(f"监控时长: {old_monitor_duration} → {self.MONITOR_DURATION}秒")
            if old_drawdown_threshold != self.DRAWDOWN_THRESHOLD:
                changes.append(f"回撤阈值: {old_drawdown_threshold} → {self.DRAWDOWN_THRESHOLD}%")

            if changes:
                logger.info("🔄 固定策略参数已更新:")
                for change in changes:
                    logger.info(f"   • {change}")

            # 更新配置引用
            self.config = new_config

        except Exception as e:
            logger.error(f"❌ 更新策略配置失败: {e}")

    def _adjust_quantity_precision(self, symbol: str, quantity: float) -> float:
        """
        根据不同代币合约调整数量精度
        """
        try:
            # 根据价格范围和代币特性判断精度
            if symbol == "CUSDT":
                # CUSDT 通常要求整数
                quantity = round(quantity, 0)
                return max(quantity, 1)  # 最小为1
            elif symbol.endswith("1000USDT"):
                # 1000倍代币（如1000PEPEUSDT）通常允许更多小数位
                quantity = round(quantity, 1)
                return max(quantity, 0.1)
            elif "USDT" in symbol:
                # 检查当前价格来判断精度
                current_price = self.binance_client.get_symbol_ticker(symbol=symbol)['price']
                price = float(current_price)
                
                if price > 1000:
                    # 高价代币，通常精度较低
                    quantity = round(quantity, 2)
                    return max(quantity, 0.01)
                elif price > 10:
                    # 中等价格代币
                    quantity = round(quantity, 3)
                    return max(quantity, 0.001)
                elif price > 0.01:
                    # 低价代币
                    quantity = round(quantity, 1)
                    return max(quantity, 0.1)
                else:
                    # 极低价代币
                    quantity = round(quantity, 0)
                    return max(quantity, 1)
            else:
                # 默认精度
                quantity = round(quantity, 3)
                return max(quantity, 0.001)
                
        except Exception as e:
            logger.warning(f"调整数量精度时出错: {e}, 使用默认精度")
            quantity = round(quantity, 3)
            return max(quantity, 0.001)

    async def process_listing_signal(self, signal=None, symbol: str = None, exchange: str = None,
                                   announcement_time: datetime = None, multiple_exchanges: bool = False,
                                   notifier=None) -> tuple[bool, str]:
        """
        处理上币信号

        Args:
            signal: 交易信号对象（新参数）
            symbol: 代币符号（兼容旧调用）
            exchange: 交易所
            announcement_time: 公告时间(精确到秒)
            multiple_exchanges: 是否多个交易所上线同一代币
            notifier: 通知器

        Returns:
            tuple[bool, str]: (是否成功开仓, 会话ID)
        """
        try:
            # 兼容旧调用方式
            if signal:
                symbol = signal.symbol
                exchange = signal.exchange
                announcement_time = signal.timestamp

            logger.info(f"🔔 收到上币信号: {symbol} ({exchange}) - {announcement_time}")

            # 1. 快速检查币安合约是否存在 - 专门针对新币优化
            logger.info(f"🔍 检查新币合约: {symbol}")

            # 使用专门的新币快速检查方法
            is_available = await self.contract_manager.check_new_coin_fast(symbol)

            if not is_available:
                logger.warning(f"❌ {symbol}USDT 合约不存在，跳过交易")
                # 发送失败通知
                session_id = ""
                if notifier and signal:
                    session_id = notifier.send_signal_open_notification(signal, 0)
                    if session_id:
                        notifier.send_trading_failed_notification(session_id, "合约不存在或未上线")
                return False, session_id

            logger.info(f"🚀 {symbol}USDT 合约存在！准备开仓")

            # 2. 立即开仓
            position, open_price = await self._open_position(symbol, exchange, announcement_time, multiple_exchanges)

            if position:
                # 发送信号开仓通知
                session_id = ""
                if notifier and signal:
                    session_id = notifier.send_signal_open_notification(signal, open_price)
                    # 将session_id存储到position中，用于后续通知
                    position.session_id = session_id

                # 3. 启动仓位管理任务
                task = asyncio.create_task(self._manage_position(position, notifier))
                self.position_tasks[symbol] = task

                logger.info(f"🚀 {symbol} 开仓成功，启动仓位管理")
                return True, session_id
            else:
                logger.error(f"❌ {symbol} 开仓失败")
                # 发送失败通知
                session_id = ""
                if notifier and signal:
                    session_id = notifier.send_signal_open_notification(signal, 0)
                    if session_id:
                        notifier.send_trading_failed_notification(session_id, "开仓失败")
                return False, session_id

        except Exception as e:
            logger.error(f"❌ 处理上币信号失败: {symbol} - {e}")
            # 发送失败通知
            session_id = ""
            if notifier and signal:
                session_id = notifier.send_signal_open_notification(signal, 0)
                if session_id:
                    notifier.send_trading_failed_notification(session_id, f"处理异常: {str(e)}")
            return False, session_id
    
    async def _open_position(self, symbol: str, exchange: str, announcement_time: datetime,
                           multiple_exchanges: bool) -> tuple[Optional[FixedPosition], float]:
        """开仓 - 优化版本，提升速度"""
        try:
            import asyncio
            import time

            start_time = time.time()
            contract_symbol = f"{symbol}USDT"

            # 并发执行多个操作以提升速度 - 使用异步包装
            async def get_price():
                # 使用线程池执行同步API调用，避免阻塞
                import concurrent.futures
                loop = asyncio.get_event_loop()
                with concurrent.futures.ThreadPoolExecutor() as pool:
                    ticker = await loop.run_in_executor(
                        pool, 
                        lambda: self.binance_client.get_symbol_ticker(symbol=contract_symbol)
                    )
                return float(ticker['price'])

            async def set_leverage():
                import concurrent.futures
                loop = asyncio.get_event_loop()
                with concurrent.futures.ThreadPoolExecutor() as pool:
                    await loop.run_in_executor(
                        pool,
                        lambda: self.binance_client.futures_change_leverage(
                            symbol=contract_symbol,
                            leverage=self.LEVERAGE
                        )
                    )

            async def set_margin_type():
                try:
                    import concurrent.futures
                    loop = asyncio.get_event_loop()
                    with concurrent.futures.ThreadPoolExecutor() as pool:
                        await loop.run_in_executor(
                            pool,
                            lambda: self.binance_client.futures_change_margin_type(
                                symbol=contract_symbol,
                                marginType='ISOLATED'
                            )
                        )
                except Exception as e:
                    # 如果已经是逐仓模式，忽略错误
                    if "No need to change margin type" not in str(e):
                        logger.debug(f"设置逐仓模式异常: {e}")

            # 并发执行价格获取和账户设置
            try:
                price_task = asyncio.create_task(get_price())
                leverage_task = asyncio.create_task(set_leverage())
                margin_task = asyncio.create_task(set_margin_type())

                # 等待价格获取完成（开仓必需）
                current_price = await price_task

                # 计算开仓数量
                quantity = (self.FIXED_AMOUNT * self.LEVERAGE) / current_price
                
                # 根据合约精度要求调整数量精度
                # 使用更智能的精度判断
                quantity = self._adjust_quantity_precision(contract_symbol, quantity)

                # 等待账户设置完成
                await asyncio.gather(leverage_task, margin_task, return_exceptions=True)

                setup_time = (time.time() - start_time) * 1000
                logger.debug(f"账户设置完成: {setup_time:.1f}ms")

            except Exception as e:
                logger.error(f"账户设置异常: {e}")
                # 即使设置失败，也尝试开仓
                current_price = await get_price()
                quantity = (self.FIXED_AMOUNT * self.LEVERAGE) / current_price

            # 开多仓 - 使用市价单确保快速成交，异步化
            order_start = time.time()
            import concurrent.futures
            loop = asyncio.get_event_loop()
            with concurrent.futures.ThreadPoolExecutor() as pool:
                order = await loop.run_in_executor(
                    pool,
                    lambda: self.binance_client.futures_create_order(
                        symbol=contract_symbol,
                        side='BUY',
                        type='MARKET',
                        quantity=quantity,
                        positionSide='LONG'  # 明确指定做多方向
                    )
                )
            order_time = (time.time() - order_start) * 1000
            total_time = (time.time() - start_time) * 1000

            # 创建仓位对象
            position = FixedPosition(
                symbol=symbol,
                exchange=exchange,
                announcement_time=announcement_time,
                open_time=datetime.now(),
                open_price=current_price,
                quantity=quantity,
                status=PositionStatus.OPENED,
                peak_price=current_price,
                current_price=current_price,
                multiple_exchanges=multiple_exchanges
            )

            self.active_positions[symbol] = position

            logger.info(f"🚀 {symbol} 快速开仓成功:")
            logger.info(f"   价格: ${current_price:.6f}")
            logger.info(f"   数量: {quantity:.4f}")
            logger.info(f"   金额: {self.FIXED_AMOUNT} USDT")
            logger.info(f"   杠杆: {self.LEVERAGE}x")
            logger.info(f"   多交易所: {multiple_exchanges}")
            logger.info(f"   ⚡ 开仓耗时: {total_time:.1f}ms (订单: {order_time:.1f}ms)")

            return position, current_price

        except Exception as e:
            total_time = (time.time() - start_time) * 1000
            logger.error(f"❌ 开仓失败: {symbol} - {e} (耗时: {total_time:.1f}ms)")
            return None, 0.0
    
    async def _manage_position(self, position: FixedPosition, notifier=None):
        """管理仓位"""
        try:
            logger.info(f"📊 开始管理仓位: {position.symbol}")
            
            # 等待56秒
            await asyncio.sleep(self.FIRST_CLOSE_TIME)
            
            # 平仓80%
            partial_price = await self._close_partial_position(position, self.FIRST_CLOSE_PERCENTAGE)

            # 更新通知器的部分平仓信息
            if notifier and position.session_id and partial_price:
                notifier.update_session_partial_close(position.session_id, partial_price, self.FIRST_CLOSE_PERCENTAGE)

            # 如果是多交易所上线，转为手动管理
            if position.multiple_exchanges:
                position.status = PositionStatus.MANUAL
                logger.info(f"🔄 {position.symbol} 多交易所上线，转为手动管理")
                # 发送交易完成通知
                if notifier and position.session_id:
                    notifier.send_trading_complete_notification(position.session_id, partial_price or position.current_price, "多交易所上线，转为手动管理")
                    # 发送统计通知
                    notifier.send_statistics_notification()
                return

            # 监控剩余20%仓位
            await self._monitor_remaining_position(position, notifier)
            
        except Exception as e:
            logger.error(f"❌ 仓位管理失败: {position.symbol} - {e}")
    
    async def _close_partial_position(self, position: FixedPosition, percentage: float) -> float:
        """部分平仓"""
        try:
            contract_symbol = f"{position.symbol}USDT"

            # 计算平仓数量
            close_quantity = position.quantity * (percentage / 100)

            # 平仓
            order = self.binance_client.futures_create_order(
                symbol=contract_symbol,
                side='SELL',
                type='MARKET',
                quantity=close_quantity,
                positionSide='LONG'  # 明确指定平多仓
            )

            # 更新仓位状态
            position.remaining_percentage = 100 - percentage
            position.status = PositionStatus.PARTIAL_CLOSED
            position.first_close_time = datetime.now()

            # 获取平仓价格
            close_price = float(order.get('avgPrice', position.current_price))
            profit_percentage = ((close_price - position.open_price) / position.open_price) * 100

            logger.info(f"✅ {position.symbol} 部分平仓 {percentage}%:")
            logger.info(f"   平仓价格: ${close_price:.6f}")
            logger.info(f"   收益率: {profit_percentage:.2f}%")
            logger.info(f"   剩余仓位: {position.remaining_percentage}%")

            return close_price

        except Exception as e:
            logger.error(f"❌ 部分平仓失败: {position.symbol} - {e}")
            return 0.0
    
    async def _monitor_remaining_position(self, position: FixedPosition, notifier=None):
        """监控剩余仓位"""
        try:
            logger.info(f"👁️ 开始监控剩余仓位: {position.symbol} - 跌至开仓价+5%止盈")

            contract_symbol = f"{position.symbol}USDT"
            # 计算止盈价格：开仓价格 + 5%
            stop_profit_price = position.open_price * 1.05

            while position.status != PositionStatus.CLOSED:
                # 获取当前价格
                ticker = self.binance_client.get_symbol_ticker(symbol=contract_symbol)
                current_price = float(ticker['price'])

                # 更新价格追踪
                position.current_price = current_price
                if current_price > position.peak_price:
                    position.peak_price = current_price

                # 检查是否跌到止盈价格（开仓价+5%）
                if current_price <= stop_profit_price:
                    logger.info(f"📈 {position.symbol} 跌至止盈价 ${stop_profit_price:.6f}（开仓价+5%），当前价${current_price:.6f}，执行止盈")
                    await self._close_final_position(position, "开仓价+5%止盈", notifier)
                    return

                # 等待1秒后继续监控
                await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"❌ 监控剩余仓位失败: {position.symbol} - {e}")
            # 发送失败通知
            if notifier and position.session_id:
                notifier.send_trading_failed_notification(position.session_id, f"监控失败: {str(e)}")
                notifier.send_statistics_notification()
    
    async def _close_final_position(self, position: FixedPosition, reason: str, notifier=None):
        """最终平仓"""
        try:
            contract_symbol = f"{position.symbol}USDT"

            # 计算剩余数量
            remaining_quantity = position.quantity * (position.remaining_percentage / 100)

            # 平仓
            order = self.binance_client.futures_create_order(
                symbol=contract_symbol,
                side='SELL',
                type='MARKET',
                quantity=remaining_quantity,
                positionSide='LONG'  # 明确指定平多仓
            )

            # 更新仓位状态
            position.status = PositionStatus.CLOSED
            position.final_close_time = datetime.now()
            position.remaining_percentage = 0

            # 获取平仓价格
            close_price = float(order.get('avgPrice', position.current_price))
            total_profit = ((close_price - position.open_price) / position.open_price) * 100

            logger.info(f"✅ {position.symbol} 最终平仓 ({reason}):")
            logger.info(f"   平仓价格: ${close_price:.6f}")
            logger.info(f"   总收益率: {total_profit:.2f}%")
            logger.info(f"   峰值价格: ${position.peak_price:.6f}")

            # 发送交易完成通知
            if notifier and position.session_id:
                notifier.send_trading_complete_notification(position.session_id, close_price, reason)
                # 发送统计通知
                notifier.send_statistics_notification()

            # 从活跃仓位中移除
            if position.symbol in self.active_positions:
                del self.active_positions[position.symbol]
            
            # 取消管理任务
            if position.symbol in self.position_tasks:
                self.position_tasks[position.symbol].cancel()
                del self.position_tasks[position.symbol]
            
        except Exception as e:
            logger.error(f"❌ 最终平仓失败: {position.symbol} - {e}")
    
    def get_active_positions(self) -> List[FixedPosition]:
        """获取活跃仓位"""
        return list(self.active_positions.values())
    
    async def manual_close_position(self, symbol: str) -> bool:
        """手动平仓"""
        try:
            if symbol not in self.active_positions:
                logger.warning(f"❌ 未找到活跃仓位: {symbol}")
                return False
            
            position = self.active_positions[symbol]
            await self._close_final_position(position, "手动平仓", None)
            
            logger.info(f"✅ {symbol} 手动平仓完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 手动平仓失败: {symbol} - {e}")
            return False

# 策略实例管理器
class FixedStrategyManager:
    """固定策略管理器 - 避免使用全局变量"""
    _instance = None

    def __init__(self):
        self.strategy = None

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def initialize_strategy(self, binance_client, contract_manager, config=None):
        """初始化策略"""
        self.strategy = FixedTradingStrategy(binance_client, contract_manager, config)
        return self.strategy

    def get_strategy(self):
        """获取策略实例"""
        return self.strategy

# 兼容性函数
def initialize_fixed_strategy(binance_client, contract_manager, config=None):
    """初始化固定策略 - 兼容性函数"""
    manager = FixedStrategyManager.get_instance()
    return manager.initialize_strategy(binance_client, contract_manager, config)

def get_fixed_strategy():
    """获取固定策略实例 - 兼容性函数"""
    manager = FixedStrategyManager.get_instance()
    return manager.get_strategy()

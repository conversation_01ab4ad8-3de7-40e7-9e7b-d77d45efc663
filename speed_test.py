#!/usr/bin/env python3
"""
快速开仓延迟测试 - 精确测量从信号到开仓的时间
"""
import sys
import os
import asyncio
import time
from datetime import datetime
sys.path.append(os.path.dirname(__file__))

from src.models import TradingSignal, SignalSource, TokenInfo
from src.core.fixed_signal_processor import FixedSignalProcessor
from src.utils import load_config

async def speed_test():
    """测试从信号到开仓的速度"""
    print("🚀 开仓速度测试...")
    print("=" * 50)
    
    # 记录总开始时间
    total_start = time.time()
    
    # 1. 快速初始化 - 测量初始化时间
    init_start = time.time()
    config = load_config('config.yaml')
    
    # 初始化交易组件
    from binance.client import Client as BinanceClient
    from src.trading.contract_manager import ContractManager
    from src.trading.fixed_strategy import initialize_fixed_strategy
    
    binance_config = config.get('trading', {}).get('binance', {})
    binance_client = BinanceClient(
        api_key=binance_config['api_key'],
        api_secret=binance_config['api_secret'],
        testnet=binance_config.get('testnet', False)
    )
    
    contract_manager = ContractManager(binance_config)
    await contract_manager.initialize(binance_client)
    
    fixed_strategy = initialize_fixed_strategy(binance_client, contract_manager, config)
    signal_processor = FixedSignalProcessor(config)
    
    init_time = (time.time() - init_start) * 1000
    print(f"✅ 初始化完成: {init_time:.1f}ms")
    
    # 2. 创建测试信号 - 测量信号创建时间
    signal_start = time.time()
    
    token_info = TokenInfo(symbol="PEPE", market_cap="5B")
    
    test_signal = TradingSignal(
        source=SignalSource.TELEGRAM,
        symbol="PEPE",
        timestamp=datetime.now(),
        content="BINANCE LISTING: PEPE/USDT perpetual contract will be launched",
        tokens=[token_info],
        exchange="binance",
        metadata={'test': True}
    )
    
    signal_time = (time.time() - signal_start) * 1000
    print(f"✅ 信号创建: {signal_time:.1f}ms")
    
    # 3. 测试合约检查速度
    contract_start = time.time()
    is_available = await contract_manager.check_new_coin_fast("PEPE")
    contract_time = (time.time() - contract_start) * 1000
    print(f"✅ 合约检查: {contract_time:.1f}ms ({'存在' if is_available else '不存在'})")
    
    if not is_available:
        print("❌ PEPE合约不存在，无法进行速度测试")
        return
    
    # 4. 测试开仓速度 - 这是关键路径
    print("\n🎯 开始关键路径测试...")
    trading_start = time.time()
    
    # 直接测试固定策略的开仓速度
    success, session_id = await fixed_strategy.process_listing_signal(
        signal=test_signal,
        exchange="binance",
        announcement_time=test_signal.timestamp,
        multiple_exchanges=False,
        notifier=None  # 不发送通知以减少延迟
    )
    
    trading_time = (time.time() - trading_start) * 1000
    total_time = (time.time() - total_start) * 1000
    
    print(f"\n📊 速度测试结果:")
    print(f"   初始化时间: {init_time:.1f}ms")
    print(f"   信号创建: {signal_time:.1f}ms") 
    print(f"   合约检查: {contract_time:.1f}ms")
    print(f"   交易执行: {trading_time:.1f}ms")
    print(f"   总耗时: {total_time:.1f}ms")
    print(f"   交易状态: {'成功' if success else '失败'}")
    
    # 分析延迟
    if trading_time > 1000:  # 超过1秒
        print(f"\n⚠️  交易执行时间过长 ({trading_time:.1f}ms)")
        print("可能的优化点:")
        print("1. 网络延迟 - 考虑使用更快的网络")
        print("2. API响应时间 - 币安API可能较慢")
        print("3. 并发优化 - 可以并行执行多个API调用")
    elif trading_time > 500:  # 超过500ms
        print(f"\n⚠️  交易执行时间较长 ({trading_time:.1f}ms)")
        print("建议优化并发API调用")
    else:
        print(f"\n✅ 交易执行速度良好 ({trading_time:.1f}ms)")
    
    return success

async def main():
    try:
        await speed_test()
    except Exception as e:
        print(f"❌ 速度测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
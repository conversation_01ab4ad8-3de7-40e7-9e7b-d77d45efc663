#!/usr/bin/env python3
"""
市场数据获取器
支持CoinGecko和CoinMarketCap API获取历史行情数据
"""
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from loguru import logger
import time


class MarketDataFetcher:
    """市场数据获取器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # CoinGecko配置 (免费API)
        self.coingecko_base_url = "https://api.coingecko.com/api/v3"
        self.coingecko_api_key = config.get('data_sources', {}).get('coingecko', {}).get('api_key')
        
        # CoinMarketCap配置 (需要API Key)
        self.cmc_base_url = "https://pro-api.coinmarketcap.com/v1"
        self.cmc_api_key = config.get('data_sources', {}).get('coinmarketcap', {}).get('api_key')
        
        # 请求限制
        self.request_delay = 1.0  # 请求间隔(秒)
        self.last_request_time = 0
        
    async def _rate_limit(self):
        """请求频率限制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.request_delay:
            await asyncio.sleep(self.request_delay - time_since_last)
        self.last_request_time = time.time()
        
    async def get_coingecko_historical_data(self, symbol: str, days: int = 7) -> Optional[Dict]:
        """
        获取CoinGecko历史数据
        
        Args:
            symbol: 代币符号 (如: bitcoin, ethereum)
            days: 历史天数 (1-365)
        """
        try:
            await self._rate_limit()
            
            # CoinGecko使用coin_id而不是symbol
            coin_id = await self._get_coingecko_coin_id(symbol)
            if not coin_id:
                logger.warning(f"未找到 {symbol} 的CoinGecko ID")
                return None
                
            url = f"{self.coingecko_base_url}/coins/{coin_id}/market_chart"
            params = {
                'vs_currency': 'usd',
                'days': days,
                'interval': 'hourly' if days <= 30 else 'daily'
            }
            
            if self.coingecko_api_key:
                params['x_cg_demo_api_key'] = self.coingecko_api_key
                
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # 解析数据
                        prices = data.get('prices', [])
                        volumes = data.get('total_volumes', [])
                        market_caps = data.get('market_caps', [])
                        
                        historical_data = []
                        for i, price_point in enumerate(prices):
                            timestamp, price = price_point
                            volume = volumes[i][1] if i < len(volumes) else 0
                            market_cap = market_caps[i][1] if i < len(market_caps) else 0
                            
                            historical_data.append({
                                'timestamp': datetime.fromtimestamp(timestamp / 1000),
                                'price': price,
                                'volume': volume,
                                'market_cap': market_cap
                            })
                            
                        logger.info(f"✅ CoinGecko获取 {symbol} 历史数据: {len(historical_data)} 个数据点")
                        return {
                            'source': 'coingecko',
                            'symbol': symbol,
                            'coin_id': coin_id,
                            'data': historical_data
                        }
                        
                    else:
                        logger.error(f"❌ CoinGecko API错误: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"❌ 获取CoinGecko历史数据异常: {e}")
            return None
            
    async def get_cmc_historical_data(self, symbol: str, days: int = 7) -> Optional[Dict]:
        """
        获取CoinMarketCap历史数据
        
        Args:
            symbol: 代币符号 (如: BTC, ETH)
            days: 历史天数
        """
        if not self.cmc_api_key:
            logger.warning("❌ CoinMarketCap API Key未配置")
            return None
            
        try:
            await self._rate_limit()
            
            # 获取代币ID
            cmc_id = await self._get_cmc_coin_id(symbol)
            if not cmc_id:
                logger.warning(f"未找到 {symbol} 的CoinMarketCap ID")
                return None
                
            # 计算时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            url = f"{self.cmc_base_url}/cryptocurrency/quotes/historical"
            params = {
                'id': cmc_id,
                'time_start': start_date.strftime('%Y-%m-%dT%H:%M:%S.000Z'),
                'time_end': end_date.strftime('%Y-%m-%dT%H:%M:%S.000Z'),
                'interval': 'hourly' if days <= 30 else 'daily',
                'convert': 'USD'
            }
            
            headers = {
                'X-CMC_PRO_API_KEY': self.cmc_api_key,
                'Accept': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if data.get('status', {}).get('error_code') == 0:
                            quotes = data.get('data', {}).get('quotes', [])
                            
                            historical_data = []
                            for quote in quotes:
                                quote_data = quote.get('quote', {}).get('USD', {})
                                historical_data.append({
                                    'timestamp': datetime.fromisoformat(quote['timestamp'].replace('Z', '+00:00')),
                                    'price': quote_data.get('price', 0),
                                    'volume': quote_data.get('volume_24h', 0),
                                    'market_cap': quote_data.get('market_cap', 0)
                                })
                                
                            logger.info(f"✅ CoinMarketCap获取 {symbol} 历史数据: {len(historical_data)} 个数据点")
                            return {
                                'source': 'coinmarketcap',
                                'symbol': symbol,
                                'cmc_id': cmc_id,
                                'data': historical_data
                            }
                        else:
                            logger.error(f"❌ CoinMarketCap API错误: {data.get('status', {}).get('error_message')}")
                            return None
                            
                    else:
                        logger.error(f"❌ CoinMarketCap HTTP错误: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"❌ 获取CoinMarketCap历史数据异常: {e}")
            return None
            
    async def _get_coingecko_coin_id(self, symbol: str) -> Optional[str]:
        """获取CoinGecko的coin_id"""
        try:
            url = f"{self.coingecko_base_url}/coins/list"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        coins = await response.json()
                        
                        # 查找匹配的coin_id
                        for coin in coins:
                            if coin['symbol'].upper() == symbol.upper():
                                return coin['id']
                                
                        # 如果没找到精确匹配，尝试模糊匹配
                        for coin in coins:
                            if symbol.upper() in coin['name'].upper():
                                return coin['id']
                                
                        return None
                    else:
                        logger.error(f"❌ 获取CoinGecko币种列表失败: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"❌ 获取CoinGecko coin_id异常: {e}")
            return None
            
    async def _get_cmc_coin_id(self, symbol: str) -> Optional[int]:
        """获取CoinMarketCap的coin_id"""
        try:
            url = f"{self.cmc_base_url}/cryptocurrency/map"
            params = {'symbol': symbol.upper()}
            headers = {
                'X-CMC_PRO_API_KEY': self.cmc_api_key,
                'Accept': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if data.get('status', {}).get('error_code') == 0:
                            coins = data.get('data', [])
                            if coins:
                                return coins[0]['id']
                                
                        return None
                    else:
                        logger.error(f"❌ 获取CoinMarketCap币种ID失败: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"❌ 获取CoinMarketCap coin_id异常: {e}")
            return None
            
    async def get_listing_price_data(self, exchange: str, symbol: str, listing_time: datetime) -> Optional[Dict]:
        """
        获取上币时的价格数据
        
        Args:
            exchange: 交易所名称
            symbol: 代币符号
            listing_time: 上币时间
        """
        try:
            # 获取上币前后24小时的数据
            start_time = listing_time - timedelta(hours=12)
            end_time = listing_time + timedelta(hours=12)
            
            # 优先使用CoinGecko (免费)
            coingecko_data = await self.get_coingecko_historical_data(symbol, days=2)
            
            if coingecko_data:
                # 从历史数据中找到最接近上币时间的数据
                listing_data = self._find_closest_price_data(
                    coingecko_data['data'], 
                    listing_time
                )
                
                if listing_data:
                    logger.info(f"✅ 获取 {exchange} {symbol} 上币价格数据")
                    return {
                        'exchange': exchange,
                        'symbol': symbol,
                        'listing_time': listing_time,
                        'listing_price': listing_data['price'],
                        'market_cap': listing_data['market_cap'],
                        'volume': listing_data['volume'],
                        'source': 'coingecko'
                    }
                    
            # 如果CoinGecko失败，尝试CoinMarketCap
            if self.cmc_api_key:
                cmc_data = await self.get_cmc_historical_data(symbol, days=2)
                if cmc_data:
                    listing_data = self._find_closest_price_data(
                        cmc_data['data'], 
                        listing_time
                    )
                    
                    if listing_data:
                        logger.info(f"✅ 获取 {exchange} {symbol} 上币价格数据 (CMC)")
                        return {
                            'exchange': exchange,
                            'symbol': symbol,
                            'listing_time': listing_time,
                            'listing_price': listing_data['price'],
                            'market_cap': listing_data['market_cap'],
                            'volume': listing_data['volume'],
                            'source': 'coinmarketcap'
                        }
                        
            logger.warning(f"⚠️ 无法获取 {exchange} {symbol} 的上币价格数据")
            return None
            
        except Exception as e:
            logger.error(f"❌ 获取上币价格数据异常: {e}")
            return None
            
    def _find_closest_price_data(self, historical_data: List[Dict], target_time: datetime) -> Optional[Dict]:
        """在历史数据中找到最接近目标时间的数据点"""
        if not historical_data:
            return None
            
        closest_data = None
        min_time_diff = float('inf')
        
        for data_point in historical_data:
            time_diff = abs((data_point['timestamp'] - target_time).total_seconds())
            if time_diff < min_time_diff:
                min_time_diff = time_diff
                closest_data = data_point
                
        return closest_data
        
    async def test_api_connections(self):
        """测试API连接"""
        logger.info("🧪 测试市场数据API连接...")
        
        # 测试CoinGecko
        try:
            coingecko_data = await self.get_coingecko_historical_data('bitcoin', days=1)
            if coingecko_data:
                logger.info("✅ CoinGecko API连接正常")
            else:
                logger.warning("⚠️ CoinGecko API连接异常")
        except Exception as e:
            logger.error(f"❌ CoinGecko API测试失败: {e}")
            
        # 测试CoinMarketCap
        if self.cmc_api_key:
            try:
                cmc_data = await self.get_cmc_historical_data('BTC', days=1)
                if cmc_data:
                    logger.info("✅ CoinMarketCap API连接正常")
                else:
                    logger.warning("⚠️ CoinMarketCap API连接异常")
            except Exception as e:
                logger.error(f"❌ CoinMarketCap API测试失败: {e}")
        else:
            logger.info("ℹ️ CoinMarketCap API Key未配置，跳过测试")


async def main():
    """测试函数"""
    config = {
        'data_sources': {
            'coingecko': {
                'api_key': None  # CoinGecko免费版不需要API Key
            },
            'coinmarketcap': {
                'api_key': None  # 需要申请API Key
            }
        }
    }
    
    fetcher = MarketDataFetcher(config)
    
    # 测试API连接
    await fetcher.test_api_connections()
    
    # 测试获取历史数据
    logger.info("\n🧪 测试获取历史数据...")
    
    # 测试CoinGecko
    btc_data = await fetcher.get_coingecko_historical_data('bitcoin', days=1)
    if btc_data:
        logger.info(f"CoinGecko BTC数据点数: {len(btc_data['data'])}")
        
    # 测试获取上币价格
    listing_time = datetime.now() - timedelta(hours=24)
    listing_data = await fetcher.get_listing_price_data('upbit', 'bitcoin', listing_time)
    if listing_data:
        logger.info(f"上币价格数据: ${listing_data['listing_price']:.2f}")


if __name__ == "__main__":
    asyncio.run(main())

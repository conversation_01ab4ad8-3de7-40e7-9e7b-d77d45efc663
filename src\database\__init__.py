"""
统一数据库管理模块
支持所有交易所的数据存储和查询
使用统一数据库，通过exchange字段区分交易所
"""
from .exchange_database_manager import (
    UnifiedDatabaseManager,
    ExchangeDatabaseManager,  # 兼容性别名
    ExchangeData,
    ListingEvent,
    TradingSignalRecord,
    TradingOrderRecord,
    TradingHistoryRecord,
    unified_db,
    exchange_db  # 兼容性别名
)

# 为了兼容性，创建各交易所的别名实例
binance_db = unified_db
coinbase_db = unified_db
upbit_db = unified_db
bithumb_db = unified_db

__all__ = [
    # 新的统一管理器
    'UnifiedDatabaseManager', 'unified_db',
    # 数据模型
    'ExchangeData', 'ListingEvent', 'TradingSignalRecord', 'TradingOrderRecord', 'TradingHistoryRecord',
    # 兼容性导出
    'ExchangeDatabaseManager', 'exchange_db',
    'binance_db', 'coinbase_db', 'upbit_db', 'bithumb_db'
]

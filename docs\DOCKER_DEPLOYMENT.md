# 🐳 Goldbot Docker 部署指南

## 📋 部署概述

本指南将帮助您将Goldbot交易机器人部署到Docker容器中，支持本地和服务器部署。

## 🔧 前置要求

### 系统要求
- Docker 20.10+
- Docker Compose 2.0+
- 至少 2GB RAM
- 至少 5GB 磁盘空间

### 网络要求
- 能够访问币安API (api.binance.com)
- 能够访问Telegram API (api.telegram.org) - 可选
- 开放端口 8080 (Web界面)

## 📱 Telegram会话文件处理

### 方案一：本地生成 + 服务器使用（推荐）

#### 1. 本地生成会话文件
```bash
# 在本地运行（需要能接收验证码）
python setup_telegram_session.py
```

这将生成：
- `telegram_session.session` - 用于所有Telegram功能（监控、数据收集等）

#### 2. 上传到服务器
```bash
# 将会话文件复制到服务器项目目录
scp *.session user@server:/path/to/goldbot/
```

#### 3. 服务器部署
```bash
# 在服务器上运行
./deploy.sh
```

### 方案二：服务器直接生成

如果服务器能够接收验证码（如VPS有图形界面），可以直接在服务器上生成：

```bash
# 在服务器上运行
python setup_telegram_session.py
./deploy.sh
```

### 方案三：跳过Telegram功能

如果不需要Telegram功能，可以直接部署：

```bash
# 直接部署，跳过Telegram功能
./deploy.sh
```

## 🚀 快速部署

### Linux/macOS
```bash
# 1. 克隆项目到服务器
git clone <your-repo> goldbot
cd goldbot

# 2. 配置文件
cp config.yaml.example config.yaml
# 编辑 config.yaml，设置API密钥等

# 3. 生成Telegram会话（可选）
python setup_telegram_session.py

# 4. 一键部署
chmod +x deploy.sh
./deploy.sh
```

### Windows
```batch
REM 1. 下载项目到本地
REM 2. 配置 config.yaml
REM 3. 生成Telegram会话（可选）
python setup_telegram_session.py

REM 4. 一键部署
deploy.bat
```

## 📁 文件结构

```
goldbot/
├── config.yaml                 # 主配置文件
├── docker-compose.yml          # Docker编排文件
├── Dockerfile                  # Docker镜像定义
├── deploy.sh                   # Linux部署脚本
├── deploy.bat                  # Windows部署脚本
├── setup_telegram_session.py   # Telegram会话生成器
├── telegram_session.session    # Telegram会话文件
├── data/                       # 数据目录（自动创建）
├── logs/                       # 日志目录（自动创建）
└── config_backups/             # 配置备份（自动创建）
```

## ⚙️ 配置说明

### 必须配置项

在 `config.yaml` 中配置：

```yaml
# 币安API配置
trading:
  binance:
    api_key: "your_binance_api_key"
    api_secret: "your_binance_api_secret"
    testnet: false

# 管理员密码
web_security:
  enabled: true
  admin_password: "your_secure_password"

# 交易策略
fixed_strategy:
  amount: 100
  leverage: 10
```

### 可选配置项

```yaml
# Telegram配置（如果使用Telegram功能）
telegram:
  api_id: your_api_id
  api_hash: "your_api_hash"
  phone: "+1234567890"

# 飞书通知
notifications:
  feishu:
    enabled: true
    webhook_url: "your_feishu_webhook"
```

## 🔍 部署验证

### 检查服务状态
```bash
docker-compose ps
```

### 查看日志
```bash
# 查看所有日志
docker-compose logs

# 实时查看日志
docker-compose logs -f goldbot

# 查看最近100行日志
docker-compose logs --tail=100 goldbot
```

### 访问Web界面
- 主界面: http://localhost:8080
- 管理员登录: 点击右上角"管理员登录"按钮

## 🛠️ 常用操作

### 启动服务
```bash
docker-compose up -d
```

### 停止服务
```bash
docker-compose down
```

### 重启服务
```bash
docker-compose restart
```

### 更新代码
```bash
git pull
docker-compose build
docker-compose up -d
```

### 备份数据
```bash
# 备份数据库和配置
tar -czf goldbot-backup-$(date +%Y%m%d).tar.gz data/ config.yaml *.session
```

## 🔧 故障排除

### 常见问题

#### 1. 端口冲突
```bash
# 修改 docker-compose.yml 中的端口映射
ports:
  - "8081:8081"  # 改为其他端口
```

#### 2. Telegram连接失败
- 检查网络连接
- 确认API ID和API Hash正确
- 重新生成会话文件

#### 3. 币安API错误
- 检查API密钥是否正确
- 确认API权限设置
- 检查网络连接

#### 4. 内存不足
```bash
# 增加Docker内存限制
docker-compose.yml 中添加:
deploy:
  resources:
    limits:
      memory: 2G
```

### 日志分析

```bash
# 查看错误日志
docker-compose logs goldbot | grep ERROR

# 查看启动日志
docker-compose logs goldbot | grep "启动\|初始化"

# 查看交易日志
docker-compose logs goldbot | grep "交易\|订单"
```

## 📊 监控和维护

### 健康检查
```bash
# 检查服务健康状态
curl http://localhost:8080/api/health
```

### 性能监控
```bash
# 查看容器资源使用
docker stats goldbot-trading
```

### 定期维护
```bash
# 清理Docker镜像
docker system prune -f

# 备份重要数据
./backup.sh  # 如果有备份脚本
```

## 🔐 安全建议

1. **修改默认端口**: 不要使用默认的8080端口
2. **设置强密码**: 管理员密码要足够复杂
3. **限制访问**: 使用防火墙限制访问IP
4. **定期备份**: 定期备份数据和配置
5. **更新系统**: 定期更新Docker和系统

## 📞 技术支持

如果遇到问题，请：

1. 查看日志: `docker-compose logs goldbot`
2. 检查配置: 确认 `config.yaml` 配置正确
3. 重启服务: `docker-compose restart`
4. 查看文档: 阅读本部署指南

---

**🎉 祝您部署成功！**
